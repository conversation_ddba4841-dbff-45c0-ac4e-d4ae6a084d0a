<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688LogisticsApi 类
 * 此类用于处理与阿里巴巴1688平台物流相关的API调用，包括获取运费模板列表、
 * 获取运费模板详情、创建和修改运费模板、获取我的运费模板列表和详情、
 * 获取我的发货地址列表以及商品物流模板更换操作检测等功能。
 */
class Ali1688LogisticsApi extends Ali1688BaseApi
{
    /**
     * 获取运费模板列表
     * 获取运费模板列表。1688有两类特殊运费模板，不在此接口返回：
     * - 不传运费模板表示使用运费说明；
     * - 传入1表示卖家承担运费。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.freightTemplate.getList-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $categoryId 商品类目Id，用于官方运费模板支持的筛选（可选）
     * @param int $addressId 发货地址Id，用于官方运费模板支持的筛选（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.freightTemplate.getList-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.freightTemplate.getList-1/7587553
     */
    public static function getFreightTemplateList(array $Ali1688AccessToken, int $categoryId = 0, int $addressId = 0)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.freightTemplate.getList/';

        // 初始化请求参数
        $requestParams = [
            'webSite' => '1688', // 固定为1688网站
        ];

        if ($categoryId > 0) {
            $requestParams['categoryId'] = $categoryId;
        }

        if ($addressId > 0) {
            $requestParams['addressId'] = $addressId;
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $requestParams,                          // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取物流模板详情
     * 根据物流模版ID获取卖家的物流模板详情。运费模板ID为0表示运费说明，为1表示卖家承担运费。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $templateId 模版ID，运费模板ID为0表示运费说明，为1表示卖家承担运费
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get-1/7587553
     */
    public static function getFreightTemplate(array $Ali1688AccessToken, int $templateId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get/';

        if ($templateId < 0) {
            throw new InvalidArgumentException('templateId 必须为非负整数。');
        }

        // 初始化请求参数
        $requestParams = [
            'templateId' => $templateId,
            'querySubTemplate' => true,
            'queryRate' => true,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $requestParams,                          // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 创建我的运费模板
     * 通过该接口创建新的运费模板。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.myFreightTemplate.create-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $templateData 运费模板数据，具体结构请参考API文档
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.myFreightTemplate.create-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.create-1/7587553
     */
    public static function createFreightTemplate(array $Ali1688AccessToken, array $templateData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.create/';


        if (empty($templateData)) {
            throw new InvalidArgumentException('templateData 是必填参数，且不能为空。');
        }

        //
        $body_params = [
            'mainTemplate' => [],//主运费模板，必填。必填字段：name（模板名称），remark（备注），fromAreaCode（发货区编码），addressCodeText（发货区编码对应文本，以空格分割）
            'expressSubTemplate' => [],//快递子模板基本信息（必填）。必填字段：chargeType（1:按重量计价，1-按件数，2-按体积），serviceChargeType（0-卖家承担运费，1-买家承担运费）
            'expressSubRateList' => [],//快递子模板的费率设置（必填）。第一个设置针对全国的费率，后面的看情况针对个别省份。必填字段见示例。
            'cashSubTemplate' => [],//货到付款子模板基本信息（可不填）。若需要则必填字段：chargeType（1:按重量计价，1-按件数，2-按体积），serviceChargeType（0-卖家承担运费，1-买家承担运费）
            'cashSubRateList' => [],//货到付款子模板的费率设置（若cashSubTemplate为空，则此字段亦无效）。第一个设置针对全国的费率，后面的看情况针对个别省份。必填字段见示例。
        ];

        // 根据API文档，进一步验证 $templateData 的结构
        // 示例：
        // if (!isset($templateData['name']) || empty($templateData['name'])) {
        //     throw new InvalidArgumentException('templateData 必须包含 name 字段且不能为空。');
        // }
        // 添加更多的字段验证...

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $templateData,                           // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 修改我的运费模板
     * 通过该接口修改现有的运费模板。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.myFreightTemplate.update-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $templateData 运费模板数据，具体结构请参考API文档
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.myFreightTemplate.update-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.update-1/7587553
     */
    public static function updateFreightTemplate(array $Ali1688AccessToken, array $templateData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.update/';


        if (empty($templateData)) {
            throw new InvalidArgumentException('templateData 是必填参数，且不能为空。');
        }

        // 根据API文档，进一步验证 $templateData 的结构
        // 示例：
        // if (!isset($templateData['templateId']) || !is_int($templateData['templateId'])) {
        //     throw new InvalidArgumentException('templateData 必须包含有效的 templateId 字段。');
        // }
        // 添加更多的字段验证...

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $templateData,                           // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 获取我的运费模板详情（另一个API）
     * 根据物流模版ID获取卖家的物流模板详情。与 getFreightTemplate 方法类似，但可用于不同的场景。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $templateId 模版ID，运费模板ID为0表示运费说明，为1表示卖家承担运费
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get-1/7587553
     */
    public static function getMyFreightTemplateList(array $Ali1688AccessToken, int $templateId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get/';


        if ($templateId < 0) {
            throw new InvalidArgumentException('templateId 必须为非负整数。');
        }

        // 初始化请求参数
        $requestParams = [
            'templateId' => $templateId,
            'querySubTemplate' => true,
            'queryRate' => true,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $requestParams,                          // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取运费模板详情（仅模板ID查询）
     * 根据物流模版ID获取卖家的物流模板详情，仅传入模板ID。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $templateId 模版ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get-1/7587553
     */
    public static function getMyFreightTemplate(array $Ali1688AccessToken, int $templateId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get/';


        if ($templateId <= 0) {
            throw new InvalidArgumentException('templateId 必须为正整数。');
        }

        // 初始化请求参数
        $requestParams = [
            'templateId' => $templateId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $requestParams,                          // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取我的发货地址列表
     * 获取卖家的发货地址列表。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.mySendGoodsAddress.list.get-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.mySendGoodsAddress.list.get-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.mySendGoodsAddress.list.get-1/7587553
     */
    public static function getMySendGoodsAddressList(array $Ali1688AccessToken)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.mySendGoodsAddress.list.get/';


        // 初始化请求参数（无额外参数）
        $requestParams = [];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $requestParams,                          // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 商品物流模板更换操作检测
     * 官方物流模板，目前只有特定的发货地址支持官方物流模板。通过该接口，在新发商品和编辑商品过程中，
     * 对官方物流模板的支撑情况做预判校验。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.freightTemplate.operate.check-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $addressId 发货地址ID（必填）
     * @param int $categoryId 叶子类目ID（必填）
     * @param int $originTemplateId 原始运费模板Id（可选）
     * @param int $targetTemplateId 目标运费模板Id（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.logistics.freightTemplate.operate.check-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.freightTemplate.operate.check-1/7587553
     */
    public static function checkFreightTemplateOperation(array $Ali1688AccessToken, int $addressId, int $categoryId, int $originTemplateId = 0, int $targetTemplateId = 0)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.logistics/alibaba.logistics.freightTemplate.operate.check/';

        if ($addressId <= 0) {
            throw new InvalidArgumentException('addressId 是必填参数，且必须为正整数。');
        }

        if ($categoryId <= 0) {
            throw new InvalidArgumentException('categoryId 是必填参数，且必须为正整数。');
        }

        if ($originTemplateId < 0) {
            throw new InvalidArgumentException('originTemplateId 必须为非负整数。');
        }

        if ($targetTemplateId < 0) {
            throw new InvalidArgumentException('targetTemplateId 必须为非负整数。');
        }

        // 初始化请求参数
        $requestParams = [
            'addressId' => $addressId,
            'categoryId' => $categoryId,
            'originTemplateId' => $originTemplateId ?? null,
            'targetTemplateId' => $targetTemplateId ?? null,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $requestParams,                          // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
