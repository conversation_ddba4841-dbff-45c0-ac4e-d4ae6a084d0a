<!--
/**
 * 思考管理页面
 *
 * 功能特性：
 * - 企业级思考管理系统
 * - 思考分类和标签管理
 * - 高级搜索和批量操作
 * - 思考内容审核和发布
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/think/think
 * 页面标题：思考管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="think-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索思考标题、作者或关键词'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增思考
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchPublish"
                    type="warning"
                    @click="handleBatchPublish"
                >
                    <i class="fas fa-paper-plane"></i>
                    批量发布 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDelete"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="发布状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in THINK_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="思考类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="技术思考" value="technical"></el-option>
                                <el-option label="管理思考" value="management"></el-option>
                                <el-option label="创新思考" value="innovation"></el-option>
                                <el-option label="战略思考" value="strategy"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="优先级">
                            <el-select v-model="filters.priority" clearable placeholder="选择优先级">
                                <el-option label="全部优先级" value=""></el-option>
                                <el-option label="高" value="high"></el-option>
                                <el-option label="中" value="medium"></el-option>
                                <el-option label="低" value="low"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 思考列表 -->
            <template #default>
                <ThinkListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @publish="handlePublish"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总思考: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已发布: <strong>{{ publishedThinkCount }}</strong>
					</span>
                    <span class="stat-item">
						草稿: <strong>{{ draftThinkCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import ThinkListView from './components/ThinkListView.vue'

export default {
    name: 'AdminThinkThinkIndexPage',
    components: {
        BackendPageListLayout,
        ThinkListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                priority: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            publishedThinkCount: 0,
            draftThinkCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部思考', icon: 'fas fa-list', badge: 0},
                {name: 'published', label: '已发布', icon: 'fas fa-check-circle', badge: 0},
                {name: 'draft', label: '草稿', icon: 'fas fa-edit', badge: 0},
                {name: 'pending', label: '待审核', icon: 'fas fa-clock', badge: 0},
                {name: 'high_priority', label: '高优先级', icon: 'fas fa-exclamation-triangle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 思考状态标签
            THINK_STATUS_LABELS: {
                'published': '已发布',
                'draft': '草稿',
                'pending': '待审核',
                'rejected': '已拒绝',
                'archived': '已归档'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchPublish() {
            return this.selectedRows.some(row => ['draft', 'pending'].includes(row.status))
        },
        canBatchDelete() {
            return this.selectedRows.some(row => ['draft', 'rejected', 'archived'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟思考数据
                const mockData = [
                    {
                        id: 1,
                        title: '关于企业数字化转型的思考',
                        author: '张三',
                        author_email: 'zha***@company.com',
                        type: 'strategy',
                        priority: 'high',
                        content_preview: '在当前数字化浪潮下，企业如何进行有效的数字化转型...',
                        tags: ['数字化', '转型', '战略'],
                        view_count: 156,
                        like_count: 23,
                        status: 'published',
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        title: '技术架构优化的几点建议',
                        author: '李四',
                        author_email: 'li***@company.com',
                        type: 'technical',
                        priority: 'medium',
                        content_preview: '基于当前系统架构的分析，提出以下优化建议...',
                        tags: ['技术', '架构', '优化'],
                        view_count: 89,
                        like_count: 12,
                        status: 'published',
                        created_at: '2024-01-15 11:20:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 3,
                        title: '团队管理的新思路',
                        author: '王五',
                        author_email: 'wan***@company.com',
                        type: 'management',
                        priority: 'medium',
                        content_preview: '在远程办公时代，如何更好地进行团队管理...',
                        tags: ['管理', '团队', '远程'],
                        view_count: 0,
                        like_count: 0,
                        status: 'draft',
                        created_at: '2024-01-15 12:45:00',
                        updated_at: '2024-01-15 12:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 234
                this.publishedThinkCount = 156
                this.draftThinkCount = 45
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.publishedThinkCount
            this.tabOptions[2].badge = this.draftThinkCount
            this.tabOptions[3].badge = 23
            this.tabOptions[4].badge = 10
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                priority: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('跳转到新增思考页面')
            // TODO: 跳转到新增页面
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('思考数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchPublish() {
            const publishableItems = this.selectedRows.filter(row =>
                ['draft', 'pending'].includes(row.status)
            )

            if (publishableItems.length === 0) {
                this.$message.warning('没有可发布的思考')
                return
            }

            this.$confirm(`确定要发布选中的 ${publishableItems.length} 个思考吗？`, '确认批量发布', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功发布 ${publishableItems.length} 个思考`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchDelete() {
            const deletableItems = this.selectedRows.filter(row =>
                ['draft', 'rejected', 'archived'].includes(row.status)
            )

            if (deletableItems.length === 0) {
                this.$message.warning('没有可删除的思考')
                return
            }

            this.$confirm(`确定要删除选中的 ${deletableItems.length} 个思考吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${deletableItems.length} 个思考`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看思考详情: ${row.title}`)
            // TODO: 跳转到详情页面
        },

        handleEdit(row) {
            this.$message.info(`编辑思考: ${row.title}`)
            // TODO: 跳转到编辑页面
        },

        handlePublish(row) {
            this.$message.info(`发布思考: ${row.title}`)
            this.loadData()
        },

        handleStatusChange(row, newStatus) {
            this.$message.success(`思考 ${row.title} 状态已更新为: ${this.THINK_STATUS_LABELS[newStatus]}`)
            this.loadData()
        },

        handleDelete(row) {
            this.$confirm(`确定要删除思考 "${row.title}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`思考 ${row.title} 删除成功`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.think-index-page {
    .advanced-search-form {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;
        align-items: center;

        .stat-item {
            font-size: 12px;
            color: #666;

            strong {
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
