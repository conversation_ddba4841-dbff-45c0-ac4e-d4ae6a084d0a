<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688LogisticsTrackingApi 类
 * 此类用于处理与阿里巴巴1688平台物流跟踪相关的API调用，包括获取物流跟踪信息、
 * 获取物流详情以及在加密场景下获取物流信息等功能。
 */
class Ali1688LogisticsTrackingApi extends Ali1688BaseApi
{
    /**
     * 获取交易订单的物流跟踪信息(卖家视角)
     * 获取卖家的订单的物流跟踪信息。该接口能根据物流单号查看物流单跟踪信息。
     * 由于物流单录入的原因，可能跟踪信息的API查询会有延迟。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.sellerView-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $orderId 订单ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.sellerView-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.getLogisticsTraceInfo.sellerView/7587553
     */
    public static function getLogisticsTraceInfoSellerView(array $Ali1688AccessToken, string $orderId)
    {
        // 验证必填参数
        if (empty($orderId)) {
            throw new InvalidArgumentException('orderId 是必填参数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.getLogisticsTraceInfo.sellerView/';

        // 初始化请求参数
        $params = [
            'orderId' => $orderId, // 订单ID
            'webSite' => '1688',    // 必填参数，根据API文档中的要求
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取交易订单的物流信息(卖家视角)
     * 获取卖家的订单的物流详情。该接口能根据物流单号查看物流详情，包括发件人、收件人、所发货物明细等。
     * 由于物流单录入的原因，可能跟踪信息的API查询会有延迟。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsInfos.sellerView-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $orderId 订单ID
     * @param string $fields 需要返回的字段，目前有:company.name,sender,receiver,sendgood。返回的字段要用英文逗号分隔开（可选）
     * @param string $webSite 是1688业务还是icbu业务，值为 "1688" 或 "alibaba"
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.trade.getLogisticsInfos.sellerView-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.getLogisticsInfos.sellerView/7587553
     */
    public static function getLogisticsInfosSellerView(array $Ali1688AccessToken, string $orderId, string $fields = '', string $webSite = '1688')
    {
        // 验证必填参数
        if (empty($orderId)) {
            throw new InvalidArgumentException('orderId 是必填参数。');
        }

        $validWebSites = ['1688', 'alibaba'];
        if (!in_array($webSite, $validWebSites, true)) {
            throw new InvalidArgumentException('webSite 必须为 "1688" 或 "alibaba"。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.getLogisticsInfos.sellerView/';

        // 初始化请求参数
        $params = [
            'orderId' => $orderId, // 订单ID
            'webSite' => $webSite,  // 站点信息
        ];

        if (!empty($fields)) {
            $params['fields'] = $fields; // 需要返回的字段
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 加密场景获取交易订单的物流信息(卖家视角)
     * 在加密场景下获取卖家的订单的物流详情。该接口能根据物流单号查看物流详情，包括发件人、收件人、所发货物明细等。
     * 由于物流单录入的原因，可能跟踪信息的API查询会有延迟。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.ec.getLogisticsInfos.sellerView-1&aopApiCategory=Logistics_NEW
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret（access_token 可选）
     * @param string $orderId 订单ID
     * @param string $fields 需要返回的字段，目前有:company.name,sender,receiver,sendgood。返回的字段要用英文逗号分隔开（可选）
     * @param string $webSite 是1688业务还是icbu业务，值为 "1688" 或 "alibaba"
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:alibaba.trade.ec.getLogisticsInfos.sellerView-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.ec.getLogisticsInfos.sellerView/7587553
     */
    public static function getEncryptedLogisticsInfosSellerView(array $Ali1688AccessToken, string $orderId, string $fields = '', string $webSite = '1688')
    {
        // 验证必填参数
        if (empty($orderId)) {
            throw new InvalidArgumentException('orderId 是必填参数。');
        }

        $validWebSites = ['1688', 'alibaba'];
        if (!in_array($webSite, $validWebSites, true)) {
            throw new InvalidArgumentException('webSite 必须为 "1688" 或 "alibaba"。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.ec.getLogisticsInfos.sellerView/';

        // 初始化请求参数
        $params = [
            'orderId' => $orderId, // 订单ID
            'webSite' => $webSite,  // 站点信息
        ];

        if (!empty($fields)) {
            $params['fields'] = $fields; // 需要返回的字段
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'] ?? '', // 访问令牌（可选）
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }
}
