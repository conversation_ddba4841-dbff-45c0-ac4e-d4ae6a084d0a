<?php

namespace App\Http\Resources\Admin\Store;

use App\Http\Resources\BaseResource;
use App\Models\CommonBrace\Week;
use App\Models\IndustryBrace\Category;
use App\Models\SiteBrace\SiteMarket;
use App\Services\BaseService;
use Illuminate\Http\Request;

class StoreDemoResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'platform_id' => $this->platform_id ?? null,
            //
            'uuid' => $this->uuid ?? null,
            'name' => $this->name ?? null,
            'nickname' => $this->nickname ?? null,
            'short_name' => $this->short_name ?? null,
            //
            'introduction' => $this->introduction ?? null,
            'content' => $this->content ?? null,
            'promotion_info' => $this->promotion_info ?? null,
            //
            'logo_url' => $this->logo_url ?? null,
            'photo_url' => $this->photo_url ?? null,
            'phone' => $this->phone ?? null,
            'brand_name' => $this->brand_name ?? null,
            //
            'cat' => $this->cat ?? null,
            'copy_cat' => $this->copy_cat ?? null,
            'market' => $this->market ?? null,
            //
            'stall' => $this->stall ?? null,
            'week' => $this->week ?? null,
            //
            'start_time' => $this->start_time ?? null,
            'end_time' => $this->end_time ?? null,
            //
            'mobile' => $this->mobile ?? null,
            'mobile_backup' => $this->mobile_backup ?? null,
            'wechat' => $this->wechat ?? null,
            'email' => $this->email ?? null,
            'qq' => $this->qq ?? null,
            'qq_qun' => $this->qq_qun ?? null,
            //
            'company' => $this->company ?? null,
            'contact_name' => $this->contact_name ?? null,
            //
            'license_merchant_name' => isset($this->info) ? $this->info->license_merchant_name : null,
            'license_legal_representative' => isset($this->info) ? $this->info->license_legal_representative : null,
            //
            'wechat_nickname' => isset($this->user) ? $this->user->nickname : null,
            'wechat_img_url' => isset($this->user) ? $this->user->head_img_url : null,
            //
            'wechat_merchant_name' => isset($this->info) ? $this->info->license_merchant_name : null,
            'wechat_name' => isset($this->info) ? $this->info->license_legal_representative : null,
            //
            'is_edit' => $this->is_edit ?? null,
            'main_state' => $this->main_state ?? null,
            //
            'audit_edit' => $this->audit_edit ?? null,
            'edit_type' => $this->edit_type ?? null,
            //
            'audit_detail' => $this->store->audit_detail ?? null,
            'describe' => $this->store->describe ?? null,
            'reject_reason' => $this->reject_reason ?? null,
            //
            'created_time' => BaseService::serializeDate($this->created_at) ?? null,
            'updated_time' => BaseService::serializeDate($this->updated_at) ?? null,
            //
            'weeks' => Week::select('week_id', 'week_name')->get() ?? null,
            //
            'store_name' => $this->store->name ?? null,
            'store_english_name' => $this->store->english_name ?? null,
            'store_nickname' => $this->store->nickname ?? null,
            'store_shortname' => $this->store->short_name ?? null,
            'store_introduction' => $this->store->introduction ?? null,
            'store_content' => $this->store->content ?? null,
            'store_promotion_info' => $this->store->promotion_info ?? null,
            'store_logo_url' => $this->store->logo_url ?? null,
            'store_photo_url' => $this->store->photo_url ?? null,
            'store_phone' => $this->store->phone ?? null,
            'store_brand_name' => $this->store->brand_name ?? null,
            //
            'store_cat' => $this->store->cat ?? null,
            'store_copy_cat' => $this->store->copy_cat ?? null,
            'store_market' => $this->store->market ?? null,
            //
            'store_stall' => $this->store->stall ?? null,
            'store_week_ids' => $this->store->weeks ?? null,
            'store_start_time' => $this->store->start_time ?? null,
            'store_end_time' => $this->store->end_time ?? null,
            'store_company' => $this->store->company ?? null,
            'store_contact_name' => $this->store->contact_name ?? null,
            //
            'store_mobile' => $this->store->mobile ?? null,
            'store_mobile_backup' => $this->store->mobile_backup ?? null,
            'store_wechat' => $this->store->wechat ?? null,
            'store_email' => $this->store->email ?? null,
            'store_qq' => $this->store->qq ?? null,
            //
            'store_markets' => $this->store->markets ? $this->store->markets()->select('market_name')->pluck('market_name')->toArray() : null,
            'store_categories' => $this->store->categories ? $this->store->categories()->select('category_name')->pluck('category_name')->toArray() : null,
            //
            'markets' => $this->market ? SiteMarket::where('platform_id', '=', config('app.platform_id'))->whereIn('id', $this->market)->select('market_name')->pluck('market_name')->toArray() : null,
            'categories' => $this->cat ? Category::where('platform_id', '=', config('app.platform_id'))->whereIn('id', $this->cat)->select('category_name')->pluck('category_name')->toArray() : null,
        ]);
    }
}
