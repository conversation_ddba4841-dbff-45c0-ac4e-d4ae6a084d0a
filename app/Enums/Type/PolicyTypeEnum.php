<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class ProductPolicyTypeEnum
 * @package App\Enums\Type\Product
 * @method static self default() 默认
 * @method static self independent_price() 产品+运费独立定价
 * @method static self fixed_price() 一口价（产品运费包装全包）
 * @method static self tiered_price() 阶梯价（采购越多价格越低）
 * @method static self negotiated_price() 协议价
 */
final class PolicyTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'independent_price' => 1,
            'fixed_price' => 2,
            'tiered_price' => 3,
            'negotiated_price' => 4,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'independent_price' => '独立定价（产品*数量）',
            'fixed_price' => '一口价（产品运费包装全包）',
            'tiered_price' => '阶梯价（采购越多价格越低）',
            'negotiated_price' => '协议价',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color', // 默认
            1 => 'red',           // 独立定价
            2 => 'orange',        // 一口价
            3 => 'yellow',        // 阶梯价
            4 => 'green',         // 协议价
        ];
    }
}
