<?php

namespace App\Traits\Seller\Store;

use App\Models\UserCenter\User;
use Illuminate\Support\Facades\Auth;

trait HasStoreTrait
{
    /**
     * Get the key for the enum value.
     * @return string|null
     */
    public static function getUserIdByStore($user_id = null): ?User
    {
        //
        $id = Auth::id() ?? request()->user()->id ?? $user_id;
        $user = User::find($id);
        return $user ? $user->store : null;
    }
}
