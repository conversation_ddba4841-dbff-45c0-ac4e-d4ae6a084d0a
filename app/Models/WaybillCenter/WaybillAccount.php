<?php

namespace App\Models\WaybillCenter;

use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\hasMany;
use Illuminate\Database\Eloquent\Relations\hasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class WaybillAccount extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'waybill_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    /**
     * 定义与 Waybill 模型的一对多关系
     * @return HasMany
     */
    public function waybills()
    {
        //
        return $this->hasMany(Waybill::class, 'waybill_account_id', 'id'); // 关联运单表
    }

    /**
     * 定义与 WaybillPrint 模型的关联
     * @return BelongsTo
     */
    public function sizes()
    {
        //
        return $this->belongsTo(WaybillPrint::class, 'print_id', 'id'); // 关联运单打印表
    }

    /**
     * 定义与 WaybillStation 模型的一对多关系
     * @return hasMany
     */
    public function addresses()
    {
        //
        return $this->hasMany(WaybillStation::class, 'waybill_account_id', 'id'); // 关联运单站点
    }

    /**
     * 定义与 WaybillService 模型的一对多关系
     * @return hasMany
     */
    public function services()
    {
        //
        return $this->hasMany(WaybillService::class, 'waybill_account_id', 'id'); // 关联运单服务
    }

    /**
     * 定义与 WaybillBird 模型的一对一关系
     * @return hasOne
     */
    public function waybill_bird()
    {
        //
        return $this->hasOne(WaybillBird::class, 'waybill_account_id', 'id'); // 关联运单鸟表
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->client_ip = request()->ip();
        return $this;
    }
}
