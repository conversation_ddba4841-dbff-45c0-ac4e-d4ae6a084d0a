<?php

namespace App\Notifications\Platform\User\Auth;

use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class AuthEmailVerificationNotification extends Notification implements ShouldQueue
{
    /**
     * Handle the event.
     * @param Registered $event
     * @return void
     */
    public function handle(Registered $event)
    {
        //
        if ($event->user instanceof MustVerifyEmail && !$event->user->hasVerifiedEmail()) {
            //
            $event->user->sendEmailVerificationNotification();
        }
    }
}
