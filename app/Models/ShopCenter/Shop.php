<?php

namespace App\Models\ShopCenter;

use App\Enums\State\ShopStateEnum;
use App\Models\IndustryBrace\Category;
use App\Models\PinCenter\Pin;
use App\Models\ProductCenter\Product;
use App\Models\RiceCenter\Rice;
use App\Models\StoreCenter\Store;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shop extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'shop_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'tags' => 'array',
        'company_offers' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'shops.shop_no' => 10,
            'shops.company_name' => 10,
            'shops.company_address' => 10,
            'shops.shop_code' => 10,
            'shops.shop_enterprise' => 10,
            'shops.shop_name' => 10,
            'shops.shop_domain' => 10,
            'shops.production_service' => 10,
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $shopStatus = [
            'default_count' => ShopStateEnum::default()->value,
            'active_count' => ShopStateEnum::active()->value,
            'discovered_count' => ShopStateEnum::discovered()->value,
            'crawling_count' => ShopStateEnum::crawling()->value,
            'parsing_count' => ShopStateEnum::parsing()->value,
            'biz_count' => ShopStateEnum::biz()->value,
            'match_count' => ShopStateEnum::match()->value,
            'operation_count' => ShopStateEnum::operation()->value,
            'ready_count' => ShopStateEnum::ready()->value,
            'review_count' => ShopStateEnum::review()->value,
            'display_count' => ShopStateEnum::display()->value,
            'featured_count' => ShopStateEnum::featured()->value,
            'manage_count' => ShopStateEnum::manage()->value,
            'expired_count' => ShopStateEnum::expired()->value,
        ];
        //
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('shop_state', $shopStatus);
    }

    /**
     * @return BelongsTo
     */
    public function category()
    {
        //
        return $this->belongsTo(Category::class, 'category_id'); // 定义多对多关系
    }


    /**
     * @return HasMany
     */
    public function pins()
    {
        //
        return $this->hasMany(Pin::class, 'shop_id'); // 定义多对多关系
    }

    /**
     * @return HasMany
     */
    public function rices()
    {
        //
        return $this->hasMany(Rice::class, 'shop_id'); // 定义多对多关系
    }

    /**
     * @return BelongsTo
     */
    public function camp()
    {
        //
        return $this->belongsTo(Category::class, 'camp_id'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function shop_pins()
    {
        //
        return $this->belongsToMany(Pin::class, 'pin_center.pin_by_shop'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function shop_rices()
    {
        //
        return $this->belongsToMany(Rice::class, 'rice_center.rice_by_shop'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function shops()
    {
        //
        return $this->belongsToMany(Shop::class, 'shop_center.shop_by_shop'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function stores()
    {
        //
        return $this->belongsToMany(Store::class, 'shop_center.shop_by_store'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function products()
    {
        //
        return $this->belongsToMany(Product::class, 'shop_center.shop_by_product'); // 定义多对多关系
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
