<x-guest-layout
    :title="'用户登录 - ' . config('app.name')"
    :description="'安全登录到 ' . config('app.name') . ' 企业级工作平台'"
    :keywords="'登录,用户认证,企业平台,工作管理'"
>

    {{-- 企业级登录页面 - 基于 guest.blade.php 布局模板 --}}

    {{-- 页面特定样式 --}}
    @push('styles')
        <style>
            /* 企业级登录表单样式增强 */
            .login-form-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                padding: 2rem;
                width: 100%;
                max-width: 400px;
                margin: 0 auto;
            }

            .login-form-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: #1f2937;
                text-align: center;
                margin-bottom: 1.5rem;
            }

            .login-form-subtitle {
                font-size: 0.875rem;
                color: #6b7280;
                text-align: center;
                margin-bottom: 2rem;
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                font-size: 0.875rem;
                font-weight: 500;
                color: #374151;
                margin-bottom: 0.5rem;
            }

            .form-input {
                display: block;
                width: 100%;
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
                line-height: 1.5;
                color: #1f2937;
                background-color: #ffffff;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                transition: none; /* 企业级无动画 */
            }

            .form-input:focus {
                outline: none;
                border-color: #5247ef;
                box-shadow: 0 0 0 3px rgba(82, 71, 239, 0.1);
            }

            .form-input:disabled {
                background-color: #f9fafb;
                color: #6b7280;
                cursor: not-allowed;
            }

            .form-error {
                margin-top: 0.5rem;
                font-size: 0.75rem;
                color: #ef4444;
            }

            .form-checkbox-group {
                display: flex;
                align-items: center;
                margin-bottom: 1.5rem;
            }

            .form-checkbox {
                width: 1rem;
                height: 1rem;
                color: #5247ef;
                background-color: #ffffff;
                border: 1px solid #d1d5db;
                border-radius: 4px;
                margin-right: 0.5rem;
            }

            .form-checkbox:focus {
                outline: none;
                border-color: #5247ef;
                box-shadow: 0 0 0 3px rgba(82, 71, 239, 0.1);
            }

            .form-checkbox-label {
                font-size: 0.875rem;
                color: #6b7280;
            }

            .form-actions {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .btn-primary {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                padding: 0.75rem 1.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                color: #ffffff;
                background-color: #5247ef;
                border: 1px solid #5247ef;
                border-radius: 6px;
                cursor: pointer;
                transition: none; /* 企业级无动画 */
            }

            .btn-primary:hover:not(:disabled) {
                background-color: #4338ca;
                border-color: #4338ca;
            }

            .btn-primary:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(82, 71, 239, 0.2);
            }

            .btn-primary:disabled {
                background-color: #9ca3af;
                border-color: #9ca3af;
                cursor: not-allowed;
            }

            .forgot-password-link {
                display: block;
                text-align: center;
                font-size: 0.875rem;
                color: #5247ef;
                text-decoration: none;
            }

            .forgot-password-link:hover {
                color: #4338ca;
            }

            .forgot-password-link:focus {
                outline: none;
                color: #4338ca;
            }

            .session-status {
                margin-bottom: 1rem;
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
                color: #065f46;
                background-color: #d1fae5;
                border: 1px solid #a7f3d0;
                border-radius: 6px;
            }

            /* 响应式设计 */
            @media (max-width: 640px) {
                .login-form-container {
                    padding: 1.5rem;
                    margin: 1rem;
                }
            }
        </style>
    @endpush

    {{-- 主要内容区域 --}}
    <div class="login-form-container" id="main-content">
        {{-- 登录表单标题 --}}
        <div class="login-form-title">
            <i class="fas fa-sign-in-alt text-primary-500 mr-2" aria-hidden="true"></i>
            用户登录
        </div>

        <div class="login-form-subtitle">
            欢迎回到 {{ config('app.name', 'WorkHub') }} 企业级工作平台
        </div>

        {{-- 会话状态消息 --}}
        @if (session('status'))
            <div class="session-status" role="alert">
                <i class="fas fa-info-circle mr-2" aria-hidden="true"></i>
                {{ session('status') }}
            </div>
        @endif

        {{-- 企业级登录表单 --}}
        <form method="POST" action="{{ route('login') }}" class="login-form" novalidate>
            @csrf

            {{-- 邮箱地址字段 --}}
            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope text-gray-400 mr-1" aria-hidden="true"></i>
                    邮箱地址
                    <span class="text-red-500" aria-label="必填项">*</span>
                </label>
                <input
                    id="email"
                    name="email"
                    type="email"
                    class="form-input @error('email') border-red-500 @enderror"
                    value="{{ old('email') }}"
                    required
                    autofocus
                    autocomplete="username"
                    placeholder="请输入您的邮箱地址"
                    aria-describedby="email-error"
                >
                @error('email')
                <div id="email-error" class="form-error" role="alert">
                    <i class="fas fa-exclamation-triangle mr-1" aria-hidden="true"></i>
                    {{ $message }}
                </div>
                @enderror
            </div>

            {{-- 密码字段 --}}
            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock text-gray-400 mr-1" aria-hidden="true"></i>
                    登录密码
                    <span class="text-red-500" aria-label="必填项">*</span>
                </label>
                <div class="relative">
                    <input
                        id="password"
                        name="password"
                        type="password"
                        class="form-input @error('password') border-red-500 @enderror"
                        required
                        autocomplete="current-password"
                        placeholder="请输入您的登录密码"
                        aria-describedby="password-error"
                    >
                    <button
                        type="button"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onclick="togglePasswordVisibility()"
                        aria-label="显示/隐藏密码"
                    >
                        <i id="password-toggle-icon" class="fas fa-eye text-gray-400 hover:text-gray-600"
                           aria-hidden="true"></i>
                    </button>
                </div>
                @error('password')
                <div id="password-error" class="form-error" role="alert">
                    <i class="fas fa-exclamation-triangle mr-1" aria-hidden="true"></i>
                    {{ $message }}
                </div>
                @enderror
            </div>

            {{-- 记住我选项 --}}
            <div class="form-checkbox-group">
                <input
                    id="remember_me"
                    name="remember"
                    type="checkbox"
                    class="form-checkbox"
                    {{ old('remember') ? 'checked' : '' }}
                >
                <label for="remember_me" class="form-checkbox-label">
                    记住我的登录状态（7天内免登录）
                </label>
            </div>

            {{-- 表单操作按钮 --}}
            <div class="form-actions">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-sign-in-alt mr-2" aria-hidden="true"></i>
                    立即登录
                </button>

                @if (Route::has('password.request'))
                    <a href="{{ route('password.request') }}" class="forgot-password-link">
                        <i class="fas fa-key mr-1" aria-hidden="true"></i>
                        忘记密码？点击重置
                    </a>
                @endif
            </div>
        </form>

        {{-- 注册链接（如果启用注册功能） --}}
        @if (Route::has('register'))
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    还没有账户？
                    <a href="{{ route('register') }}" class="text-primary-500 hover:text-primary-600 no-focus-outline">
                        <i class="fas fa-user-plus mr-1" aria-hidden="true"></i>
                        立即注册
                    </a>
                </p>
            </div>
        @endif

        {{-- 帮助信息 --}}
        <div class="mt-6 text-center">
            <p class="text-xs text-gray-500">
                <i class="fas fa-shield-alt mr-1" aria-hidden="true"></i>
                您的信息受到企业级安全保护
            </p>
            <div class="mt-2 space-x-4 text-xs">
                <a href="{{ url('/privacy') }}" class="text-gray-500 hover:text-gray-700 no-focus-outline">隐私政策</a>
                <a href="{{ url('/terms') }}" class="text-gray-500 hover:text-gray-700 no-focus-outline">服务条款</a>
                <a href="{{ url('/help') }}" class="text-gray-500 hover:text-gray-700 no-focus-outline">帮助中心</a>
            </div>
        </div>
    </div>

    {{-- 页面特定脚本 --}}
    @push('scripts')
        <script>
            // 企业级登录页面管理器
            class EnterpriseLoginManager {
                constructor() {
                    this.isInitialized = false;
                    this.loginAttempts = 0;
                    this.maxAttempts = 5;
                    this.lockoutTime = 15 * 60 * 1000; // 15分钟

                    this.init();
                }

                init() {
                    if (this.isInitialized) return;

                    // 初始化表单验证
                    this.initFormValidation();

                    // 初始化安全功能
                    this.initSecurityFeatures();

                    // 初始化无障碍支持
                    this.initAccessibilitySupport();

                    // 标记为已初始化
                    this.isInitialized = true;

                    console.log('企业级登录管理器初始化完成');
                }

                initFormValidation() {
                    const form = document.querySelector('.login-form');
                    const emailInput = document.getElementById('email');
                    const passwordInput = document.getElementById('password');

                    if (!form || !emailInput || !passwordInput) return;

                    // 实时验证邮箱格式
                    emailInput.addEventListener('blur', () => {
                        this.validateEmail(emailInput);
                    });

                    // 实时验证密码强度
                    passwordInput.addEventListener('input', () => {
                        this.validatePassword(passwordInput);
                    });

                    // 表单提交验证
                    form.addEventListener('submit', (e) => {
                        if (!this.validateForm(form)) {
                            e.preventDefault();
                        }
                    });
                }

                validateEmail(input) {
                    const email = input.value.trim();
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                    if (!email) {
                        this.showFieldError(input, '请输入邮箱地址');
                        return false;
                    }

                    if (!emailRegex.test(email)) {
                        this.showFieldError(input, '请输入有效的邮箱地址');
                        return false;
                    }

                    this.clearFieldError(input);
                    return true;
                }

                validatePassword(input) {
                    const password = input.value;

                    if (!password) {
                        this.showFieldError(input, '请输入登录密码');
                        return false;
                    }

                    if (password.length < 6) {
                        this.showFieldError(input, '密码长度至少6位');
                        return false;
                    }

                    this.clearFieldError(input);
                    return true;
                }

                validateForm(form) {
                    const emailInput = form.querySelector('#email');
                    const passwordInput = form.querySelector('#password');

                    const isEmailValid = this.validateEmail(emailInput);
                    const isPasswordValid = this.validatePassword(passwordInput);

                    return isEmailValid && isPasswordValid;
                }

                showFieldError(input, message) {
                    // 移除现有错误
                    this.clearFieldError(input);

                    // 添加错误样式
                    input.classList.add('border-red-500');

                    // 创建错误消息
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'form-error';
                    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-1" aria-hidden="true"></i>${message}`;

                    // 插入错误消息
                    input.parentNode.appendChild(errorDiv);
                }

                clearFieldError(input) {
                    // 移除错误样式
                    input.classList.remove('border-red-500');

                    // 移除错误消息
                    const errorDiv = input.parentNode.querySelector('.form-error');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                }

                initSecurityFeatures() {
                    // 防止暴力破解
                    this.checkLoginAttempts();

                    // 监控可疑活动
                    this.monitorSuspiciousActivity();

                    // 自动刷新 CSRF 令牌
                    this.initCSRFRefresh();
                }

                initCSRFRefresh() {
                    // 每10分钟刷新一次 CSRF 令牌
                    setInterval(() => {
                        this.refreshCSRFToken();
                    }, 10 * 60 * 1000);
                }

                async refreshCSRFToken() {
                    try {
                        const response = await fetch('/csrf-token', {
                            method: 'GET',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'Accept': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.csrf_token) {
                                // 更新 meta 标签
                                const metaTag = document.querySelector('meta[name="csrf-token"]');
                                if (metaTag) {
                                    metaTag.setAttribute('content', data.csrf_token);
                                }

                                // 更新表单中的 CSRF 令牌
                                const csrfInput = document.querySelector('input[name="_token"]');
                                if (csrfInput) {
                                    csrfInput.value = data.csrf_token;
                                }

                                // 更新全局配置
                                if (window.AppConfig) {
                                    window.AppConfig.csrfToken = data.csrf_token;
                                }

                                console.log('CSRF 令牌已刷新');
                            }
                        }
                    } catch (error) {
                        console.warn('刷新 CSRF 令牌失败:', error);
                    }
                }

                checkLoginAttempts() {
                    const attempts = localStorage.getItem('loginAttempts');
                    const lastAttempt = localStorage.getItem('lastLoginAttempt');

                    if (attempts && lastAttempt) {
                        const attemptsCount = parseInt(attempts);
                        const lastAttemptTime = parseInt(lastAttempt);
                        const now = Date.now();

                        if (attemptsCount >= this.maxAttempts && (now - lastAttemptTime) < this.lockoutTime) {
                            this.lockForm();
                        }
                    }
                }

                lockForm() {
                    const form = document.querySelector('.login-form');
                    const submitBtn = form.querySelector('.btn-primary');

                    if (form && submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-lock mr-2"></i>账户已锁定，请稍后再试';

                        // 显示锁定消息
                        this.showLockoutMessage();
                    }
                }

                showLockoutMessage() {
                    const container = document.querySelector('.login-form-container');
                    const lockoutDiv = document.createElement('div');
                    lockoutDiv.className = 'session-status';
                    lockoutDiv.style.backgroundColor = '#fef2f2';
                    lockoutDiv.style.borderColor = '#fecaca';
                    lockoutDiv.style.color = '#dc2626';
                    lockoutDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle mr-2"></i>
                由于多次登录失败，您的账户已被临时锁定。请15分钟后再试。
            `;

                    container.insertBefore(lockoutDiv, container.firstChild);
                }

                monitorSuspiciousActivity() {
                    // 监控异常行为
                    let keystrokes = 0;
                    let rapidClicks = 0;

                    document.addEventListener('keydown', () => {
                        keystrokes++;
                        if (keystrokes > 100) {
                            console.warn('检测到异常键盘活动');
                        }
                    });

                    document.addEventListener('click', () => {
                        rapidClicks++;
                        setTimeout(() => rapidClicks--, 1000);
                        if (rapidClicks > 10) {
                            console.warn('检测到异常点击活动');
                        }
                    });
                }

                initAccessibilitySupport() {
                    // 键盘导航支持
                    const form = document.querySelector('.login-form');
                    if (!form) return;

                    const inputs = form.querySelectorAll('input, button, a');
                    inputs.forEach((input, index) => {
                        input.addEventListener('keydown', (e) => {
                            if (e.key === 'Tab') {
                                // Tab 键导航已由浏览器处理
                                return;
                            }

                            if (e.key === 'Enter' && input.type !== 'submit') {
                                e.preventDefault();
                                const nextInput = inputs[index + 1];
                                if (nextInput) {
                                    nextInput.focus();
                                }
                            }
                        });
                    });
                }
            }

            // 密码显示/隐藏切换
            function togglePasswordVisibility() {
                const passwordInput = document.getElementById('password');
                const toggleIcon = document.getElementById('password-toggle-icon');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleIcon.className = 'fas fa-eye-slash text-gray-400 hover:text-gray-600';
                } else {
                    passwordInput.type = 'password';
                    toggleIcon.className = 'fas fa-eye text-gray-400 hover:text-gray-600';
                }
            }

            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function () {
                window.EnterpriseLoginManager = new EnterpriseLoginManager();

                // 自动聚焦到邮箱输入框
                const emailInput = document.getElementById('email');
                if (emailInput && !emailInput.value) {
                    emailInput.focus();
                }
            });

            // 页面卸载前清理
            window.addEventListener('beforeunload', function () {
                if (window.EnterpriseLoginManager) {
                    console.log('登录页面卸载，清理资源');
                }
            });
        </script>
    @endpush

</x-guest-layout>
