<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProducePatternResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid,
            'platform_id' => $this->platform_id,
            'site_id' => $this->site_id,
            'category_id' => $this->category_id,
            'group_id' => $this->group_id,
            'pattern_no' => $this->pattern_no,
            'pattern_name' => $this->pattern_name,
            'pattern_english_name' => $this->pattern_english_name,
            'alias_names' => $this->alias_names,
            'pattern_value' => $this->pattern_value,
            'pattern_english_value' => $this->pattern_english_value,
            'pattern_thumb' => $this->pattern_thumb,
            'pattern_description' => $this->pattern_description,
            'deep' => $this->deep,
            'sort' => $this->sort,
            'is_thumb' => $this->is_thumb,
        ]);
    }
}
