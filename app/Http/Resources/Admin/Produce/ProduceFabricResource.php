<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceFabricResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid,
            'platform_id' => $this->platform_id,
            'site_id' => $this->site_id,
            'category_id' => $this->category_id,
            'group_id' => $this->group_id,
            'fabric_no' => $this->fabric_no,
            'fabric_name' => $this->fabric_name,
            'fabric_english_name' => $this->fabric_english_name,
            'alias_names' => $this->alias_names,
            'fabric_value' => $this->fabric_value,
            'fabric_english_value' => $this->fabric_english_value,
            'fabric_thumb' => $this->fabric_thumb,
            'fabric_description' => $this->fabric_description,
            'deep' => $this->deep,
            'sort' => $this->sort,
            'is_thumb' => $this->is_thumb,
        ]);
    }
}
