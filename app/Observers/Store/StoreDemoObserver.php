<?php

namespace App\Observers\Store;

use App\Models\StoreCenter\StoreDemo;

class StoreDemoObserver
{
    /**
     * 处理 StoreDemo "created" 事件.
     */
    public function created(StoreDemo $storeDemo): void
    {
        //
    }

    /**
     * 处理 StoreDemo "updated" 事件.
     */
    public function updated(StoreDemo $storeDemo): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($storeDemo->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($storeDemo->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * 处理 StoreDemo "deleted" 事件.
     */
    public function deleted(StoreDemo $storeDemo): void
    {
        //
    }

    /**
     * 处理 StoreDemo "restored" 事件.
     */
    public function restored(StoreDemo $storeDemo): void
    {
        //
    }

    /**
     * 处理 StoreDemo "force deleted" 事件.
     */
    public function forceDeleted(StoreDemo $storeDemo): void
    {
        //
    }
}
