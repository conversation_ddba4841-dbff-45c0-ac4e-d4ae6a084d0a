<?php

namespace App\Services\Seller;

use App\Enums\Is\IsDefaultEnum;
use App\Enums\Vest\ConductVestEnum;
use App\Enums\Vest\OwnerVestEnum;
use App\Http\Resources\Seller\Sheet\SheetResource;
use App\Http\Resources\Seller\Ship\ShipResource;
use App\Http\Resources\Seller\Shipment\ShipmentAddressResource;
use App\Http\Resources\Seller\Shipment\ShipmentExpressDetailResource;
use App\Models\PlatformBrace\Sheet;
use App\Models\ShipBrace\Ship;
use App\Models\ShipBrace\ShipReceiveAddress;
use App\Models\ShipBrace\ShipSendAddress;
use App\Models\ShipmentCenter\ShipmentAddress;
use App\Models\ShipmentCenter\ShipmentExpress;
use App\Models\ShipmentCenter\ShipmentFreight;
use App\Models\WaybillCenter\Waybill;
use App\Models\WaybillCenter\WaybillStation;
use App\Traits\System\Controller\SetDefaultTrait;
use Exception;
use Illuminate\Http\Request;

class ShipmentExpressService
{
    // 最大地址数量限制
    use SetDefaultTrait;

    /**
     * 返回创建快递方式时所需的表单数据
     * @param Request $request
     * @return array
     */
    public static function create($seller, Request $request)
    {
        // 获取发货地址信息
        $ShipmentAddress = ShipmentAddress::where('platform_id', config('app.platform_id'))
            ->whereUuid($request->input('shipment_address_uuid', null))
            ->first();

        // 获取省份代码，如果没有则默认为44
        $send_code = $ShipmentAddress->province_code ?? $request->input('send_code', 44);

        // 获取发送地址信息
        $ShipSendAddress = ShipSendAddress::where('platform_id', config('app.platform_id'))
            ->where('code', $send_code)
            ->first();
        // 获取接收地址信息并按接收级别分组
        $shipReceiveAddresses = ShipReceiveAddress::where('platform_id', config('app.platform_id'))
            ->where('send_id', $ShipSendAddress->id)
            ->orderBy('receive_level')
            ->get()
            ->groupBy('receive_level');
        // 获取所有运费规则
        $ships = Ship::where('platform_id', config('app.platform_id'))->where('conduct_vest', ConductVestEnum::Store())->orderBy('id', 'desc')->get();
        // 构建发送地址数组
        $send_address = [];
        foreach ($shipReceiveAddresses as $index => $receive_address) {
            $data = [
                'city' => $receive_address,
                'send_code' => $receive_address[0]['send_code'],
                'send_name' => $receive_address[0]['send_name'],
                'receive_level' => $receive_address[0]['receive_level'],
                'receive_name' => $receive_address[0]['receive_name'],
                'three_g_price' => $receive_address[0]['fix_price'],
                'five_g_price' => $receive_address[0]['fix_price'],
                'fix_price' => $receive_address[0]['fix_price'],
                'continue_price' => $receive_address[0]['continue_price']
            ];
            array_push($send_address, $data);
        }

        // 获取所有有效的运单
        $sheet = Sheet::where('platform_id', config('app.platform_id'))
            ->where('is_state', 1)
            ->get();

        // 获取默认运单
        $defaultSheet = Sheet::where('platform_id', config('app.platform_id'))
            ->where('is_default', IsDefaultEnum::default())
            ->first();

        // 获取用户的所有发货地址
        $ShipmentAddresses = ShipmentAddress::where('platform_id', config('app.platform_id'))
            ->where('province_code', $ShipmentAddress->province_code)
            ->orderBy('is_default', 'desc')
            ->orderBy('id', 'desc')
            ->get();

        // 返回创建快递方式时所需的表单数据
        return [
            'send_address' => $send_address,
            'ship_lists' => ShipResource::collection($ships),
            'sheet_lists' => SheetResource::collection($sheet),
            'send_code' => $send_code,
            'sheet_uuid' => $defaultSheet->uuid ?? null,
            'shipment_address_uuid' => $ShipmentAddress->uuid ?? null,
            'shipment_addresses' => ShipmentAddressResource::collection($ShipmentAddresses)
        ];
    }

    /**
     * 创建新的快递方式
     * @param Request $request
     * @param         $seller
     * @param null $Shipment
     * @param string $type
     * @return bool
     * @throws Exception
     */
    public static function store(Request $request, $seller, $Shipment = null, $type = 'store')
    {
        // 验证请求参数
        $request->validate([
            'waybill_uuid' => ['required'],
            'waybill_station_uuid' => ['required'],
            'freight_form' => ['required'],
        ]);

        // 检查当前平台上的快递方式数量是否已满
        $count = ShipmentExpress::where('platform_id', config('app.platform_id'))->count();
        if ($count >= 15) {
            throw new Exception('快递数量已满:)');
        }

        // 获取运单和运单站点信息
        $waybill = Waybill::where('platform_id', config('app.platform_id'))
            ->whereUuid($request->input('waybill_uuid'))
            ->first();

        $waybillStation = WaybillStation::where('platform_id', config('app.platform_id'))
            ->whereUuid($request->input('waybill_station_uuid'))
            ->first();

        // 获取发货地址信息
        $ShipmentAddress = ShipmentAddress::where('platform_id', config('app.platform_id'))
            ->whereUuid($request->input('shipment_address_uuid'))
            ->first();

        // 获取发送地址信息
        $send_code = $ShipmentAddress->province_code ?? $request->input('send_code', 44);
        $ShipSendAddress = ShipSendAddress::where('platform_id', config('app.platform_id'))
            ->where('code', $send_code)
            ->first();
        //
        $isDefault = $request->input('is_default', IsDefaultEnum::NonDefault());
        // 创建新的快递方式
        $ShipmentExpress = new ShipmentExpress();
        $ShipmentExpress->fill([
            'platform_id' => config('app.platform_id'),
            'site_id' => $ShipSendAddress->site_id ?? null,
            'shipment_address_id' => $ShipmentAddress->id ?? null,
            'region_send_id' => $ShipSendAddress->id ?? null,
            'region_id' => $ShipSendAddress->region_id ?? null,
            'shipment_id' => $Shipment->id ?? null,
            'shipment_type' => $Shipment ? $Shipment->getMorphClass() : null,
            'sheet_id' => $waybill->sheet_id ?? null,
            'waybill_id' => $waybill->id ?? null,
            'waybill_account_id' => $waybill->account_id ?? null,
            'waybill_print_id' => $waybill->print_id ?? null,
            'express_corp_id' => $waybill->express_corp_id ?? null,
            'waybill_station_id' => $waybillStation->id ?? null,
            'cp_code' => $waybill->cp_code ?? null,
            'cp_name' => $waybill->cp_name ?? null,
            'country_code' => $ShipSendAddress->country_code ?? null,
            'country_name' => $ShipSendAddress->country_name ?? null,
            'province_code' => $ShipSendAddress->province_code ?? null,
            'province_name' => $ShipSendAddress->province_name ?? null,
            'region' => $ShipSendAddress->region ?? null,
            'freight_time' => $request->input('freight_time', 0),
            'shipping_type' => $request->input('shipping_type', 0),
            'is_default' => $isDefault,
            'is_check' => 1,
            'client_ip' => request()->ip(),
        ]);

        // 设置快递方式的所有者
        if ($type === 'store') {
            $ShipmentExpress->conduct_vest = OwnerVestEnum::Store();
        } elseif ($type === 'provider') {
            $ShipmentExpress->conduct_vest = OwnerVestEnum::Provider();
        }
        // 保存快递方式
        $seller->shipment_expresses()->save($ShipmentExpress);
        // 如果提供了shipment对象，保存其多态关系
        if ($Shipment) {
            //
            $ShipmentExpress->deliveries()->save($Shipment);
        }
        // 如果设置为默认快递，则更新其他快递为非默认
        $serviceInstance = new self();
        $serviceInstance->setDefaultItem($seller->shipment_expresses(), $ShipmentExpress, $isDefault);
        // 处理运费信息
        self::processFreightForm($request, $ShipmentExpress, $ShipSendAddress, $ShipmentAddress, $waybill);
        return true;
    }

    /**
     * 处理运费表单
     * @param Request $request
     * @param ShipmentExpress $ShipmentExpress
     * @param ShipSendAddress $ShipSendAddress
     * @param ShipmentAddress $ShipmentAddress
     * @param Waybill $waybill
     */
    protected static function processFreightForm(Request $request, ShipmentExpress $ShipmentExpress, ShipSendAddress $ShipSendAddress, ShipmentAddress $ShipmentAddress, Waybill $waybill)
    {
        //
        $freight_form = $request->input('freight_form');
        //
        foreach ($freight_form as $freight) {
            //
            foreach ($freight['city'] as $city) {
                // 获取接收地址信息
                $shipReceiveAddress = ShipReceiveAddress::where('platform_id', config('app.platform_id'))
                    ->find($city['id']);
                // 创建新的运费信息
                $ShipmentFreight = new ShipmentFreight();
                $ShipmentFreight->fill([
                    'platform_id' => config('app.platform_id'),
                    'region_send_id' => $ShipSendAddress->region_id ?? null,
                    'region_receive_id' => $shipReceiveAddress->region_id ?? null,
                    'shipment_express_id' => $ShipmentExpress->id ?? null,
                    'shipment_address_id' => $ShipmentAddress->id ?? null,
                    'sheet_id' => $waybill->sheet_id ?? null,
                    'waybill_id' => $waybill->id ?? null,
                    'waybill_account_id' => $waybill->account_id ?? null,
                    'waybill_print_id' => $waybill->print_id ?? null,
                    'express_corp_id' => $waybill->express_corp_id ?? null,
                    'cp_code' => $waybill->cp_code ?? null,
                    'cp_name' => $waybill->cp_name ?? null,
                    'send_id' => $ShipSendAddress->id,
                    'receive_level' => $city['receive_level'] ?? null,
                    'receive_name' => $city['receive_name'] ?? null,
                    'send_code' => $ShipSendAddress->province_code ?? null,
                    'send_name' => $ShipSendAddress->province_name ?? null,
                    //
                    'province_code' => $shipReceiveAddress->province_code ?? null,
                    'province_name' => $shipReceiveAddress->province_name ?? null,
                    //
                    'freight_time' => $request->input('freight_time', 24),
                    'shipping_type' => $request->input('shipping_type', 0),
                    'valuation_type' => 2,
                    'three_g_price' => $freight['three_g_price'] ?? null,
                    'five_g_price' => $freight['five_g_price'] ?? null,
                    'first_val_price' => $freight['fix_price'] ?? null,
                    'first_val_meter' => 1,
                    'one_kg_price' => $freight['fix_price'] ?? null,
                    'second_val_price' => $freight['continue_price'] ?? null,
                    'second_val_meter' => 1,
                    'two_kg_price' => $freight['fix_price'] + $freight['continue_price'],
                    'three_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 2),
                    'four_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 3),
                    'five_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 4),
                    'ten_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 9),
                    'twenty_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 19),
                    'thirty_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 29),
                    'fifty_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 49),
                    'sixty_kg_price' => $freight['fix_price'] + ($freight['continue_price'] * 59),
                    'client_ip' => request()->ip(),
                ]);
                // 保存运费信息
                $ShipmentFreight->save();
            }
        }
    }

    /**
     * 获取指定快递方式的详细信息
     * @param        $seller
     * @param string $uuid
     * @return array
     */
    public static function edit($seller, string $uuid)
    {
        // 获取指定UUID的快递方式
        $ShipmentExpress = $seller->shipment_expresses()
            ->where('platform_id', config('app.platform_id'))
            ->whereUuid($uuid)
            ->first();
        // 获取省份代码
        $send_code = $ShipmentExpress->province_code ?? null;
        // 获取所有有效的运单
        $sheet = Sheet::where('platform_id', config('app.platform_id'))
            ->where('is_state', 1)
            ->get();
        // 获取所有的运费规则
        $ships = Ship::where('platform_id', config('app.platform_id'))->where('conduct_vest', ConductVestEnum::Store())->orderBy('id', 'desc')->get();
        // 获取用户的所有发货地址
        $ShipmentAddresses = ShipmentAddress::where('platform_id', config('app.platform_id'))
            ->where('province_code', $ShipmentExpress->province_code)
            ->orderBy('is_default', 'desc')
            ->orderBy('id', 'desc')
            ->get();
        // 返回快递方式的详细信息
        return [
            'shipment_express' => new ShipmentExpressDetailResource($ShipmentExpress),
            'send_code' => $send_code,
            'ship_lists' => ShipResource::collection($ships),
            'sheet_lists' => SheetResource::collection($sheet),
            'shipment_addresses' => ShipmentAddressResource::collection($ShipmentAddresses)
        ];
    }

    /**
     * 更新指定快递方式
     * @param Request $request
     * @param         $seller
     * @param string $uuid
     * @return bool
     * @throws Exception
     */
    public static function update(Request $request, $seller, string $uuid)
    {
        // 验证请求参数
        $request->validate([
            'waybill_uuid' => ['required'],
            'waybill_station_uuid' => ['required'],
            'send_code' => [''],
            'freight_form' => ['required'],
        ]);

        // 获取指定UUID的快递方式
        $ShipmentExpress = $seller->shipment_expresses()
            ->where('platform_id', config('app.platform_id'))
            ->whereUuid($uuid)
            ->first();

        // 获取发送地址信息
        $ShipSendAddress = ShipSendAddress::where('platform_id', config('app.platform_id'))
            ->where('code', $request->input('send_code', 44))
            ->first();

        // 获取发货地址信息
        $ShipmentAddress = ShipmentAddress::where('platform_id', config('app.platform_id'))
            ->whereUuid($request->input('shipment_address_uuid'))
            ->first();

        // 获取运单和运单站点信息
        $waybill = Waybill::where('platform_id', config('app.platform_id'))
            ->whereUuid($request->input('waybill_uuid'))
            ->first();

        $waybillStation = WaybillStation::where('platform_id', config('app.platform_id'))
            ->whereUuid($request->input('waybill_station_uuid'))
            ->first();
        //
        $isDefault = $request->input('is_default', IsDefaultEnum::NonDefault());
        // 更新快递方式信息
        $ShipmentExpress->fill([
            'region_send_id' => $ShipSendAddress->id ?? null,
            'region' => $ShipSendAddress->region ?? null,
            'country_code' => $ShipSendAddress->country_code ?? null,
            'country_name' => $ShipSendAddress->country_name ?? null,
            'province_code' => $ShipSendAddress->province_code ?? null,
            'province_name' => $ShipSendAddress->province_name ?? null,
            'shipment_address_id' => $ShipmentAddress->id,
            'sheet_id' => $waybill->sheet_id ?? null,
            'waybill_id' => $waybill->id ?? null,
            'waybill_account_id' => $waybill->account_id ?? null,
            'waybill_station_id' => $waybillStation->id ?? null,
            'waybill_print_id' => $waybill->print_id ?? null,
            'express_corp_id' => $waybill->express_corp_id ?? null,
            'cp_code' => $waybill->cp_code ?? null,
            'cp_name' => $waybill->cp_name ?? null,
            'freight_time' => $request->input('freight_time', 24),
            'shipping_type' => $request->input('shipping_type', 0),
            'is_default' => $isDefault,
            'is_check' => 1,
            'client_ip' => request()->ip(),
        ]);
        // 保存更新后的快递方式
        $ShipmentExpress->save();
        // 如果设置为默认快递，则更新其他快递为非默认
        $serviceInstance = new self();
        $serviceInstance->setDefaultItem($seller->shipment_expresses(), $ShipmentExpress, $isDefault, []);
        // 处理运费信息
        $freight_form = $request->input('freight_form');
        //
        foreach ($freight_form as $freight) {
            $ShipmentFreight = ShipmentFreight::whereUuid($freight['uuid'])->first();
            $ShipmentFreight->shipment_express_id = $ShipmentExpress->id;
            $ShipmentFreight->sheet_id = $waybill->sheet_id;
            $ShipmentFreight->waybill_id = $waybill->id;
            $ShipmentFreight->waybill_account_id = $waybill->account_id;
            $ShipmentFreight->waybill_print_id = $waybill->print_id;
            $ShipmentFreight->express_corp_id = $waybill->express_corp_id;
            $ShipmentFreight->cp_code = $waybill->cp_code ?? null;
            $ShipmentFreight->cp_name = $waybill->cp_name ?? null;
            $ShipmentFreight->send_id = $ShipSendAddress->id;
            $ShipmentFreight->region_send_id = $ShipSendAddress->region_id;
            $ShipmentFreight->send_code = $ShipSendAddress->province_code ?? null;
            $ShipmentFreight->send_name = $ShipSendAddress->province_name ?? null;
            $ShipmentFreight->shipment_address_id = $ShipmentAddress->id;
            $ShipmentFreight->freight_time = $request->input('freight_time', 24);
            $ShipmentFreight->shipping_type = $request->input('shipping_type', 0);
            $ShipmentFreight->valuation_type = 2;
            $ShipmentFreight->three_g_price = $freight['three_g_price'];
            $ShipmentFreight->five_g_price = $freight['five_g_price'];
            $ShipmentFreight->first_val_price = $freight['first_val_price'];
            $ShipmentFreight->first_val_meter = 1;
            $ShipmentFreight->one_kg_price = $freight['first_val_price'];
            $ShipmentFreight->second_val_price = $freight['second_val_price'];
            $ShipmentFreight->second_val_meter = 1;
            $ShipmentFreight->two_kg_price = $freight['first_val_price'] + $freight['second_val_price'];
            $ShipmentFreight->three_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 2);
            $ShipmentFreight->four_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 3);
            $ShipmentFreight->five_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 4);
            $ShipmentFreight->ten_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 9);
            $ShipmentFreight->twenty_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 19);
            $ShipmentFreight->thirty_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 29);
            $ShipmentFreight->fifty_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 49);
            $ShipmentFreight->sixty_kg_price = $freight['first_val_price'] + ($freight['second_val_price'] * 59);
            $ShipmentFreight->client_ip = request()->ip;
            $ShipmentFreight->save();
        }
        //
        return true;
    }

    /**
     * 删除指定快递方式
     * @param        $seller
     * @param string $uuid
     * @return bool
     * @throws Exception
     */
    public static function destroy($seller, string $uuid)
    {
        // 获取指定UUID的发货信息
        $Shipment = $seller->shipments()->whereUuid($uuid)->first();
        //
        if (!$Shipment) {
            //
            throw new Exception('参数不正确:(');
        }
        // 获取快递方式信息
        $ShipmentExpress = $Shipment->delivery;

        if ($ShipmentExpress->is_default == IsDefaultEnum::default()) {
            //
            throw new Exception('默认快递禁止删除:(');
        }
        // 删除快递方式和发货信息
        $ShipmentExpress->delete();
        $Shipment->delete();
        //
        return true;
    }
}
