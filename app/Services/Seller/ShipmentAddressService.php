<?php

namespace App\Services\Seller;

use App\Enums\Is\IsCheckEnum;
use App\Enums\Is\IsDefaultEnum;
use App\Http\Resources\Seller\Shipment\ShipmentAddressDetailResource;
use App\Http\Resources\Seller\Shipment\ShipmentAddressResource;
use App\Models\CommonBrace\Region;
use App\Models\ShipmentCenter\ShipmentAddress;
use App\Models\ShipmentCenter\ShipmentExpress;
use App\Models\ShipmentCenter\ShipmentLattice;
use App\Traits\System\Controller\SetDefaultTrait;
use Exception;
use Illuminate\Http\Request;

class ShipmentAddressService
{
    // 最大地址数量限制
    use SetDefaultTrait;

    const MAX_ADDRESSES = 15;

    /**
     * 获取用户的所有地址列表
     * @param $seller
     * @throws Exception
     */
    public static function getAll($seller)
    {
        // 查询并排序用户的所有地址
        $shipmentAddresses = $seller->shipment_address()
            ->where('platform_id', '=', config('app.platform_id'))
            ->orderBy('is_default', 'desc')
            ->orderBy('id', 'desc')
            ->get();
        // 使用资源类格式化地址数据
        $shipmentAddressesResource = ShipmentAddressResource::collection($shipmentAddresses)->resource;
        // 返回地址列表
        return $shipmentAddressesResource;
    }

    /**
     * 创建新的地址
     * @param Request $request
     * @param         $seller
     * @throws Exception
     */
    public static function store(Request $request, $seller)
    {
        // 验证请求数据
        self::validateAddress($request);
        // 获取请求中的地址编码和默认标识
        $code = $request->input('code', '');
        $isDefault = $request->input('is_default', IsDefaultEnum::NonDefault());
        // 获取用户现有地址数量
        $count = $seller->shipment_address()->where('platform_id', '=', config('app.platform_id'))->count();
        // 检查地址数量是否已达到上限
        if ($count >= self::MAX_ADDRESSES) {
            //
            throw new Exception('地址已满:)');
        }
        // 创建新的地址实例
        $shipmentAddress = new ShipmentAddress();
        // 填充地址信息
        self::fillShipmentAddress($shipmentAddress, $request, $code);
        $shipmentAddress->platform_id = config('app.platform_id');
        $shipmentAddress->is_default = $isDefault;
        $shipmentAddress->client_ip = request()->ip;
        // 保存地址到用户地址列表中
        $seller->shipment_address()->save($shipmentAddress);
        //
        // 如果是第一个地址或者设置为默认地址，则更新默认地址
        $serviceInstance = new self();
        $filters = ['model_type' => get_class($seller), 'model_id' => $seller->id];
        $serviceInstance->setDefaultItem($seller->shipment_address(), $shipmentAddress, $isDefault, $filters);
        // 返回地址列表
        return true;
    }

    /**
     * 验证地址信息
     * @param Request $request
     * @return array
     */
    protected static function validateAddress(Request $request)
    {
        //
        return $request->validate([
            'contact_name' => 'required',   // 联系人姓名，必填
            'contact_phone' => 'required',   // 联系电话，必填
            'contact_extension' => '',           // 联系人分机号，可选
            'province_name' => 'required',   // 省名称，必填
            'city_name' => 'required',   // 市名称，必填
            'area_name' => '',           // 区名称，可选
            'detail_info' => 'required',   // 详细地址信息，必填
            'national_code' => '',           // 国家代码，可选
            'address_type' => 'required',   // 标签类型，必填
            'pickup_address' => '',           // 自提地址，可选
            'after_address' => '',           // 售后地址，可选
            'send_address' => '',           // 发货地址，可选
            'latitude' => '',           // 纬度，可选
            'longitude' => '',           // 经度，可选
        ]);
    }

    /**
     * 填充地址信息
     * @param ShipmentAddress $shipmentAddress
     * @param Request $request
     * @param string $code
     */
    protected static function fillShipmentAddress(ShipmentAddress $shipmentAddress, Request $request, string $code)
    {
        $shipmentAddress->contact_name = $request->input('contact_name', '');
        $shipmentAddress->contact_phone = $request->input('contact_phone', '');

        // 根据区域码查找对应的区域信息
        $region = Region::firstWhere('code', $code);

        if (!empty($region)) {
            $shipmentAddress->region_id = $region->id;
            $shipmentAddress->country_code = $region->country_code;
            $shipmentAddress->country_name = $region->country_name;
            $shipmentAddress->province_code = $region->province_code;
            $shipmentAddress->province_name = $request->input('province_name', $region->province_name);
            $shipmentAddress->city_code = $region->city_code;
            $shipmentAddress->city_name = $request->input('city_name', $region->city_name);
            $shipmentAddress->area_code = $region->code;
            $shipmentAddress->area_name = $request->input('area_name', $region->name);
        } else {
            // 如果找不到对应的区域信息，设置为null
            $shipmentAddress->country_code = null;
            $shipmentAddress->province_code = null;
            $shipmentAddress->province_name = $request->input('province_name', null);
            $shipmentAddress->city_code = null;
            $shipmentAddress->city_name = $request->input('city_name', null);
            $shipmentAddress->area_code = null;
            $shipmentAddress->area_name = $request->input('area_name', null);
        }

        $shipmentAddress->detail_info = $request->input('detail_info', null);
        $shipmentAddress->longitude = $request->input('longitude', null);
        $shipmentAddress->latitude = $request->input('latitude', null);
        $shipmentAddress->contact_extension = $request->input('contact_extension', null);
        $shipmentAddress->house_number = $request->input('house_number', null);
        $shipmentAddress->address_type = $request->input('address_type', 0);
        $shipmentAddress->stall = $request->input('stall', '');
        $shipmentAddress->send_address = $request->input('send_address', 0);
        $shipmentAddress->after_address = $request->input('after_address', 0);
        $shipmentAddress->default_after = $request->input('default_after', 0);
        $shipmentAddress->pickup_address = $request->input('pickup_address', 0);
        $shipmentAddress->city_address = $request->input('city_address', 0);
        $shipmentAddress->is_type = $request->input('is_type', null);
        $shipmentAddress->remark = $request->input('remark', null);
        $shipmentAddress->is_check = $request->input('is_check', IsCheckEnum::Normal());
    }

    /**
     * 获取指定地址的详细信息
     * @param        $seller
     * @param string $uuid
     * @throws Exception
     */
    public static function edit($seller, string $uuid)
    {
        // 查找指定的地址
        $shipmentAddress = $seller->shipment_address()->where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        // 检查地址是否存在
        if (empty($shipmentAddress)) {
            //
            throw new Exception('地址不存在');
        }
        // 返回地址详细信息
        return new ShipmentAddressDetailResource($shipmentAddress);
    }

    /**
     * 更新地址
     * @param Request $request
     * @param         $seller
     * @param string $uuid
     * @throws Exception
     */
    public static function update(Request $request, $seller, string $uuid)
    {
        // 验证请求数据
        self::validateAddress($request);
        // 获取请求中的地址编码和默认标识
        $code = $request->input('code', '');
        $isDefault = $request->input('form.is_default', IsDefaultEnum::NonDefault());
        // 获取用户现有地址数量
        $count = $seller->shipment_address()->where('platform_id', '=', config('app.platform_id'))->count();
        // 检查地址数量是否已达到上限
        if ($count >= self::MAX_ADDRESSES) {
            //
            throw new Exception('地址已满:)');
        }
        // 查找要更新的地址
        $shipmentAddress = $seller->shipment_address()->where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        // 填充地址信息
        self::fillShipmentAddress($shipmentAddress, $request, $code);
        //
        $shipmentAddress->is_default = $isDefault;
        $shipmentAddress->client_ip = request()->ip;
        // 更新地址
        $shipmentAddress->update();
        // 设置默认地址
        $serviceInstance = new self();
        $filters = ['model_type' => get_class($seller), 'model_id' => $seller->id];
        $serviceInstance->setDefaultItem($seller->shipment_address(), $shipmentAddress, $isDefault, $filters);
        // 返回地址列表
        return true;
    }

    /**
     * 删除地址
     * @param        $seller
     * @param string $uuid
     * @throws Exception
     */
    public static function destroy($seller, string $uuid)
    {
        // 查找要删除的地址
        $shipmentAddress = $seller->shipment_address()->where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        // 检查地址是否存在
        if (empty($shipmentAddress)) {
            throw new Exception('参数不正确:(');
        }
        // 检查是否为默认地址
        if ($shipmentAddress->is_default == IsDefaultEnum::default()) {
            throw new Exception('默认地址禁止删除:(');
        }
        // 检查是否有自提跑腿使用此地址
        $ShipmentLattice = ShipmentLattice::where('shipment_address_id', $shipmentAddress->id)->first();
        if (!empty($ShipmentLattice)) {
            throw new Exception('自提跑腿有使用此地址,禁止删除:(');
        }
        // 检查是否有快递运费使用此地址
        $shipmentExpress = ShipmentExpress::where('shipment_address_id', $shipmentAddress->id)->first();
        if (!empty($shipmentExpress)) {
            throw new Exception('快递运费有使用此地址,禁止删除:(');
        }
        // 删除地址
        $shipmentAddress->delete();
        // 返回地址列表
        return true;
    }

    /**
     * 设置默认地址
     * @param $seller
     * @param $shipmentAddress
     * @throws Exception
     */
    protected static function setDefaultAddress($seller, $shipmentAddress)
    {

    }
}
