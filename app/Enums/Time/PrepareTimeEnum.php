<?php

namespace App\Enums\Time;

use App\Enums\BaseEnum;


/**
 * Class ShipmentTimeTypeEnum
 * @package App\Enums\Type\Shipment
 * @method static self default() 默认
 * @method static self StandardExpress() 标准快递
 * @method static self ExpressDelivery() 特快专递
 * @method static self SameDayDelivery() 当日达
 * @method static self HalfDayDelivery() 半日达
 * @method static self RegionalPickup() 区域自提
 * @method static self RegionalDelivery() 区域送货
 * @method static self RegionalFreight() 区域货运
 * @method static self RegionalSpecialLine() 区域专线
 * @method static self RegionalConsolidation() 区域集运
 * @method static self ExpressSpeed() 光速达
 * @method static self FastSpeed() 飞速达
 * @method static self QuickSpeed() 快速达
 * @method static self SameCityHalfDayDelivery() 同城半日达
 * @method static self SameCityNextDayDelivery() 同城次日达
 * @method static self CourierHelpDelivery() 跑腿-帮送
 * @method static self PrepositionTimelyDelivery() 前置及时达
 * @method static self PrepositionHourlyDelivery() 前置小时达
 * @method static self PrepositionCentralizedDelivery() 前置集中送
 * @method static self PrepositionStandardSameDayDelivery() 前置标快当日达
 * @method static self StandardLogistics() 标准物流
 * @method static self StandardLessThanTruckload() 标准零担
 * @method static self HeavyRicePackage() 重货包裹
 * @method static self PrepositionWarehousing() 前置入仓
 * @method static self FillCabinExpress() 填舱标快
 * @method static self FillCabinElectronicExpress() 填舱电标
 * @method static self DirectCarDelivery() 整车直达
 * @method static self InternationalExpress() 国际快递
 * @method static self InternationalSmallParcel() 国际小包邮政
 * @method static self InternationalLargeItem() 国际大件
 * @method static self InternationalSpecialOffer() 国际特惠
 * @method static self InternationalWarehousingFBA() 国际入仓FBA
 * @method static self InternationalBookingAirFreight() 国际订舱空运
 * @method static self InternationalFullContainerLoadSeaFreight() 国际整柜海运
 * @method static self IndustryDistribution() 同业分销
 * @method static self ColdChain() 冷链
 * @method static self BusinessCourtesy() 商务礼遇
 */
final class PrepareTimeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'StandardExpress' => 1,
            'ExpressDelivery' => 2,
            'SameDayDelivery' => 3,
            'HalfDayDelivery' => 4,
            'RegionalPickup' => 11,
            'RegionalDelivery' => 12,
            'RegionalFreight' => 13,
            'RegionalSpecialLine' => 14,
            'RegionalConsolidation' => 15,
            'ExpressSpeed' => 21,
            'FastSpeed' => 22,
            'QuickSpeed' => 23,
            'SameCityHalfDayDelivery' => 24,
            'SameCityNextDayDelivery' => 25,
            'CourierHelpDelivery' => 26,
            'PrepositionTimelyDelivery' => 31,
            'PrepositionHourlyDelivery' => 32,
            'PrepositionCentralizedDelivery' => 33,
            'PrepositionStandardSameDayDelivery' => 34,
            'StandardLogistics' => 41,
            'StandardLessThanTruckload' => 42,
            'HeavyRicePackage' => 43,
            'PrepositionWarehousing' => 44,
            'FillCabinExpress' => 45,
            'FillCabinElectronicExpress' => 46,
            'DirectCarDelivery' => 47,
            'InternationalExpress' => 51,
            'InternationalSmallParcel' => 52,
            'InternationalLargeItem' => 53,
            'InternationalSpecialOffer' => 54,
            'InternationalWarehousing(FBA)' => 55,
            'InternationalBooking(Air Freight)' => 56,
            'InternationalFullContainerLoad(Sea Freight)' => 57,
            'IndustryDistribution' => 61,
            'ColdChain' => 71,
            'BusinessCourtesy' => 81,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'StandardExpress' => '标准快递',
            'ExpressDelivery' => '特快专递',
            'SameDayDelivery' => '当日达',
            'HalfDayDelivery' => '半日达',
            'RegionalPickup' => '区域自提',
            'RegionalDelivery' => '区域送货',
            'RegionalFreight' => '区域货运',
            'RegionalSpecialLine' => '区域专线',
            'RegionalConsolidation' => '区域集运',
            'ExpressSpeed' => '光速达',
            'FastSpeed' => '飞速达',
            'QuickSpeed' => '快速达',
            'SameCityHalfDayDelivery' => '同城半日达',
            'SameCityNextDayDelivery' => '同城次日达',
            'CourierHelpDelivery' => '跑腿-帮送',
            'PrepositionTimelyDelivery' => '前置及时达',
            'PrepositionHourlyDelivery' => '前置小时达',
            'PrepositionCentralizedDelivery' => '前置集中送',
            'PrepositionStandardSameDayDelivery' => '前置标快当日达',
            'StandardLogistics' => '标准物流',
            'StandardLessThanTruckload' => '标准零担',
            'HeavyRicePackage' => '重货包裹',
            'PrepositionWarehousing' => '前置入仓',
            'FillCabinExpress' => '填舱标快',
            'FillCabinElectronicExpress' => '填舱电标',
            'DirectCarDelivery' => '整车直达',
            'InternationalExpress' => '国际快递',
            'InternationalSmallParcel' => '国际小包(邮政)',
            'InternationalLargeItem' => '国际大件',
            'InternationalSpecialOffer' => '国际特惠',
            'InternationalWarehousing(FBA)' => '国际入仓(FBA)',
            'InternationalBooking(Air Freight)' => '国际订舱(空运)',
            'InternationalFullContainerLoad(Sea Freight)' => '国际整柜(海运)',
            'IndustryDistribution' => '同业分销',
            'ColdChain' => '冷链',
            'BusinessCourtesy' => '商务礼遇',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'default_color',            // default
            1 => 'red',                      // StandardExpress
            2 => 'orange',                   // ExpressDelivery
            3 => 'yellow',                   // SameDayDelivery
            4 => 'green',                    // HalfDayDelivery
            11 => 'purple',                  // RegionalPickup
            12 => 'blue',                    // RegionalDelivery
            13 => 'pink',                    // RegionalFreight
            14 => 'brown',                   // RegionalSpecialLine
            15 => 'cyan',                    // RegionalConsolidation
            21 => 'teal',                    // ExpressSpeed
            22 => 'indigo',                  // FastSpeed
            23 => 'deep-purple',             // QuickSpeed
            24 => 'light-blue',              // SameCityHalfDayDelivery
            25 => 'blue-grey',               // SameCityNextDayDelivery
            26 => 'amber',                   // CourierHelpDelivery
            31 => 'brown',                   // PrepositionTimelyDelivery
            32 => 'grey',                    // PrepositionHourlyDelivery
            33 => 'deep-orange',             // PrepositionCentralizedDelivery
            34 => 'cyan',                    // PrepositionStandardSameDayDelivery
            41 => 'blue-grey',               // StandardLogistics
            42 => 'deep-purple',             // StandardLessThanTruckload
            43 => 'amber',                   // HeavyRicePackage
            44 => 'indigo',                  // PrepositionWarehousing
            45 => 'green',                   // FillCabinExpress
            46 => 'purple',                  // FillCabinElectronicExpress
            47 => 'deep-orange',             // DirectCarDelivery
            51 => 'red',                     // InternationalExpress
            52 => 'orange',                  // InternationalSmallParcel
            53 => 'yellow',                  // InternationalLargeItem
            54 => 'green',                   // InternationalSpecialOffer
            55 => 'purple',                  // InternationalWarehousing(FBA)
            56 => 'blue',                    // InternationalBooking(Air Freight)
            57 => 'pink',                    // InternationalFullContainerLoad(Sea Freight)
            61 => 'brown',                   // IndustryDistribution
            71 => 'cyan',                    // ColdChain
            81 => 'grey',                    // BusinessCourtesy
        ];
    }
}
