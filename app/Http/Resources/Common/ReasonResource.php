<?php

namespace App\Http\Resources\Common;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReasonResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'id' => $this->id ?? null,
            'uuid' => $this->uuid ?? null,
            'reason_code' => $this->reason_code ?? null,
            'reason_name' => $this->reason_name ?? null,
            'reason_category' => $this->reason_category ?? null,
            'reason_description' => $this->reason_description ?? null,
        ]);
    }
}
