<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * WordStateEnum 用于管理商品标题和搜索中的关键词状态。
 * @package App\Enums\Type\User
 * @method static self default() 默认状态
 * @method static self active() 激活状态
 * @method static self inactive() 非激活状态
 * @method static self featured() 特色状态
 * @method static self archived() 归档状态
 * @method static self pending() 待审核状态
 * @method static self expired() 过期状态
 * @method static self suspended() 暂停状态
 * @method static self flagged() 标记状态
 * @method static self invalid() 无效状态
 * @method static self incorrect() 不正确的词汇
 * @method static self forbidden() 禁用词汇
 * @method static self recommended() 推荐状态
 * @method static self priority() 优先状态
 * @method static self popular() 热门状态
 * @method static self temporary() 临时状态
 * @method static self soon() 即将过期状态
 * @method static self approved() 审核通过状态
 * @method static self locked() 锁定状态
 * @method static self fixing() 修复中状态
 * @method static self recycled() 回收状态
 * @method static self trial() 试用状态
 */
final class WordStateEnum extends BaseEnum
{
    use KeyedEnumTrait;

    // 使用KeyedEnumTrait，提供按键值查找的功能
    use ColorEnumTrait;

    /**
     * 定义关键词状态的值
     * 返回一个数组，其中键是状态名称，值是该状态对应的数字。
     * 数字值可以用于存储和数据库操作。
     * @return array 状态值数组
     */
    protected static function values(): array
    {
        //
        return [
            'default' => 0,  // 默认状态
            'active' => 1,  // 激活状态
            'inactive' => 2,  // 非激活状态
            'recommended' => 3,  // 推荐状态
            'priority' => 4,  // 优先状态
            'popular' => 5,  // 热门状态
            'featured' => 6,  // 特色状态
            'temporary' => 7,  // 临时状态
            'soon' => 8, // 即将过期状态
            'approved' => 9,  // 审核通过状态
            'pending' => 10, // 待审核状态
            'expired' => 11, // 过期状态
            'suspended' => 12, // 暂停状态
            'flagged' => 13, // 标记状态
            'locked' => 14, // 锁定状态
            'fixing' => 15, // 修复中状态
            'recycled' => 16, // 回收状态
            'trial' => 17, // 试用状态
            'invalid' => 18, // 无效状态
            'incorrect' => 19, // 不正确的词汇
            'forbidden' => 20, // 禁用词汇
        ];
    }

    /**
     * 定义状态标签
     * 返回一个数组，其中键是状态名称，值是该状态的中文描述。
     * 标签用于界面显示，帮助用户理解每个状态的含义。
     * @return array 状态标签数组
     */
    protected static function labels(): array
    {
        return [
            'default' => '默认状态',
            'active' => '激活状态',
            'inactive' => '非激活状态',
            'recommended' => '推荐状态',
            'priority' => '优先状态',
            'popular' => '热门状态',
            'featured' => '特色状态',
            'temporary' => '临时状态',
            'soon' => '即将过期状态',
            'approved' => '审核通过状态',
            'pending' => '待审核状态',
            'expired' => '过期状态',
            'suspended' => '暂停状态',
            'flagged' => '标记状态',
            'locked' => '锁定状态',
            'fixing' => '修复中状态',
            'recycled' => '回收状态',
            'trial' => '试用状态',
            'invalid' => '无效状态',
            'incorrect' => '不正确的词汇',
            'forbidden' => '禁用词汇',
        ];
    }

    /**
     * 定义状态的颜色映射
     * 返回一个数组，其中数字表示状态的值，颜色代表该状态的视觉标识。
     * 颜色可用于UI中，以不同颜色标识不同状态。
     * @return array 状态颜色映射数组
     */
    protected static function colors(): array
    {
        return [
            0 => 'gray',     // 默认状态，使用灰色表示
            1 => 'green',    // 激活状态，使用绿色表示
            2 => 'yellow',   // 非激活状态，使用黄色表示
            3 => 'blue',     // 推荐状态，使用蓝色表示
            4 => 'green',    // 优先状态，使用绿色表示
            5 => 'orange',   // 热门状态，使用橙色表示
            6 => 'blue',     // 特色状态，使用蓝色表示
            7 => 'gray',     // 临时状态，使用灰色表示
            8 => 'yellow',   // 即将过期状态，使用黄色表示
            9 => 'green',    // 审核通过状态，使用绿色表示
            10 => 'orange',   // 待审核状态，使用橙色表示
            11 => 'red',      // 过期状态，使用红色表示
            12 => 'purple',   // 暂停状态，使用紫色表示
            13 => 'pink',     // 标记状态，使用粉色表示
            14 => 'purple',   // 锁定状态，使用紫色表示
            15 => 'orange',   // 修复中状态，使用橙色表示
            16 => 'gray',     // 回收状态，使用灰色表示
            17 => 'blue',     // 试用状态，使用蓝色表示
            18 => 'gray',     // 无效状态，使用灰色表示
            19 => 'yellow',   // 不正确的词汇，使用黄色表示
            20 => 'red',      // 禁用词汇，使用红色表示
        ];
    }
}
