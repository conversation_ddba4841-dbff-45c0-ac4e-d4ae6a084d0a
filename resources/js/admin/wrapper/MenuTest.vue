<template>
    <div class="menu-test">
        <h2>菜单数据测试</h2>

        <div class="test-section">
            <h3>原始菜单配置</h3>
            <pre>{{ JSON.stringify(rawMenuConfig, null, 2) }}</pre>
        </div>

        <div class="test-section">
            <h3>处理后的菜单数据</h3>
            <pre>{{ JSON.stringify(processedMenuData, null, 2) }}</pre>
        </div>

        <div class="test-section">
            <h3>主菜单项 ({{ mainMenuItems.length }} 项)</h3>
            <ul>
                <li v-for="item in mainMenuItems" :key="item.id">
                    {{ item.name }} ({{ item.route || 'no route' }})
                </li>
            </ul>
        </div>

        <div class="test-section">
            <h3>系统菜单项 ({{ systemMenuItems.length }} 项)</h3>
            <ul>
                <li v-for="item in systemMenuItems" :key="item.id">
                    {{ item.name }} ({{ item.route || 'no route' }})
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
import {menuConfig, MenuUtils} from '@/admin/config/menuConfig'

export default {
    name: 'MenuTest',

    data() {
        return {
            rawMenuConfig: null,
            processedMenuData: null
        }
    },

    computed: {
        mainMenuItems() {
            return this.processedMenuData?.mainMenuItems || []
        },

        systemMenuItems() {
            return this.processedMenuData?.systemMenuItems || []
        }
    },

    mounted() {
        this.loadMenuData()
    },

    methods: {
        loadMenuData() {
            try {
                // 获取原始配置
                this.rawMenuConfig = menuConfig

                // 获取处理后的配置
                this.processedMenuData = MenuUtils.getProcessedMenuConfig({
                    includeDisabled: false,
                    includeHidden: false,
                    filter: (item) => {
                        return item.visible !== false && !item.adminHidden
                    }
                })

                console.log('MenuTest - 原始配置:', this.rawMenuConfig)
                console.log('MenuTest - 处理后配置:', this.processedMenuData)

            } catch (error) {
                console.error('MenuTest - 加载菜单数据失败:', error)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.menu-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.test-section {
    margin-bottom: 30px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;

    h3 {
        margin-top: 0;
        color: #333;
    }

    pre {
        background: #f5f5f5;
        padding: 10px;
        border-radius: 3px;
        overflow-x: auto;
        max-height: 300px;
        font-size: 12px;
    }

    ul {
        list-style-type: disc;
        padding-left: 20px;

        li {
            margin-bottom: 5px;
        }
    }
}
</style>
