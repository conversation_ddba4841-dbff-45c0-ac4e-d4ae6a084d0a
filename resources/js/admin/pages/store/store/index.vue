<!--
/**
 * 商店管理页面
 *
 * 功能特性：
 * - 企业级商店管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 商店状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/store/store
 * 页面标题：商店列表
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="store-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索商店名称、店主姓名、联系电话或商店编号'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新建商店
                </el-button>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出商店
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchSuspend"
                    type="warning"
                    @click="handleBatchSuspend"
                >
                    <i class="fas fa-pause"></i>
                    批量暂停 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchActivate"
                    type="info"
                    @click="handleBatchActivate"
                >
                    <i class="fas fa-play"></i>
                    批量激活 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="商店状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in STORE_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="商店分类">
                            <el-select v-model="filters.category" clearable placeholder="选择分类">
                                <el-option label="全部分类" value=""></el-option>
                                <el-option label="服装类" value="clothing"></el-option>
                                <el-option label="数码类" value="digital"></el-option>
                                <el-option label="食品类" value="food"></el-option>
                                <el-option label="家居类" value="home"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="开店日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item label="月销售额">
                            <el-input-number
                                v-model="filters.min_sales"
                                :min="0"
                                :precision="2"
                                placeholder="最小金额"
                                style="width: 120px; margin-right: 8px;"
                            />
                            <span style="margin: 0 8px;">-</span>
                            <el-input-number
                                v-model="filters.max_sales"
                                :min="0"
                                :precision="2"
                                placeholder="最大金额"
                                style="width: 120px;"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 商店列表 -->
            <template #default>
                <StoreListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总商店: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						总销售额: <strong>¥{{ formatAmount(totalSales) }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import StoreListView from './components/StoreListView.vue'

export default {
    name: 'AdminStoreStoreIndexPage',
    components: {
        BackendPageListLayout,
        StoreListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                category: '',
                start_date: '',
                end_date: '',
                min_sales: null,
                max_sales: null
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            totalSales: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部商店', icon: 'fas fa-store', badge: 0},
                {name: 'active', label: '活跃商店', icon: 'fas fa-check-circle', badge: 0},
                {name: 'pending', label: '待审核', icon: 'fas fa-clock', badge: 0},
                {name: 'suspended', label: '已暂停', icon: 'fas fa-pause-circle', badge: 0},
                {name: 'closed', label: '已关闭', icon: 'fas fa-times-circle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 商店状态标签
            STORE_STATUS_LABELS: {
                'active': '活跃',
                'pending': '待审核',
                'suspended': '已暂停',
                'closed': '已关闭',
                'reviewing': '审核中'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchSuspend() {
            return this.selectedRows.some(row => row.status === 'active')
        },
        canBatchActivate() {
            return this.selectedRows.some(row => ['suspended', 'pending'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟商店数据（敏感信息脱敏）
                const mockData = [
                    {
                        id: 1,
                        store_code: 'ST202401001',
                        store_name: '时尚服装店',
                        owner_name: '张三',
                        owner_phone: '138****5678', // 手机号脱敏
                        category: 'clothing',
                        status: 'active',
                        monthly_sales: 25680.50,
                        product_count: 156,
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        store_code: 'ST202401002',
                        store_name: '数码科技馆',
                        owner_name: '李四',
                        owner_phone: '139****1234',
                        category: 'digital',
                        status: 'active',
                        monthly_sales: 45890.00,
                        product_count: 89,
                        created_at: '2024-01-14 11:20:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 3,
                        store_code: 'ST202401003',
                        store_name: '美食天地',
                        owner_name: '王五',
                        owner_phone: '136****9876',
                        category: 'food',
                        status: 'pending',
                        monthly_sales: 12350.80,
                        product_count: 234,
                        created_at: '2024-01-13 12:45:00',
                        updated_at: '2024-01-15 12:45:00'
                    },
                    {
                        id: 4,
                        store_code: 'ST202401004',
                        store_name: '家居生活馆',
                        owner_name: '赵六',
                        owner_phone: '135****4567',
                        category: 'home',
                        status: 'suspended',
                        monthly_sales: 18760.30,
                        product_count: 67,
                        created_at: '2024-01-12 16:20:00',
                        updated_at: '2024-01-15 09:15:00'
                    }
                ]

                this.dataList = mockData
                this.total = 1256
                this.totalSales = 2568750.80
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 856
            this.tabOptions[2].badge = 123
            this.tabOptions[3].badge = 234
            this.tabOptions[4].badge = 43
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount || 0).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                category: '',
                start_date: '',
                end_date: '',
                min_sales: null,
                max_sales: null
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('跳转到新建商店页面')
            // TODO: 跳转到商店创建页面
            // this.$router.push('/admin/store/store/create')
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true

                // 模拟导出
                await new Promise(resolve => setTimeout(resolve, 2000))

                this.$message.success('商店数据导出成功')

            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchSuspend() {
            const suspendableStores = this.selectedRows.filter(row =>
                row.status === 'active'
            )

            if (suspendableStores.length === 0) {
                this.$message.warning('没有可暂停的商店')
                return
            }

            this.$confirm(`确定要暂停选中的 ${suspendableStores.length} 个商店吗？`, '确认批量暂停', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功暂停 ${suspendableStores.length} 个商店`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchActivate() {
            const activatableStores = this.selectedRows.filter(row =>
                ['suspended', 'pending'].includes(row.status)
            )

            if (activatableStores.length === 0) {
                this.$message.warning('没有可激活的商店')
                return
            }

            this.$confirm(`确定要激活选中的 ${activatableStores.length} 个商店吗？`, '确认批量激活', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功激活 ${activatableStores.length} 个商店`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看商店详情: ${row.store_name}`)
            // TODO: 跳转到商店详情页面
            // this.$router.push(`/admin/store/store/detail/${row.id}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑商店: ${row.store_name}`)
            // TODO: 跳转到商店编辑页面
            // this.$router.push(`/admin/store/store/edit/${row.id}`)
        },

        handleStatusChange(row, status) {
            this.$message.info(`商店 ${row.store_name} 状态变更为: ${this.STORE_STATUS_LABELS[status]}`)
            this.loadData()
        },

        handleDelete(row) {
            this.$confirm(`确定要删除商店 "${row.store_name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('商店删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.store-index-page {
    .advanced-search-form {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        align-items: center;
        gap: 24px;

        .stat-item {
            font-size: 14px;
            color: #606266;

            strong {
                color: #303133;
                margin-left: 4px;
            }
        }
    }
}
</style>
