<?php

namespace App\Services\Lesson;

use App\Enums\State\CustomerStateEnum;
use App\Http\Resources\Advocate\Customer\CustomerResource;
use App\Models\CustomerCenter\Customer;
use App\Services\BaseService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomerService extends BaseService
{
    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCustomerCount()
    {
        //
        // 1. 计算产品的统计数据 (CountData)
        $customer_counts = [
            'lead_count' => Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::lead())->count(),
            'opportunity_count' => Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::opportunity())->count(),
            'prospect_count' => Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::prospect())->count(),
            'partner_count' => Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::partner())->count(),
        ];
        //
        return $customer_counts;
    }

    /**
     * 获取筛选后的产品列表
     * @param array $filters
     * @param string $customer_state
     * @param string|null $sortBy
     * @param string|null $order
     * @return LengthAwarePaginator
     */
    public static function getFilteredCustomerList(array $filters, string $customer_state, string $sortBy = null, string $order = null)
    {
        //
        $Customer = Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', $customer_state);
        // 添加筛选条件
        if (isset($filters['keyword'])) {
            //
            $Customer->search($filters['keyword']);
        }
        //
        if (isset($filters['date_from'])) {
            $Customer->whereDate('created_at', '>=', $filters['date_from']);
        }
        //
        if (isset($filters['date_to'])) {
            $Customer->whereDate('created_at', '<=', $filters['date_to']);
        }
        // 添加排序
        if ($sortBy && in_array($sortBy, ['customer_name', 'created_at', 'updated_at'])) {
            //
            $order = $order === 'desc' ? 'desc' : 'asc';
            $Customer->orderBy($sortBy, $order);
        } else {
            $Customer->orderBy('created_at', 'desc');
        }
        // Paginate the result
        $Customer = $Customer->paginate(15);
        // 使用 CustomerResource 进行资源封装和返回
        return CustomerResource::collection($Customer)->resource;
    }

    /**
     * 获取待审核产品的列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getCustomerOnlineList()
    {
        // 2. 获取待审核产品列表，并进行分页
        $Customer = Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::lead())->paginate(15);
        // 使用 CustomerResource 进行资源封装和返回
        return CustomerResource::collection($Customer)->resource;
    }

    /**
     * 获取已审核产品的列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getCustomerOfflineList()
    {
        // 3. 获取已审核产品列表，并进行分页
        $Customer = Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::prospect())->paginate(15);
        // 使用 CustomerResource 进行资源封装和返回
        return CustomerResource::collection($Customer)->resource;
    }

    /**
     * 获取拒绝的产品列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getCustomerClosureList()
    {
        // 4. 获取拒绝的产品列表，并进行分页
        $Customer = Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::partner())->paginate(15);
        // 使用 CustomerResource 进行资源封装和返回
        return CustomerResource::collection($Customer)->resource;
    }

    /**
     * 获取拒绝的产品列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getCustomerLockList()
    {
        // 4. 获取拒绝的产品列表，并进行分页
        $Customer = Customer::where('platform_id', '=', config('app.platform_id'))->where('customer_state', CustomerStateEnum::Lock())->paginate(15);
        // 使用 CustomerResource 进行资源封装和返回
        return CustomerResource::collection($Customer)->resource;
    }

    /**
     * 获取单个产品的详细信息
     * @param string $uuid
     * @return CustomerResource
     */
    public static function getCustomerByUuid(string $uuid)
    {
        //
        $Customer = Customer::where('uuid', $uuid)->first();
        $Customer = new CustomerResource($Customer);
        //
        return $Customer;
    }
}
