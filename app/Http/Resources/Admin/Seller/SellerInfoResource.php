<?php

namespace App\Http\Resources\Admin\Seller;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class SellerInfoResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'license_merchant_name' => $this->license_merchant_name ?? null,
            'license_legal_representative' => $this->license_legal_representative ?? null,
            'is_operate' => $this->is_operate ?? null,
            'is_payee' => $this->is_payee ?? null,
            'is_contract' => $this->is_contract ?? null,
        ]);
    }
}
