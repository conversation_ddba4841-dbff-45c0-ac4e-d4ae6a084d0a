<template>
    <div class="backend-notification-panel">
        <!-- 通知弹出层容器 -->
        <div class="notification-container">
            <!-- 高优先级通知 -->
            <div v-if="criticalNotifications.length > 0" class="notification-group critical-group">
                <div
                    v-for="notification in criticalNotifications"
                    :key="notification.id"
                    class="notification-item critical-notification"
                    @click="handleNotificationClick(notification)"
                >
                    <div class="notification-icon critical-icon">
                        <i :class="notification.icon"></i>
                    </div>
                    <div class="notification-content">
                        <h4 class="notification-title">{{ notification.title }}</h4>
                        <p class="notification-message">{{ notification.message }}</p>
                        <div class="notification-meta">
                            <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
                            <span class="notification-category">{{ getCategoryText(notification.category) }}</span>
                        </div>
                    </div>
                    <div class="notification-actions">
                        <el-button
                            v-for="action in notification.actions"
                            v-if="notification.actions?.length > 0"
                            :key="action.text"
                            :type="action.type || 'primary'"
                            size="small"
                            @click.stop="handleAction(notification, action)"
                        >
                            {{ action.text }}
                        </el-button>
                        <el-button
                            circle
                            size="small"
                            @click.stop="dismissNotification('critical', notification.id)"
                        >
                            <i class="fas fa-times"></i>
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 中优先级通知 -->
            <div v-if="importantNotifications.length > 0" class="notification-group important-group">
                <div
                    v-for="notification in importantNotifications"
                    :key="notification.id"
                    class="notification-item important-notification"
                    @click="handleNotificationClick(notification)"
                >
                    <div class="notification-icon important-icon">
                        <i :class="notification.icon"></i>
                    </div>
                    <div class="notification-content">
                        <h4 class="notification-title">{{ notification.title }}</h4>
                        <p class="notification-message">{{ notification.message }}</p>
                        <div class="notification-meta">
                            <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
                        </div>
                    </div>
                    <div class="notification-actions">
                        <el-button
                            circle
                            size="small"
                            @click.stop="dismissNotification('important', notification.id)"
                        >
                            <i class="fas fa-times"></i>
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 普通通知 -->
            <div v-if="normalNotifications.length > 0" class="notification-group normal-group">
                <div
                    v-for="notification in normalNotifications"
                    :key="notification.id"
                    class="notification-item normal-notification"
                    @click="handleNotificationClick(notification)"
                >
                    <div class="notification-icon normal-icon">
                        <i :class="notification.icon"></i>
                    </div>
                    <div class="notification-content">
                        <h4 class="notification-title">{{ notification.title }}</h4>
                        <p class="notification-message">{{ notification.message }}</p>
                        <div class="notification-meta">
                            <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
                        </div>
                    </div>
                    <div class="notification-actions">
                        <el-button
                            circle
                            size="small"
                            @click.stop="dismissNotification('normal', notification.id)"
                        >
                            <i class="fas fa-times"></i>
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 信息提示通知 -->
            <div v-if="infoNotifications.length > 0" class="notification-group info-group">
                <div
                    v-for="notification in infoNotifications"
                    :key="notification.id"
                    class="notification-item info-notification"
                    @click="handleNotificationClick(notification)"
                >
                    <div class="notification-icon info-icon">
                        <i :class="notification.icon"></i>
                    </div>
                    <div class="notification-content">
                        <h4 class="notification-title">{{ notification.title }}</h4>
                        <p class="notification-message">{{ notification.message }}</p>
                        <div class="notification-meta">
                            <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
                        </div>
                    </div>
                    <div class="notification-actions">
                        <el-button
                            circle
                            size="small"
                            @click.stop="dismissNotification('info', notification.id)"
                        >
                            <i class="fas fa-times"></i>
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {useNotificationStore} from '@stores/notification/useNotificationStore.js'
import {computed} from 'vue'

export default {
    name: 'BackendNotificationPanel',
    setup() {
        const notificationSystem = useNotificationStore()

        // 计算属性 - 获取各优先级的可见通知
        const criticalNotifications = computed(() =>
            notificationSystem.critical.visibleNotifications
        )

        const importantNotifications = computed(() =>
            notificationSystem.important.visibleNotifications
        )

        const normalNotifications = computed(() =>
            notificationSystem.normal.visibleNotifications
        )

        const infoNotifications = computed(() =>
            notificationSystem.info.visibleNotifications
        )

        // 方法
        const handleNotificationClick = (notification) => {
            // 标记为已读
            if (notification.priority === 'critical') {
                notificationSystem.critical.markAsRead(notification.id)
            } else if (notification.priority === 'important') {
                notificationSystem.important.markAsRead(notification.id)
            } else if (notification.priority === 'normal') {
                notificationSystem.normal.markAsRead(notification.id)
            } else if (notification.priority === 'info') {
                notificationSystem.info.markAsRead(notification.id)
            }

            // 处理点击事件
            if (notification.onClick) {
                notification.onClick(notification)
            }
        }

        const dismissNotification = (priority, id) => {
            if (priority === 'critical') {
                notificationSystem.critical.dismissNotification(id)
            } else if (priority === 'important') {
                notificationSystem.important.dismissNotification(id)
            } else if (priority === 'normal') {
                notificationSystem.normal.dismissNotification(id)
            } else if (priority === 'info') {
                notificationSystem.info.dismissNotification(id)
            }
        }

        const handleAction = (notification, action) => {
            if (action.action === 'learn-more' && action.url) {
                window.open(action.url, '_blank')
            } else if (action.onClick) {
                action.onClick(notification, action)
            }

            // 执行操作后通常关闭通知
            dismissNotification(notification.priority, notification.id)
        }

        const formatTime = (timestamp) => {
            if (!timestamp) return ''
            const date = new Date(timestamp)
            const now = new Date()
            const diff = now - date

            if (diff < 60000) { // 1分钟内
                return '刚刚'
            } else if (diff < 3600000) { // 1小时内
                return `${Math.floor(diff / 60000)}分钟前`
            } else if (diff < 86400000) { // 24小时内
                return `${Math.floor(diff / 3600000)}小时前`
            } else {
                return date.toLocaleDateString('zh-CN')
            }
        }

        const getCategoryText = (category) => {
            const categoryMap = {
                system: '系统',
                security: '安全',
                error: '错误',
                maintenance: '维护',
                warning: '警告',
                business: '业务',
                operation: '操作',
                success: '成功',
                update: '更新',
                reminder: '提醒',
                action: '操作',
                tip: '提示',
                guide: '指南',
                feature: '功能'
            }
            return categoryMap[category] || category
        }

        return {
            criticalNotifications,
            importantNotifications,
            normalNotifications,
            infoNotifications,
            handleNotificationClick,
            dismissNotification,
            handleAction,
            formatTime,
            getCategoryText
        }
    }
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

.backend-notification-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    pointer-events: none;
}

.notification-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 400px;
}

.notification-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: slideInRight 0.3s ease-out;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
    }

    &.critical-notification {
        border-left: 4px solid #ff4757;
        background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
    }

    &.important-notification {
        border-left: 4px solid #ffa502;
        background: linear-gradient(135deg, #fffbf5 0%, #fff 100%);
    }

    &.normal-notification {
        border-left: 4px solid #2ed573;
        background: linear-gradient(135deg, #f5fff8 0%, #fff 100%);
    }

    &.info-notification {
        border-left: 4px solid #3742fa;
        background: linear-gradient(135deg, #f5f7ff 0%, #fff 100%);
    }
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;

    i {
        font-size: 18px;
        color: white;
    }

    &.critical-icon {
        background: linear-gradient(135deg, #ff4757, #ff3838);
    }

    &.important-icon {
        background: linear-gradient(135deg, #ffa502, #ff9500);
    }

    &.normal-icon {
        background: linear-gradient(135deg, #2ed573, #20bf6b);
    }

    &.info-icon {
        background: linear-gradient(135deg, #3742fa, #2f3542);
    }
}

.notification-content {
    flex: 1;
    min-width: 0;

    .notification-title {
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 4px 0;
        line-height: 1.4;
    }

    .notification-message {
        font-size: 13px;
        color: #5a6c7d;
        margin: 0 0 8px 0;
        line-height: 1.4;
        word-break: break-word;
    }

    .notification-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 11px;
        color: #95a5a6;

        .notification-category {
            background: #ecf0f1;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }
    }
}

.notification-actions {
    display: flex;
    align-items: flex-start;
    gap: 4px;
    margin-left: 8px;
    flex-shrink: 0;

    .el-button {
        font-size: 12px;
        padding: 4px 8px;
        min-height: auto;
        height: auto;

        &.is-circle {
            width: 24px;
            height: 24px;
            padding: 0;
        }
    }
}

// 动画
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .backend-notification-panel {
        top: 10px;
        right: 10px;
        left: 10px;
    }

    .notification-container {
        max-width: none;
    }

    .notification-item {
        padding: 12px;
    }

    .notification-icon {
        width: 36px;
        height: 36px;

        i {
            font-size: 16px;
        }
    }

    .notification-content {
        .notification-title {
            font-size: 13px;
        }

        .notification-message {
            font-size: 12px;
        }
    }
}
</style>
