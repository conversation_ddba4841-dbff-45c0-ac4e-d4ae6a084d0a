<?php

namespace App\Models\ProductDraft;

use App\Casts\Amount\AmountToHundred;
use App\Enums\Is\IsClosureEnum;
use App\Enums\Is\IsEditEnum;
use App\Enums\Is\IsOfflineEnum;
use App\Enums\State\ProductMainStateEnum;
use App\Enums\State\ProductStateEnum;
use App\Models\ProductCenter\Product;
use App\Models\ProductCenter\ProductAttr;
use App\Models\ProductCenter\ProductOption;
use App\Models\ProductCenter\ProductSku;
use App\Models\WarehouseCenter\WarehousePosition;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 产品 SKU 模型类
 * 该类表示产品的 SKU（Stock Keeping Unit）模型，包含 SKU 的属性、关系和方法。
 */
class ProductSkuDraft extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名
     * @var string
     */
    protected $connection = 'product_draft';

    /**
     * 使用有序的 UUID 版本
     * @var string
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性类型转换
     * @var array<string, string>
     */
    protected $casts = [
        //
        'amount' => AmountToHundred::class,
        'price' => AmountToHundred::class,
        'reference_price' => AmountToHundred::class,
        'limited_price' => AmountToHundred::class,
        'cost_price' => AmountToHundred::class,
        'market_price' => AmountToHundred::class,
        'wrap_price' => AmountToHundred::class,
        'box_price' => AmountToHundred::class,
        'freight_price' => AmountToHundred::class,
        'package_price' => AmountToHundred::class,
        'service_price' => AmountToHundred::class,
        'process_price' => AmountToHundred::class,
        'pack_price' => AmountToHundred::class,
        'insurance_price' => AmountToHundred::class,
        'tax_price' => AmountToHundred::class,
        //'thumb_img'       => 'array',
        //'sku_attrs'       => 'array',
        'is_edit' => IsEditEnum::class,
        'is_state' => ProductStateEnum::class,
        'main_state' => ProductMainStateEnum::class,
        //
        'is_offline' => IsOfflineEnum::class,
        'is_closure' => IsClosureEnum::class,
        //
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 模型的引导方法
     * 注册模型观察者等操作
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // 为 ProductSku 模型注册观察者
        //self::observe(ProductSkuObserver::class);
    }

    /**
     * 获取与 SKU 相关的选项
     * @return BelongsToMany
     */
    public function options(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProductOption::class, 'product_center.product_sku_options', 'product_sku_id', 'product_option_id')->withTimestamps();
    }

    /**
     * 获取与 SKU 相关的产品
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        //
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * 获取与 SKU 相关的产品
     * @return BelongsTo
     */
    public function sku(): BelongsTo
    {
        //
        return $this->belongsTo(ProductSku::class, 'sku_id');
    }

    /**
     * 获取 SKU 所在的仓库位置
     * @return BelongsTo
     */
    public function position(): BelongsTo
    {
        //
        return $this->belongsTo(WarehousePosition::class, 'position_id', 'id');
    }

    /**
     * 获取 SKU 的完整图片 URL
     * @return string
     */
    public function getSkuImageUrlAttribute(): string
    {
        //
        $imgDomain = config('app.img_domain');
        $skuImagePath = $this->attributes['sku_image_path'] ?? ''; // 确保字段存在
        return $imgDomain . '/' . ltrim($skuImagePath, '/');
    }

    /**
     * 获取 SKU 的名称
     * @return string
     */
    public function getName(): string
    {
        //
        return $this->sku_name ?? '';
    }

    /**
     * 设置 SKU 的状态
     * @param ProductStateEnum $state 状态枚举值
     * @return void
     */
    public function setState(ProductStateEnum $state): void
    {
        //
        $this->is_state = $state;
        $this->save();
    }

    /**
     * 检查 SKU 是否可编辑
     * @return bool
     */
    public function isEditable(): bool
    {
        //
        return $this->is_edit == IsEditEnum::Approved();
    }

    /**
     * 获取 SKU 的属性列表
     * @return array
     */
    public function getAttributesList(): array
    {
        //
        return $this->product_attrs()->pluck('attr_value', 'attr_name')->toArray();
    }

    /**
     * 获取与 SKU 相关的属性
     * @return BelongsToMany
     */
    public function product_attrs(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProductAttr::class, 'product_center.product_sku_attrs', 'product_sku_id', 'product_attr_id')->withTimestamps();
    }

    /**
     * 添加 SKU 的属性
     * @param int $attrId 属性 ID
     * @return void
     */
    public function addAttribute(int $attrId): void
    {
        //
        $this->product_attrs()->attach($attrId);
    }

    /**
     * 移除 SKU 的属性
     * @param int $attrId 属性 ID
     * @return void
     */
    public function removeAttribute(int $attrId): void
    {
        //
        $this->product_attrs()->detach($attrId);
    }

    /**
     * 初始化模型的默认属性
     * 在保存或更新模型之前，设置一些默认属性
     * @return $this
     */
    public function syncOriginal()
    {
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
