<?php

namespace App\Services\Wechat;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * 微信二维码服务类
 *
 * 负责处理微信公众号二维码相关功能，包括：
 * - 临时二维码生成
 * - 永久二维码生成
 * - 二维码图片下载和缓存
 * - 场景值管理
 *
 * @package App\Services\Wechat
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class WechatQrCodeService
{
    /**
     * 微信API基础URL
     */
    private const API_BASE_URL = 'https://api.weixin.qq.com/cgi-bin';
    /**
     * 二维码显示URL
     */
    private const QR_SHOW_URL = 'https://mp.weixin.qq.com/cgi-bin/showqrcode';
    /**
     * 微信服务实例
     *
     * @var WechatService
     */
    protected WechatService $wechatService;

    /**
     * 构造函数
     *
     * 注入微信服务依赖
     *
     * @param WechatService $wechatService 微信服务
     */
    public function __construct(WechatService $wechatService)
    {
        $this->wechatService = $wechatService;
    }

    /**
     * 创建临时二维码
     *
     * 创建临时的带参数二维码
     *
     * @param string $sceneStr 场景值字符串
     * @param int $expireSeconds 过期时间（秒），最大2592000（30天）
     * @return array 返回二维码信息
     */
    public function createTempQrCode(string $sceneStr, int $expireSeconds = 604800): array
    {
        try {
            // 验证过期时间
            if ($expireSeconds > 2592000) {
                $expireSeconds = 2592000; // 最大30天
            }

            $accessToken = $this->wechatService->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('无法获取访问令牌');
            }

            // 构建请求数据
            $requestData = [
                'expire_seconds' => $expireSeconds,
                'action_name' => 'QR_STR_SCENE',
                'action_info' => [
                    'scene' => [
                        'scene_str' => $sceneStr
                    ]
                ]
            ];

            // 请求创建二维码
            $response = Http::timeout(30)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post(self::API_BASE_URL . '/qrcode/create?access_token=' . $accessToken, $requestData);

            if (!$response->successful()) {
                throw new \Exception('创建二维码失败: ' . $response->status());
            }

            $data = $response->json();

            if (isset($data['errcode']) && $data['errcode'] !== 0) {
                throw new \Exception('创建二维码失败: ' . ($data['errmsg'] ?? 'Unknown error'));
            }

            $ticket = $data['ticket'] ?? null;
            $url = $data['url'] ?? null;

            if (!$ticket) {
                throw new \Exception('二维码ticket为空');
            }

            // 生成二维码图片URL
            $qrCodeUrl = self::QR_SHOW_URL . '?ticket=' . urlencode($ticket);

            // 缓存二维码信息
            $this->cacheQrCodeInfo($sceneStr, [
                'ticket' => $ticket,
                'url' => $url,
                'qr_code_url' => $qrCodeUrl,
                'expire_seconds' => $expireSeconds,
                'created_at' => now(),
                'expires_at' => now()->addSeconds($expireSeconds)
            ]);

            Log::info('临时二维码创建成功', [
                'scene_str' => $sceneStr,
                'expire_seconds' => $expireSeconds,
                'ticket' => substr($ticket, 0, 20) . '...'
            ]);

            return [
                'success' => true,
                'ticket' => $ticket,
                'url' => $qrCodeUrl,
                'expire_seconds' => $expireSeconds,
                'scene_str' => $sceneStr
            ];

        } catch (\Exception $e) {
            Log::error('创建临时二维码失败', [
                'scene_str' => $sceneStr,
                'expire_seconds' => $expireSeconds,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 缓存二维码信息
     *
     * @param string $sceneStr 场景值
     * @param array $data 二维码数据
     * @param \Carbon\Carbon|null $expiry 过期时间
     */
    private function cacheQrCodeInfo(string $sceneStr, array $data, ?\Carbon\Carbon $expiry = null): void
    {
        $key = $this->getQrCodeCacheKey($sceneStr);

        if ($expiry) {
            Cache::put($key, $data, $expiry);
        } else {
            // 临时二维码根据其过期时间缓存
            $cacheExpiry = isset($data['expires_at']) ? $data['expires_at'] : now()->addHour();
            Cache::put($key, $data, $cacheExpiry);
        }
    }

    /**
     * 获取二维码缓存键
     *
     * @param string $sceneStr 场景值
     * @return string 返回缓存键
     */
    private function getQrCodeCacheKey(string $sceneStr): string
    {
        return "wechat_qrcode:{$sceneStr}";
    }

    /**
     * 创建永久二维码
     *
     * 创建永久的带参数二维码
     *
     * @param string $sceneStr 场景值字符串
     * @return array 返回二维码信息
     */
    public function createPermanentQrCode(string $sceneStr): array
    {
        try {
            $accessToken = $this->wechatService->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('无法获取访问令牌');
            }

            // 构建请求数据
            $requestData = [
                'action_name' => 'QR_LIMIT_STR_SCENE',
                'action_info' => [
                    'scene' => [
                        'scene_str' => $sceneStr
                    ]
                ]
            ];

            // 请求创建二维码
            $response = Http::timeout(30)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post(self::API_BASE_URL . '/qrcode/create?access_token=' . $accessToken, $requestData);

            if (!$response->successful()) {
                throw new \Exception('创建永久二维码失败: ' . $response->status());
            }

            $data = $response->json();

            if (isset($data['errcode']) && $data['errcode'] !== 0) {
                throw new \Exception('创建永久二维码失败: ' . ($data['errmsg'] ?? 'Unknown error'));
            }

            $ticket = $data['ticket'] ?? null;
            $url = $data['url'] ?? null;

            if (!$ticket) {
                throw new \Exception('二维码ticket为空');
            }

            // 生成二维码图片URL
            $qrCodeUrl = self::QR_SHOW_URL . '?ticket=' . urlencode($ticket);

            // 缓存二维码信息（永久二维码缓存更长时间）
            $this->cacheQrCodeInfo($sceneStr, [
                'ticket' => $ticket,
                'url' => $url,
                'qr_code_url' => $qrCodeUrl,
                'permanent' => true,
                'created_at' => now()
            ], now()->addDays(30)); // 缓存30天

            Log::info('永久二维码创建成功', [
                'scene_str' => $sceneStr,
                'ticket' => substr($ticket, 0, 20) . '...'
            ]);

            return [
                'success' => true,
                'ticket' => $ticket,
                'url' => $qrCodeUrl,
                'permanent' => true,
                'scene_str' => $sceneStr
            ];

        } catch (\Exception $e) {
            Log::error('创建永久二维码失败', [
                'scene_str' => $sceneStr,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 下载二维码图片
     *
     * 下载二维码图片并保存到本地存储
     *
     * @param string $ticket 二维码ticket
     * @param string|null $filename 保存的文件名
     * @return array 返回下载结果
     */
    public function downloadQrCodeImage(string $ticket, ?string $filename = null): array
    {
        try {
            // 生成二维码图片URL
            $qrCodeUrl = self::QR_SHOW_URL . '?ticket=' . urlencode($ticket);

            // 下载图片
            $response = Http::timeout(30)->get($qrCodeUrl);

            if (!$response->successful()) {
                throw new \Exception('下载二维码图片失败: ' . $response->status());
            }

            $imageContent = $response->body();

            if (empty($imageContent)) {
                throw new \Exception('二维码图片内容为空');
            }

            // 生成文件名
            if (!$filename) {
                $filename = 'qrcode_' . md5($ticket) . '.jpg';
            }

            // 保存到存储
            $path = 'qrcodes/' . $filename;
            $saved = Storage::disk('public')->put($path, $imageContent);

            if (!$saved) {
                throw new \Exception('保存二维码图片失败');
            }

            $fullPath = Storage::disk('public')->path($path);
            $url = Storage::disk('public')->url($path);

            Log::info('二维码图片下载成功', [
                'ticket' => substr($ticket, 0, 20) . '...',
                'filename' => $filename,
                'path' => $path,
                'size' => strlen($imageContent)
            ]);

            return [
                'success' => true,
                'filename' => $filename,
                'path' => $path,
                'full_path' => $fullPath,
                'url' => $url,
                'size' => strlen($imageContent)
            ];

        } catch (\Exception $e) {
            Log::error('下载二维码图片失败', [
                'ticket' => substr($ticket, 0, 20) . '...',
                'filename' => $filename,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查二维码是否有效
     *
     * 检查二维码是否存在且未过期
     *
     * @param string $sceneStr 场景值
     * @return bool 有效返回true
     */
    public function isQrCodeValid(string $sceneStr): bool
    {
        $info = $this->getQrCodeInfo($sceneStr);

        if (!$info) {
            return false;
        }

        // 永久二维码始终有效
        if ($info['permanent'] ?? false) {
            return true;
        }

        // 检查临时二维码是否过期
        if (isset($info['expires_at'])) {
            return now()->isBefore($info['expires_at']);
        }

        return false;
    }

    /**
     * 获取二维码信息
     *
     * 从缓存中获取二维码信息
     *
     * @param string $sceneStr 场景值
     * @return array|null 返回二维码信息或null
     */
    public function getQrCodeInfo(string $sceneStr): ?array
    {
        $key = $this->getQrCodeCacheKey($sceneStr);
        return Cache::get($key);
    }

    /**
     * 删除二维码信息
     *
     * 从缓存中删除二维码信息
     *
     * @param string $sceneStr 场景值
     */
    public function deleteQrCodeInfo(string $sceneStr): void
    {
        $key = $this->getQrCodeCacheKey($sceneStr);
        Cache::forget($key);

        Log::info('二维码信息已删除', [
            'scene_str' => $sceneStr
        ]);
    }

    /**
     * 批量清理过期二维码
     *
     * 清理所有过期的二维码缓存（可用于定时任务）
     */
    public function cleanupExpiredQrCodes(): void
    {
        // 这个方法可以在定时任务中调用
        // 由于使用了Cache的TTL机制，过期的二维码会自动清理
        // 这里主要用于记录清理日志

        Log::info('执行二维码清理任务', [
            'cleanup_time' => now()->toDateTimeString()
        ]);
    }

    /**
     * 生成场景值
     *
     * 生成唯一的场景值
     *
     * @param string $prefix 前缀
     * @return string 返回场景值
     */
    public function generateSceneStr(string $prefix = 'scene'): string
    {
        return $prefix . '_' . time() . '_' . mt_rand(1000, 9999);
    }

    /**
     * 验证场景值格式
     *
     * 验证场景值是否符合微信要求
     *
     * @param string $sceneStr 场景值
     * @return bool 有效返回true
     */
    public function validateSceneStr(string $sceneStr): bool
    {
        // 场景值长度限制：1-64字符
        if (strlen($sceneStr) < 1 || strlen($sceneStr) > 64) {
            return false;
        }

        // 场景值只能包含字母、数字、下划线
        return preg_match('/^[a-zA-Z0-9_]+$/', $sceneStr) === 1;
    }

    /**
     * 获取二维码统计信息
     *
     * 获取二维码的使用统计信息
     *
     * @param string $sceneStr 场景值
     * @return array 返回统计信息
     */
    public function getQrCodeStats(string $sceneStr): array
    {
        // 这里可以实现二维码扫描统计功能
        // 例如：扫描次数、扫描用户、扫描时间等

        return [
            'scene_str' => $sceneStr,
            'scan_count' => 0,
            'unique_users' => 0,
            'last_scan_time' => null,
            'created_time' => null
        ];
    }
}
