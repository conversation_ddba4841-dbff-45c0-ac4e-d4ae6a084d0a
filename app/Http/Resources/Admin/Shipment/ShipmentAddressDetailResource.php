<?php

namespace App\Http\Resources\Admin\Shipment;

use App\Http\Resources\BaseResource;
use App\Models\SiteBrace\SiteMarket;
use Illuminate\Http\Request;

class ShipmentAddressDetailResource extends BaseResource
{


    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return [
            'uuid' => $this->uuid ?? null,
            //
            'name' => $this->market_name ?? null,
            'region_id' => $this->region_id ?? null,
            'country_code' => $this->country_code ?? null,
            'country_name' => $this->country_name ?? null,
            'province_code' => $this->province_code ?? null,
            'province_name' => $this->province_name ?? null,
            'city_code' => $this->city_code ?? null,
            'city_name' => $this->city_name ?? null,
            'area_code' => $this->area_code ?? null,
            'area_name' => $this->area_name ?? null,
            //
            'contact_name' => $this->contact_name ?? null,
            'contact_phone' => $this->contact_phone ?? null,
            //
            'contact_extension' => $this->contact_extension ?? null,
            'national_code' => $this->national_code ?? null,
            'county_name' => $this->county_name ?? null,
            'detail_info' => $this->detail_info ?? null,
            //
            'label_type' => $this->label_type ?? null,
            'label_name' => $this->label_type->label ?? null,
            'label_color' => $this->label_type->color() ?? null,
            //
            'stall' => $this->stall ?? null,
            //
            'latitude' => $this->latitude ?? null,
            'longitude' => $this->longitude ?? null,
            //
            'is_default' => $this->is_default ?? null,
            //
            'send_address' => $this->send_address ?? null,
            'after_address' => $this->after_address ?? null,
            'default_after' => $this->default_after ?? null,
            'pickup_address' => $this->pickup_address ?? null,
            'city_address' => $this->city_address ?? null,
            //
            'market' => isset($this->markets) ? $this->markets()->select('id')->pluck('id')->toArray() : null,
            'site_market' => SiteMarket::where('platform_id', '=', config('app.platform_id'))->get()->toTree(),
        ];
    }
}
