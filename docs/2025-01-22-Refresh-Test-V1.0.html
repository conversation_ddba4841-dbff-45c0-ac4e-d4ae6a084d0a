<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Layout 刷新功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #5247ef 0%, #1664FF 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            background: #fafbfc;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
            border-bottom: 2px solid #5247ef;
            padding-bottom: 8px;
        }
        .test-description {
            color: #606266;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .test-steps {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #5247ef;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
            color: #303133;
        }
        .expected-result {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            padding: 12px;
            margin-top: 15px;
        }
        .expected-result strong {
            color: #1664FF;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #495057;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 12px;
            margin: 15px 0;
            color: #d46b08;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 12px;
            margin: 15px 0;
            color: #389e0d;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e4e7ed;
        }
        .feature-item h4 {
            color: #5247ef;
            margin: 0 0 10px 0;
        }
        .feature-item p {
            color: #606266;
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Backend Layout 刷新功能测试指南</h1>
            <p>版本: V1.0 | 日期: 2025-01-22</p>
        </div>
        
        <div class="content">
            <!-- 功能概述 -->
            <div class="test-section">
                <div class="test-title">🎯 功能概述</div>
                <div class="test-description">
                    本次更新优化了 BackendMainLayout.vue 的刷新功能，实现了局部内容区域的智能刷新，
                    提供了更好的用户体验和更稳定的状态管理。
                </div>
                
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>🔄 智能刷新机制</h4>
                        <p>使用复合 key 确保组件完全重新渲染，支持局部刷新和完整页面刷新</p>
                    </div>
                    <div class="feature-item">
                        <h4>⚡ 状态管理优化</h4>
                        <p>区分不同类型的导航操作，智能管理加载状态和错误处理</p>
                    </div>
                    <div class="feature-item">
                        <h4>🎨 用户体验增强</h4>
                        <p>优化动画效果，提供视觉反馈，确保用户了解操作状态</p>
                    </div>
                    <div class="feature-item">
                        <h4>🔧 全局方法支持</h4>
                        <p>暴露全局方法供其他组件调用，提高代码复用性</p>
                    </div>
                </div>
            </div>

            <!-- 测试用例 1 -->
            <div class="test-section">
                <div class="test-title">🧪 测试用例 1: 基本刷新功能</div>
                <div class="test-description">
                    测试通过刷新按钮进行页面内容刷新的基本功能。
                </div>
                
                <div class="test-steps">
                    <strong>测试步骤:</strong>
                    <ol>
                        <li>打开任意后台管理页面</li>
                        <li>在页面中进行一些操作（如填写表单、滚动页面等）</li>
                        <li>点击右上角的刷新按钮（圆形按钮带刷新图标）</li>
                        <li>观察页面的刷新过程和状态变化</li>
                    </ol>
                </div>
                
                <div class="expected-result">
                    <strong>预期结果:</strong>
                    <ul>
                        <li>✅ 刷新按钮显示加载状态（loading 图标）</li>
                        <li>✅ 内容区域显示骨架屏加载效果</li>
                        <li>✅ 页面内容完全重新渲染</li>
                        <li>✅ 显示"页面内容已刷新"成功消息</li>
                        <li>✅ 更新"上次更新"时间</li>
                        <li>✅ 加载状态在适当时间后消失</li>
                    </ul>
                </div>
            </div>

            <!-- 测试用例 2 -->
            <div class="test-section">
                <div class="test-title">🧪 测试用例 2: 全局方法调用</div>
                <div class="test-description">
                    测试通过全局方法调用刷新功能。
                </div>
                
                <div class="test-steps">
                    <strong>测试步骤:</strong>
                    <ol>
                        <li>打开浏览器开发者工具（F12）</li>
                        <li>切换到 Console 标签页</li>
                        <li>输入以下命令测试快速刷新:</li>
                    </ol>
                </div>
                
                <div class="code-block">
window.BackendLayout.refreshContent()
                </div>
                
                <div class="test-steps">
                    <ol start="4">
                        <li>输入以下命令测试完整刷新:</li>
                    </ol>
                </div>
                
                <div class="code-block">
window.BackendLayout.refreshPage()
                </div>
                
                <div class="expected-result">
                    <strong>预期结果:</strong>
                    <ul>
                        <li>✅ refreshContent() 快速刷新内容，显示成功消息</li>
                        <li>✅ refreshPage() 执行完整刷新流程</li>
                        <li>✅ 两种方法都能正确更新页面内容</li>
                        <li>✅ 控制台无错误信息</li>
                    </ul>
                </div>
            </div>

            <!-- 测试用例 3 -->
            <div class="test-section">
                <div class="test-title">🧪 测试用例 3: 路由切换状态管理</div>
                <div class="test-description">
                    测试在路由切换过程中刷新状态的正确管理。
                </div>
                
                <div class="test-steps">
                    <strong>测试步骤:</strong>
                    <ol>
                        <li>在当前页面点击刷新按钮</li>
                        <li>在刷新过程中（加载状态显示时）快速点击侧边栏其他菜单</li>
                        <li>观察页面切换和状态管理</li>
                        <li>返回原页面，再次测试刷新功能</li>
                    </ol>
                </div>
                
                <div class="expected-result">
                    <strong>预期结果:</strong>
                    <ul>
                        <li>✅ 路由切换正常，不会卡在加载状态</li>
                        <li>✅ 新页面正常显示，无异常状态</li>
                        <li>✅ 返回原页面后刷新功能仍然正常</li>
                        <li>✅ 面包屑和页面标题正确更新</li>
                    </ul>
                </div>
            </div>

            <!-- 测试用例 4 -->
            <div class="test-section">
                <div class="test-title">🧪 测试用例 4: 错误处理测试</div>
                <div class="test-description">
                    测试刷新过程中的错误处理机制。
                </div>
                
                <div class="warning">
                    <strong>注意:</strong> 此测试需要模拟网络错误或服务器异常情况。
                </div>
                
                <div class="test-steps">
                    <strong>测试步骤:</strong>
                    <ol>
                        <li>打开浏览器开发者工具</li>
                        <li>切换到 Network 标签页</li>
                        <li>设置网络为 "Offline" 或 "Slow 3G"</li>
                        <li>点击刷新按钮</li>
                        <li>观察错误处理流程</li>
                        <li>恢复网络连接，点击"重新加载"按钮</li>
                    </ol>
                </div>
                
                <div class="expected-result">
                    <strong>预期结果:</strong>
                    <ul>
                        <li>✅ 网络异常时显示错误状态页面</li>
                        <li>✅ 错误信息清晰明确</li>
                        <li>✅ 提供"重新加载"、"返回上页"等操作选项</li>
                        <li>✅ 网络恢复后重新加载功能正常</li>
                        <li>✅ 错误状态能够正确重置</li>
                    </ul>
                </div>
            </div>

            <!-- 测试用例 5 -->
            <div class="test-section">
                <div class="test-title">🧪 测试用例 5: 动画效果测试</div>
                <div class="test-description">
                    测试页面刷新时的动画效果和视觉体验。
                </div>
                
                <div class="test-steps">
                    <strong>测试步骤:</strong>
                    <ol>
                        <li>在不同类型的页面（列表页、表单页、详情页）测试刷新</li>
                        <li>观察 fade-slide 动画效果</li>
                        <li>检查骨架屏加载动画</li>
                        <li>测试刷新按钮的 loading 状态动画</li>
                    </ol>
                </div>
                
                <div class="expected-result">
                    <strong>预期结果:</strong>
                    <ul>
                        <li>✅ 页面切换动画流畅自然</li>
                        <li>✅ 骨架屏动画符合企业级设计标准</li>
                        <li>✅ 刷新按钮状态变化清晰</li>
                        <li>✅ 整体视觉体验良好，无闪烁或卡顿</li>
                    </ul>
                </div>
            </div>

            <!-- 性能检查 -->
            <div class="test-section">
                <div class="test-title">⚡ 性能检查清单</div>
                <div class="test-description">
                    确保刷新功能不会影响应用性能。
                </div>
                
                <div class="success">
                    <strong>检查项目:</strong>
                    <ul>
                        <li>□ 刷新操作响应时间 < 1秒</li>
                        <li>□ 内存使用无明显增长</li>
                        <li>□ 控制台无错误或警告信息</li>
                        <li>□ 多次刷新后功能仍然稳定</li>
                        <li>□ 不同浏览器兼容性良好</li>
                    </ul>
                </div>
            </div>

            <!-- 总结 -->
            <div class="test-section">
                <div class="test-title">📋 测试总结</div>
                <div class="test-description">
                    完成所有测试用例后，请确认以下关键功能点：
                </div>
                
                <div class="code-block">
✅ 基本刷新功能正常
✅ 全局方法调用有效
✅ 状态管理正确
✅ 错误处理完善
✅ 动画效果良好
✅ 性能表现优秀
                </div>
                
                <div class="warning">
                    <strong>如果发现问题:</strong>
                    <ol>
                        <li>记录具体的错误信息和重现步骤</li>
                        <li>检查浏览器控制台的错误日志</li>
                        <li>确认网络连接状态</li>
                        <li>尝试清除浏览器缓存后重新测试</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
