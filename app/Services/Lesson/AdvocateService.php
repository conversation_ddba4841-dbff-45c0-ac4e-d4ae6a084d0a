<?php

namespace App\Services\Lesson;

use App\Models\AdvocateCenter\Advocate;
use App\Services\BaseService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Jenssegers\Agent\Agent;

class AdvocateService extends BaseService
{
    // 获取首页数据
    public static function getAdvocateIdByAdvocate($user_id = null)
    {
        //
        $id = Auth::id() ?? request()->user()->id ?? $user_id;
        $Advocate = Advocate::find($id);
        //
        return $Advocate ?? null;
    }

    // 获取首页数据
    public static function getAdvocateIdByAdvocateConnection($Advocate = null)
    {
        //
        $connection = collect(
        //
            DB::connection(config('session.connection'))->table(config('session.table', 'sessions'))
                ->where('user_id', Auth::user()->getAuthIdentifier())
                ->orderBy('last_activity', 'desc')
                ->get()
        )->map(function ($session) {
            //
            return (object)[
                'agent' => static::createAgent($session),
                'ip_address' => $session->ip_address,
                'is_current_device' => $session->id === request()->session()->getId(),
                'last_active' => Carbon::createFromTimestamp($session->last_activity)->diffForHumans(),
            ];
        });
        //
        return $connection ?? null;
    }

    /**
     * @param $session
     * @return mixed
     */
    protected static function createAgent($session)
    {
        //
        return tap(new Agent(), function ($agent) use ($session) {
            //
            $agent->setAdvocateAgent($session->user_agent);
        });
    }
}
