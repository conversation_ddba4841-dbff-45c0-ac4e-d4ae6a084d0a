<!--
/**
 * 功能管理页面
 *
 * 功能特性：
 * - 企业级功能管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 功能状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 权限控制显示
 *
 * 路由路径：/admin/plan/functions
 * 页面标题：功能管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="functions-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索功能名称、功能编码或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出功能
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchEnable"
                    type="warning"
                    @click="handleBatchEnable"
                >
                    <i class="fas fa-toggle-on"></i>
                    批量启用 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDisable"
                    type="info"
                    @click="handleBatchDisable"
                >
                    <i class="fas fa-toggle-off"></i>
                    批量禁用 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="功能状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in FUNCTION_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="功能模块">
                            <el-select v-model="filters.module" clearable placeholder="选择模块">
                                <el-option label="全部模块" value=""></el-option>
                                <el-option label="用户管理" value="user"></el-option>
                                <el-option label="订单管理" value="order"></el-option>
                                <el-option label="产品管理" value="product"></el-option>
                                <el-option label="系统设置" value="system"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="权限级别">
                            <el-select v-model="filters.permission_level" clearable placeholder="选择级别">
                                <el-option label="全部级别" value=""></el-option>
                                <el-option label="超级管理员" value="super_admin"></el-option>
                                <el-option label="管理员" value="admin"></el-option>
                                <el-option label="操作员" value="operator"></el-option>
                                <el-option label="普通用户" value="user"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 功能列表 -->
            <template #default>
                <FunctionListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总功能: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已启用: <strong>{{ enabledCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import FunctionListView from './components/FunctionListView.vue'

export default {
    name: 'AdminPlanFunctionsIndexPage',
    components: {
        BackendPageListLayout,
        FunctionListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                module: '',
                permission_level: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            enabledCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部功能', icon: 'fas fa-list', badge: 0},
                {name: 'enabled', label: '已启用', icon: 'fas fa-toggle-on', badge: 0},
                {name: 'disabled', label: '已禁用', icon: 'fas fa-toggle-off', badge: 0},
                {name: 'development', label: '开发中', icon: 'fas fa-code', badge: 0},
                {name: 'deprecated', label: '已废弃', icon: 'fas fa-ban', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 功能状态标签
            FUNCTION_STATUS_LABELS: {
                'enabled': '已启用',
                'disabled': '已禁用',
                'development': '开发中',
                'testing': '测试中',
                'deprecated': '已废弃'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchEnable() {
            return this.selectedRows.some(row => ['disabled', 'testing'].includes(row.status))
        },
        canBatchDisable() {
            return this.selectedRows.some(row => row.status === 'enabled')
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟功能数据
                const mockData = [
                    {
                        id: 1,
                        function_code: 'USER_MANAGE',
                        name: '用户管理',
                        description: '用户账户的增删改查功能',
                        module: 'user',
                        status: 'enabled',
                        permission_level: 'admin',
                        route_path: '/admin/user/user',
                        icon: 'fas fa-users',
                        sort_order: 1,
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:30:00'
                    }
                ]

                this.dataList = mockData
                this.total = 156
                this.enabledCount = 123
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 123
            this.tabOptions[2].badge = 23
            this.tabOptions[3].badge = 8
            this.tabOptions[4].badge = 2
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                module: '',
                permission_level: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('功能数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchEnable() {
            const enableableItems = this.selectedRows.filter(row =>
                ['disabled', 'testing'].includes(row.status)
            )

            if (enableableItems.length === 0) {
                this.$message.warning('没有可启用的功能')
                return
            }

            this.$confirm(`确定要启用选中的 ${enableableItems.length} 个功能吗？`, '确认批量启用', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功启用 ${enableableItems.length} 个功能`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchDisable() {
            const disableableItems = this.selectedRows.filter(row =>
                row.status === 'enabled'
            )

            if (disableableItems.length === 0) {
                this.$message.warning('没有可禁用的功能')
                return
            }

            this.$confirm(`确定要禁用选中的 ${disableableItems.length} 个功能吗？`, '确认批量禁用', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功禁用 ${disableableItems.length} 个功能`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看功能详情: ${row.name}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑功能: ${row.name}`)
        },

        handleDelete(row) {
            this.$message.info(`删除功能: ${row.name}`)
        },

        handleStatusChange(row, newStatus) {
            this.$message.info(`功能状态已更改为: ${this.FUNCTION_STATUS_LABELS[newStatus]}`)
            this.loadData()
        }
    }
}
</script>
