<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            // 注册业务模块路由文件
            Route::middleware('web')->group(base_path('routes/lesson.php'));
            Route::middleware('web')->group(base_path('routes/monitor.php'));
            Route::middleware('web')->group(base_path('routes/chain.php'));
            Route::middleware('web')->group(base_path('routes/admin.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // 注册自定义中间件别名
        $middleware->alias([
            'route.response.type' => \App\Http\Middleware\RouteResponseTypeMiddleware::class,
        ]);
    })
    ->withProviders([
        // 注册权限服务提供者
        \App\Providers\AuthServiceProvider::class,
    ])
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
