<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * Class ProductArchivalStateEnum
 * @package App\Enums\State\Product
 * @method static self NotArchived() 未激活状态
 * @method static self Archived() 已激活状态（激活产品不能进行修改）
 */
final class IsActivationEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'NotArchived' => 0,
            'Archived' => 1,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'NotArchived' => '未激活',
            'Archived' => '已激活 ',
        ];
    }
}
