<?php

namespace App\Enums\State;


use App\Enums\BaseEnum;

/**
 * Class OrderMainStateEnum
 * @package App\Enums\State\Order
 * @method static self default() 默认状态
 * @method static self OrderCreated() 用户下单
 * @method static self OrderProcessing() 商家处理
 * @method static self OrderPicking() 商家拣货
 * @method static self OrderShipping() 商家出货
 * @method static self OrderShipment() 商家发货
 * @method static self OrderTransport() 物流运输
 * @method static self OrderDistribution() 物流配送
 * @method static self OrderSettlement() 账单结算
 * @method static self OrderException() 订单异常
 */
final class OrderMainStateEnum extends BaseEnum
{


    // 定义颜色映射

    public static function currentDescribe($state): string
    {
        $descriptions = [
            'default' => '订单在默认状态',
            'OrderCreated' => '用户创建订单',
            'OrderProcessing' => '商家处理订单',
            'OrderPicking' => '商家拣货中',
            'OrderShipping' => '出货打包中',
            'OrderShipment' => '商家已发货',
            'OrderTransport' => '物流运输中',
            'OrderDistribution' => '物流配送中',
            'OrderSettlement' => '用户收货',
            'OrderException' => '订单异常',
        ];

        return $descriptions[$state] ?? '未知状态';
    }

    public static function nextDescribe($state): string
    {
        $nextDescriptions = [
            'default' => '等待用户创建订单',
            'OrderCreated' => '等待商家处理',
            'OrderProcessing' => '等待拣货',
            'OrderPicking' => '等待出货',
            'OrderShipping' => '等待发货',
            'OrderShipment' => '交付快递公司，等待物流运输',
            'OrderTransport' => '等待签收',
            'OrderDistribution' => '等待结算',
            'OrderSettlement' => '订单完成',
            'OrderException' => '处理订单异常',
        ];

        return $nextDescriptions[$state] ?? '未知下一步';
    }

    protected static function values(): array
    {
        return [
            'default' => 0, // 默认状态
            'OrderCreated' => 1, // 用户下单
            'OrderProcessing' => 2, // 商家处理
            'OrderPicking' => 3, // 商家拣货
            'OrderShipping' => 4, // 商家出货
            'OrderShipment' => 5, // 商家发货
            'OrderTransport' => 6, // 物流运输
            'OrderDistribution' => 7, // 物流配送
            'OrderSettlement' => 8, // 账单结算
            'OrderException' => 9, // 订单异常
        ];
    }

    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'OrderCreated' => '用户下单',
            'OrderProcessing' => '商家处理',
            'OrderPicking' => '商家拣货',
            'OrderShipping' => '商家出货',
            'OrderShipment' => '商家发货',
            'OrderTransport' => '物流运输',
            'OrderDistribution' => '物流配送',
            'OrderSettlement' => '账单结算',
            'OrderException' => '订单异常',
        ];
    }

    // 定义状态标签

    protected static function colors(): array
    {
        //
        return [
            'OrderCreated' => 'red',
            'OrderProcessing' => 'orange',
            'OrderPicking' => 'yellow',
            'OrderShipping' => 'green',
            'OrderShipment' => 'purple',
            'OrderTransport' => 'blue',
            'OrderDistribution' => 'indigo',
            'OrderSettlement' => 'cyan',
            'OrderException' => 'magenta',
        ];
    }

}
