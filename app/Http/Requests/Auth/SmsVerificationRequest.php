<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

/**
 * 短信验证码请求验证类
 *
 * 负责验证短信验证码相关的请求数据，包括：
 * - 手机号格式验证
 * - 验证码格式验证
 * - 频率限制检查
 * - 自定义验证规则
 *
 * @package App\Http\Requests\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class SmsVerificationRequest extends FormRequest
{
    /**
     * 确定用户是否有权限发出此请求
     *
     * 对于短信验证码请求，任何用户都可以尝试
     *
     * @return bool 始终返回true，允许所有用户尝试
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * 定义短信验证码请求的验证规则
     *
     * @return array<string, mixed> 返回验证规则数组
     */
    public function rules(): array
    {
        $rules = [
            // 手机号验证规则
            'mobile' => [
                'required',
                'string',
                'regex:/^1[3-9]\d{9}$/', // 中国大陆手机号格式
            ],

            // 验证码验证规则
            'code' => [
                'required',
                'string',
                'digits:6', // 6位数字验证码
            ],

            // 记住我选项（可选）
            'remember' => [
                'sometimes',
                'boolean',
            ],

            // 用户名（注册时可选）
            'name' => [
                'sometimes',
                'string',
                'max:255',
                'min:2',
                'regex:/^[\p{L}\p{M}\s\-\'\.]+$/u', // 只允许字母、空格、连字符、撇号和点
            ],
        ];

        // 根据请求路径添加特定规则
        if ($this->is('*/register')) {
            // 注册时的额外规则
            $rules['name'] = array_merge($rules['name'] ?? [], ['sometimes']);
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义消息
     *
     * 为验证失败提供用户友好的中文错误消息
     *
     * @return array<string, string> 返回自定义错误消息数组
     */
    public function messages(): array
    {
        return [
            // 手机号字段错误消息
            'mobile.required' => '手机号是必填项',
            'mobile.string' => '手机号必须是文本格式',
            'mobile.regex' => '请输入有效的中国大陆手机号（11位数字，以1开头）',

            // 验证码字段错误消息
            'code.required' => '验证码是必填项',
            'code.string' => '验证码必须是文本格式',
            'code.digits' => '验证码必须是6位数字',

            // 记住我选项错误消息
            'remember.boolean' => '记住我选项格式不正确',

            // 用户名字段错误消息
            'name.string' => '用户名必须是文本格式',
            'name.max' => '用户名不能超过255个字符',
            'name.min' => '用户名至少需要2个字符',
            'name.regex' => '用户名只能包含字母、空格、连字符、撇号和点',
        ];
    }

    /**
     * 获取验证字段的自定义属性名称
     *
     * 为验证字段提供中文名称，用于错误消息显示
     *
     * @return array<string, string> 返回字段中文名称数组
     */
    public function attributes(): array
    {
        return [
            'mobile' => '手机号',
            'code' => '验证码',
            'remember' => '记住我',
            'name' => '用户名',
        ];
    }

    /**
     * 配置验证器实例
     *
     * 在验证器创建后进行额外的配置
     *
     * @param \Illuminate\Validation\Validator $validator 验证器实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 检查验证码提交频率限制
            $this->checkCodeSubmissionRateLimit($validator);

            // 检查手机号格式的额外验证
            $this->validateMobileNumber($validator);
        });
    }

    /**
     * 检查验证码提交频率限制
     *
     * 防止验证码被恶意频繁提交
     *
     * @param \Illuminate\Validation\Validator $validator 验证器实例
     */
    private function checkCodeSubmissionRateLimit($validator): void
    {
        $mobile = $this->input('mobile');

        if (!$mobile) {
            return;
        }

        $key = $this->getCodeSubmissionRateLimitKey($mobile);
        $maxAttempts = config('sms.max_code_attempts_per_minute', 5);
        $decayMinutes = 1;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);

            $validator->errors()->add('code', "验证码提交过于频繁，请在 {$seconds} 秒后再试");
        } else {
            // 增加尝试次数
            RateLimiter::hit($key, $decayMinutes * 60);
        }
    }

    /**
     * 获取验证码提交频率限制的键名
     *
     * @param string $mobile 手机号
     * @return string 返回频率限制键名
     */
    private function getCodeSubmissionRateLimitKey(string $mobile): string
    {
        return 'sms_code_submission:' . $mobile . '|' . $this->ip();
    }

    /**
     * 验证手机号格式的额外检查
     *
     * 进行更严格的手机号格式验证
     *
     * @param \Illuminate\Validation\Validator $validator 验证器实例
     */
    private function validateMobileNumber($validator): void
    {
        $mobile = $this->input('mobile');

        if (!$mobile) {
            return;
        }

        // 检查手机号长度
        if (strlen($mobile) !== 11) {
            $validator->errors()->add('mobile', '手机号必须是11位数字');
            return;
        }

        // 检查是否全为数字
        if (!ctype_digit($mobile)) {
            $validator->errors()->add('mobile', '手机号只能包含数字');
            return;
        }

        // 检查运营商号段（更严格的验证）
        $validPrefixes = [
            // 中国移动
            '134', '135', '136', '137', '138', '139', '147', '150', '151', '152', '157', '158', '159',
            '172', '178', '182', '183', '184', '187', '188', '195', '198',
            // 中国联通
            '130', '131', '132', '145', '155', '156', '166', '171', '175', '176', '185', '186', '196',
            // 中国电信
            '133', '149', '153', '173', '174', '177', '180', '181', '189', '191', '193', '199',
            // 虚拟运营商
            '170', '171'
        ];

        $prefix = substr($mobile, 0, 3);
        if (!in_array($prefix, $validPrefixes)) {
            $validator->errors()->add('mobile', '请输入有效的中国大陆手机号');
        }
    }

    /**
     * 获取已验证的数据
     *
     * 返回经过验证和清理的数据
     *
     * @param array|null $keys 要获取的键名数组
     * @return array 返回验证后的数据
     */
    public function validated($keys = null): array
    {
        $validated = parent::validated($keys);

        // 确保手机号格式一致
        if (isset($validated['mobile'])) {
            $validated['mobile'] = preg_replace('/[\s\-\(\)]/', '', $validated['mobile']);
        }

        // 确保验证码格式一致
        if (isset($validated['code'])) {
            $validated['code'] = preg_replace('/\s/', '', $validated['code']);
        }

        return $validated;
    }

    /**
     * 准备验证数据
     *
     * 在验证之前清理和准备数据
     */
    protected function prepareForValidation(): void
    {
        // 清理手机号（移除空格、连字符等）
        if ($this->has('mobile')) {
            $mobile = $this->input('mobile');
            $mobile = preg_replace('/[\s\-\(\)]/', '', $mobile);
            $this->merge(['mobile' => $mobile]);
        }

        // 清理验证码（移除空格）
        if ($this->has('code')) {
            $code = $this->input('code');
            $code = preg_replace('/\s/', '', $code);
            $this->merge(['code' => $code]);
        }

        // 清理用户名（去除首尾空格）
        if ($this->has('name')) {
            $name = trim($this->input('name'));
            $this->merge(['name' => $name]);
        }
    }

    /**
     * 处理验证失败
     *
     * 当验证失败时的自定义处理逻辑
     *
     * @param \Illuminate\Validation\Validator $validator 验证器实例
     * @throws ValidationException 抛出验证异常
     */
    protected function failedValidation($validator): void
    {
        // 记录验证失败日志
        \Log::warning('短信验证码请求验证失败', [
            'mobile' => $this->input('mobile'),
            'errors' => $validator->errors()->toArray(),
            'ip' => $this->ip(),
            'user_agent' => $this->userAgent(),
            'url' => $this->fullUrl()
        ]);

        parent::failedValidation($validator);
    }
}
