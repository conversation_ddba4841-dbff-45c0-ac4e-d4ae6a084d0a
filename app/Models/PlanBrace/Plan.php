<?php

namespace App\Models\PlanBrace;

use App\Casts\Amount\AmountToHundred;
use App\Models\IndustryBrace\Category;
use App\Models\PlatformBrace\Source;
use App\Models\SiteBrace\Site;
use App\Models\SiteBrace\SiteMarket;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Rinvex\Support\Traits\HasSlug;
use Rinvex\Support\Traits\HasTranslations;
use Rinvex\Support\Traits\ValidatingTrait;
use Spatie\EloquentSortable\SortableTrait;
use Spatie\Sluggable\SlugOptions;

class Plan extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;
    use HasFactory;
    use HasSlug;
    use SoftDeletes;
    use SortableTrait;
    use HasTranslations;
    use ValidatingTrait;
    use GeneratesUuid;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * The attributes that are translatable.
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];
    /**
     * The sortable settings.
     * @var array
     */
    public $sortable = [
        'order_column_name' => 'sort_order',
    ];
    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'plan_brace';
    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * {@inheritdoc}
     */
    protected $fillable = [
        'slug',
        'name',
        'description',
        'is_active',
        'price',
        'signup_fee',
        'currency',
        'trial_period',
        'trial_interval',
        'invoice_period',
        'invoice_interval',
        'grace_period',
        'grace_interval',
        'prorate_day',
        'prorate_period',
        'prorate_extend_due',
        'active_subscribers_limit',
        'sort_order',
    ];

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        //        'slug'                     => 'string',
        //        'is_active'                => 'boolean',
        //        'price'                    => 'float',
        //        'signup_fee'               => 'float',
        //        'currency'                 => 'string',
        //        'trial_period'             => 'integer',
        //        'trial_interval'           => 'string',
        //        'invoice_period'           => 'integer',
        //        'invoice_interval'         => 'string',
        //        'grace_period'             => 'integer',
        //        'grace_interval'           => 'string',
        //        'prorate_day'              => 'integer',
        //        'prorate_period'           => 'integer',
        //        'prorate_extend_due'       => 'integer',
        //        'active_subscribers_limit' => 'integer',
        //        'sort_order'               => 'integer',
        //
        'price' => AmountToHundred::class,
        'signup_fee' => AmountToHundred::class,
        'bond_price' => AmountToHundred::class,
        'charge_rate' => AmountToHundred::class,
        'service_rate' => AmountToHundred::class,
        'invoice_period' => 'integer',
        'trial_period' => 'integer',
        'deleted_at' => 'datetime',
    ];

    /**
     * {@inheritdoc}
     */
    protected $observables = [
        'validating',
        'validated',
    ];

    /**
     * The default rules that the model will validate against.
     * @var array
     */
    protected $rules = [];

    /**
     * Whether the model should throw a
     * ValidationException if it fails validation.
     * @var bool
     */
    protected $throwValidationExceptions = true;

    /**
     * Create a new Eloquent model instance.
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        //
        $this->setTable('plans');
        //
        $this->mergeRules([
            'slug' => 'required|alpha_dash|max:150|unique:' . 'plans' . ',slug',
            'name' => 'required|string|strip_tags|max:150',
            'description' => 'nullable|string|max:32768',
            'is_active' => 'sometimes|boolean',
            'price' => 'required|numeric',
            'signup_fee' => 'required|numeric',
            'currency' => 'required|alpha|size:3',
            'trial_period' => 'sometimes|integer|max:100000',
            'trial_interval' => 'sometimes|in:hour,day,week,month',
            'invoice_period' => 'sometimes|integer|max:100000',
            'invoice_interval' => 'sometimes|in:hour,day,week,month',
            'grace_period' => 'sometimes|integer|max:100000',
            'grace_interval' => 'sometimes|in:hour,day,week,month',
            'sort_order' => 'nullable|integer|max:100000',
            'prorate_day' => 'nullable|integer|max:150',
            'prorate_period' => 'nullable|integer|max:150',
            'prorate_extend_due' => 'nullable|integer|max:150',
            'active_subscribers_limit' => 'nullable|integer|max:100000',
        ]);

        parent::__construct($attributes);
    }

    /**
     * {@inheritdoc}
     */
    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($plan) {
            $plan->features()->delete();
            $plan->planSubscriptions()->delete();
        });
    }

    /**
     * The plan may have many features.
     * @return HasMany
     */
    public function features(): HasMany
    {
        //
        return $this->hasMany('plan_features', 'plan_id', 'id');
    }

    /**
     * Get the options for generating the slug.
     * @return SlugOptions
     */
    public function getSlugOptions(): SlugOptions
    {
        //
        return SlugOptions::create()->doNotGenerateSlugsOnUpdate()->generateSlugsFrom('name')->saveSlugsTo('slug');
    }

    /**
     * The plan may have many subscriptions.
     * @return HasMany
     */
    public function subscriptions(): HasMany
    {
        //
        return $this->hasMany('plan_subscriptions', 'plan_id', 'id');
    }

    /**
     * @return BelongsToMany
     */
    public function markets()
    {
        //
        return $this->belongsToMany(SiteMarket::class)->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function categories()
    {
        //
        return $this->belongsToMany(Category::class)->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function plan_functions()
    {
        //
        return $this->belongsToMany(PlanFunction::class, 'plan_brace.plan_by_plan_function')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function plan_serves()
    {
        //
        return $this->belongsToMany(PlanServe::class, 'plan_brace.plan_by_plan_serve')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function sources()
    {
        //
        return $this->belongsToMany(Source::class, 'plan_brace.plan_by_source')->withTimestamps();
    }

    /**
     * 获取拥有此电话的用户
     */
    public function site()
    {
        //
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * Check if plan is free.
     * @return bool
     */
    public function isFree(): bool
    {
        //
        return (float)$this->price <= 0.00;
    }

    /**
     * Check if plan has trial.
     * @return bool
     */
    public function hasTrial(): bool
    {
        //
        return $this->trial_period && $this->trial_interval;
    }

    /**
     * Check if plan has grace.
     * @return bool
     */
    public function hasGrace(): bool
    {
        //
        return $this->grace_period && $this->grace_interval;
    }

    /**
     * @param string $featureSlug
     * @return Model|HasMany|object|null
     */
    public function getFeatureBySlug(string $featureSlug)
    {
        //
        return $this->features()->where('slug', $featureSlug)->first();
    }

    /**
     * Activate the plan.
     * @return $this
     */
    public function activate()
    {
        //
        $this->update(['is_active' => true]);
        //
        return $this;
    }

    /**
     * Deactivate the plan.
     * @return $this
     */
    public function deactivate()
    {
        $this->update(['is_active' => false]);

        return $this;
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
