/**
 * 批量生成管理后台页面文件脚本
 * 
 * 功能：
 * - 根据 menuConfig.js 配置自动生成所有页面文件
 * - 使用统一的页面模板
 * - 自动创建目录结构
 * - 生成符合企业级标准的页面代码
 * 
 * 使用方法：
 * node scripts/generate-admin-pages.js
 * 
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */

const fs = require('fs')
const path = require('path')

// 页面配置数据 - 基于 menuConfig.js
const pageConfigs = [
    // 站点管理模块
    { route: '/admin/site/market', title: '站点市场', description: '管理站点市场配置和商店信息', icon: 'fas fa-store' },
    { route: '/admin/site/send_address', title: '发货地址', description: '管理发货地址和物流配置', icon: 'fas fa-map-marker-alt' },
    { route: '/admin/site/ship', title: '物流管理', description: '管理物流配置和运输方式', icon: 'fas fa-shipping-fast' },
    { route: '/admin/site/ship_part', title: '物流部件', description: '管理物流部件和配件信息', icon: 'fas fa-boxes' },
    
    // 分类管理模块
    { route: '/admin/category/category', title: '分类管理', description: '管理商品和内容分类', icon: 'fas fa-tags' },
    
    // 格子管理模块
    { route: '/admin/lattice/lattice', title: '格子管理', description: '管理格子布局和排列', icon: 'fas fa-th' },
    
    // 订单管理模块
    { route: '/admin/order/order', title: '订单管理', description: '管理订单处理和状态跟踪', icon: 'fas fa-receipt' },
    
    // 零售管理模块
    { route: '/admin/retail/retail', title: '零售管理', description: '管理零售业务和销售数据', icon: 'fas fa-shopping-cart' },
    
    // 账单管理模块
    { route: '/admin/bill/bill', title: '账单管理', description: '管理账单和发票信息', icon: 'fas fa-file-invoice' },
    
    // 支付管理模块
    { route: '/admin/payment/payment', title: '支付管理', description: '管理支付方式和交易记录', icon: 'fas fa-credit-card' },
    
    // 钱包管理模块
    { route: '/admin/wallet/wallet', title: '钱包管理', description: '管理钱包账户和余额', icon: 'fas fa-wallet' },
    { route: '/admin/wallet/payee', title: '收款人管理', description: '管理收款人信息和账户', icon: 'fas fa-user-check' },
    
    // 商家管理模块
    { route: '/admin/seller/group', title: '商家分组', description: '管理商家分组和分类', icon: 'fas fa-layer-group' },
    { route: '/admin/seller/tag', title: '商家标签', description: '管理商家标签和标记', icon: 'fas fa-tag' },
    
    // 商店管理模块
    { route: '/admin/store/store', title: '商店列表', description: '管理商店信息和配置', icon: 'fas fa-store-alt' },
    { route: '/admin/store/demo', title: '商店演示', description: '管理商店演示和预览', icon: 'fas fa-eye' },
    { route: '/admin/store/class', title: '商店分类', description: '管理商店分类和类型', icon: 'fas fa-sitemap' },
    
    // 计划管理模块
    { route: '/admin/plan/plan', title: '计划管理', description: '管理计划信息和进度', icon: 'fas fa-calendar-alt' },
    { route: '/admin/plan/rent', title: '租赁管理', description: '管理租赁业务和合同', icon: 'fas fa-handshake' },
    { route: '/admin/plan/contract', title: '合同管理', description: '管理合同信息和条款', icon: 'fas fa-file-contract' },
    { route: '/admin/plan/serve', title: '服务管理', description: '管理计划服务和支持', icon: 'fas fa-concierge-bell' },
    { route: '/admin/plan/functions', title: '功能管理', description: '管理计划功能和特性', icon: 'fas fa-cogs' },
    
    // 供应商管理模块
    { route: '/admin/provider/provider', title: '供应商管理', description: '管理供应商信息和合作', icon: 'fas fa-truck' },
    { route: '/admin/provider/demo', title: '供应商演示', description: '管理供应商演示和展示', icon: 'fas fa-play-circle' },
    { route: '/admin/provider/shipment', title: '发货管理', description: '管理供应商发货和物流', icon: 'fas fa-shipping-fast' },
    { route: '/admin/supplier/supplier', title: '供货商管理', description: '管理供货商信息和产品', icon: 'fas fa-industry' },
    { route: '/admin/supplier/demo', title: '供货商演示', description: '管理供货商演示和样品', icon: 'fas fa-eye' },
    
    // 产品管理模块
    { route: '/admin/product/product', title: '产品列表', description: '管理产品信息和库存', icon: 'fas fa-cube' },
    { route: '/admin/product/draft', title: '产品草稿', description: '管理产品草稿和编辑', icon: 'fas fa-edit' },
    { route: '/admin/product/sku', title: 'SKU管理', description: '管理产品SKU和规格', icon: 'fas fa-barcode' },
    
    // 生产管理模块 (系统工具)
    { route: '/admin/produce/schema', title: '生产方案', description: '管理生产方案和流程', icon: 'fas fa-project-diagram' },
    { route: '/admin/produce/group', title: '生产分组', description: '管理生产分组和分类', icon: 'fas fa-layer-group' },
    { route: '/admin/produce/spec', title: '生产规格', description: '管理生产规格和标准', icon: 'fas fa-ruler' },
    { route: '/admin/produce/suit', title: '生产套装', description: '管理生产套装和组合', icon: 'fas fa-boxes' },
    { route: '/admin/produce/custom', title: '生产定制', description: '管理生产定制和个性化', icon: 'fas fa-magic' },
    { route: '/admin/produce/param', title: '生产参数', description: '管理生产参数和配置', icon: 'fas fa-sliders-h' },
    { route: '/admin/produce/fabric', title: '面料管理', description: '管理面料信息和属性', icon: 'fas fa-cut' },
    { route: '/admin/produce/design', title: '设计管理', description: '管理设计方案和创意', icon: 'fas fa-paint-brush' },
    { route: '/admin/produce/trend', title: '趋势管理', description: '管理流行趋势和预测', icon: 'fas fa-chart-line' },
    { route: '/admin/produce/craft', title: '工艺管理', description: '管理生产工艺和技术', icon: 'fas fa-hammer' },
    { route: '/admin/produce/shade', title: '色调管理', description: '管理色调配置和调色', icon: 'fas fa-palette' },
    { route: '/admin/produce/purpose', title: '用途管理', description: '管理产品用途和应用', icon: 'fas fa-bullseye' },
    { route: '/admin/produce/accessory', title: '配件管理', description: '管理生产配件和辅料', icon: 'fas fa-puzzle-piece' },
    { route: '/admin/produce/level', title: '等级管理', description: '管理产品等级和分级', icon: 'fas fa-star' },
    { route: '/admin/produce/tag', title: '生产标签', description: '管理生产标签和标记', icon: 'fas fa-tags' },
    { route: '/admin/produce/peg', title: '挂钩管理', description: '管理挂钩配置和设置', icon: 'fas fa-link' },
    { route: '/admin/produce/property', title: '生产属性', description: '管理生产属性和特征', icon: 'fas fa-list-ul' },
    
    // 版权管理模块 (系统工具)
    { route: '/admin/copyright/copyright', title: '版权管理', description: '管理版权信息和保护', icon: 'fas fa-copyright' },
    { route: '/admin/copyright/copyright_agent', title: '版权代理', description: '管理版权代理和授权', icon: 'fas fa-user-tie' },
    { route: '/admin/copyright/copyright_holder', title: '版权持有人', description: '管理版权持有人信息', icon: 'fas fa-user-shield' },
    { route: '/admin/copyright/copyright_relate', title: '版权关联', description: '管理版权关联和关系', icon: 'fas fa-link' },
    
    // 倡导者管理模块 (系统工具)
    { route: '/admin/advocate/advocate', title: '倡导者管理', description: '管理倡导者信息和活动', icon: 'fas fa-bullhorn' },
    { route: '/admin/advocate/permission', title: '倡导者权限', description: '管理倡导者权限和访问', icon: 'fas fa-key' },
    { route: '/admin/advocate/role', title: '倡导者角色', description: '管理倡导者角色和职责', icon: 'fas fa-user-tag' },
    { route: '/admin/advocate/assignment', title: '倡导者分配', description: '管理倡导者角色分配', icon: 'fas fa-user-cog' },
    
    // 其他系统工具模块
    { route: '/admin/brand/brand', title: '品牌管理', description: '管理品牌信息和商标', icon: 'fas fa-award' },
    { route: '/admin/duty/duty', title: '职责管理', description: '管理职责分配和任务', icon: 'fas fa-tasks' },
    { route: '/admin/think/think', title: '思考管理', description: '管理思考记录和想法', icon: 'fas fa-lightbulb' }
]

// 生成页面模板
function generatePageTemplate(config) {
    const routeParts = config.route.split('/')
    const moduleName = routeParts[2] // 模块名 (如 site, category 等)
    const pageName = routeParts[3] // 页面名 (如 site, market 等)
    
    // 生成组件名
    const componentName = `Admin${moduleName.charAt(0).toUpperCase() + moduleName.slice(1)}${pageName.charAt(0).toUpperCase() + pageName.slice(1)}Index`
    
    return `<!--
/**
 * ${config.title}页面
 * 
 * 功能特性：
 * - ${config.description}
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * 
 * 路由路径：${config.route}
 * 页面标题：${config.title}
 * 
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="admin-page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                <h1>
                    <i class="${config.icon} page-icon"></i>
                    ${config.title}
                </h1>
                <p class="page-description">${config.description}</p>
            </div>
            <div class="page-actions">
                <el-button 
                    type="primary" 
                    :icon="Plus" 
                    @click="handleAdd"
                >
                    新增
                </el-button>
                <el-button 
                    :icon="Refresh" 
                    @click="handleRefresh"
                >
                    刷新
                </el-button>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <el-card class="search-card">
                <el-form 
                    :model="searchForm" 
                    :inline="true" 
                    class="search-form"
                    @submit.prevent="handleSearch"
                >
                    <el-form-item label="关键词">
                        <el-input
                            v-model="searchForm.keyword"
                            placeholder="请输入搜索关键词"
                            clearable
                            @keyup.enter="handleSearch"
                        />
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select 
                            v-model="searchForm.status" 
                            placeholder="请选择状态"
                            clearable
                        >
                            <el-option label="启用" value="1" />
                            <el-option label="禁用" value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button 
                            type="primary" 
                            :icon="Search" 
                            @click="handleSearch"
                            :loading="loading"
                        >
                            搜索
                        </el-button>
                        <el-button 
                            :icon="RefreshLeft" 
                            @click="handleReset"
                        >
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <!-- 数据表格区域 -->
        <div class="table-section">
            <el-card class="table-card">
                <!-- 表格工具栏 -->
                <div class="table-toolbar">
                    <div class="toolbar-left">
                        <el-button 
                            type="danger" 
                            :icon="Delete" 
                            @click="handleBatchDelete"
                            :disabled="!selectedRows.length"
                        >
                            批量删除
                        </el-button>
                    </div>
                    <div class="toolbar-right">
                        <el-tooltip content="刷新数据" placement="top">
                            <el-button 
                                :icon="Refresh" 
                                circle 
                                @click="handleRefresh"
                            />
                        </el-tooltip>
                    </div>
                </div>

                <!-- 数据表格 -->
                <el-table
                    :data="tableData"
                    :loading="loading"
                    @selection-change="handleSelectionChange"
                    stripe
                    border
                    class="admin-table"
                >
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="id" label="ID" width="80" sortable />
                    <el-table-column 
                        prop="name" 
                        label="名称" 
                        min-width="150"
                        show-overflow-tooltip
                    />
                    <el-table-column 
                        prop="status" 
                        label="状态" 
                        width="100"
                        align="center"
                    >
                        <template #default="{ row }">
                            <el-tag 
                                :type="row.status === 1 ? 'success' : 'danger'"
                                size="small"
                            >
                                {{ row.status === 1 ? '启用' : '禁用' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column 
                        prop="created_at" 
                        label="创建时间" 
                        width="180"
                        sortable
                    />
                    <el-table-column 
                        label="操作" 
                        width="200" 
                        fixed="right"
                        align="center"
                    >
                        <template #default="{ row }">
                            <el-button 
                                type="primary" 
                                size="small" 
                                :icon="Edit"
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                            <el-button 
                                type="danger" 
                                size="small" 
                                :icon="Delete"
                                @click="handleDelete(row)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-wrapper">
                    <el-pagination
                        v-model:current-page="pagination.currentPage"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </el-card>
        </div>
    </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
    Plus, 
    Refresh, 
    Search, 
    RefreshLeft, 
    Edit, 
    Delete 
} from '@element-plus/icons-vue'

export default {
    name: '${componentName}',
    components: {
        Plus,
        Refresh,
        Search,
        RefreshLeft,
        Edit,
        Delete
    },
    setup() {
        // 响应式数据
        const loading = ref(false)
        const tableData = ref([])
        const selectedRows = ref([])
        
        // 搜索表单
        const searchForm = reactive({
            keyword: '',
            status: ''
        })
        
        // 分页数据
        const pagination = reactive({
            currentPage: 1,
            pageSize: 20,
            total: 0
        })

        // 获取数据
        const fetchData = async () => {
            loading.value = true
            try {
                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))
                
                // 模拟数据
                const mockData = Array.from({ length: 10 }, (_, index) => ({
                    id: index + 1,
                    name: \`${config.title}示例 \${index + 1}\`,
                    status: Math.random() > 0.5 ? 1 : 0,
                    created_at: new Date().toLocaleString()
                }))
                
                tableData.value = mockData
                pagination.total = 100 // 模拟总数
                
            } catch (error) {
                ElMessage.error('获取数据失败')
                console.error('获取数据失败:', error)
            } finally {
                loading.value = false
            }
        }

        // 事件处理方法
        const handleAdd = () => {
            ElMessage.info('新增功能待实现')
        }

        const handleRefresh = () => {
            fetchData()
        }

        const handleSearch = () => {
            pagination.currentPage = 1
            fetchData()
        }

        const handleReset = () => {
            searchForm.keyword = ''
            searchForm.status = ''
            handleSearch()
        }

        const handleEdit = (row) => {
            ElMessage.info(\`编辑 \${row.name}\`)
        }

        const handleDelete = async (row) => {
            try {
                await ElMessageBox.confirm(
                    \`确定要删除 "\${row.name}" 吗？\`,
                    '确认删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )
                
                ElMessage.success('删除成功')
                fetchData()
            } catch {
                ElMessage.info('已取消删除')
            }
        }

        const handleBatchDelete = async () => {
            if (!selectedRows.value.length) {
                ElMessage.warning('请选择要删除的数据')
                return
            }

            try {
                await ElMessageBox.confirm(
                    \`确定要删除选中的 \${selectedRows.value.length} 条数据吗？\`,
                    '确认批量删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )
                
                ElMessage.success('批量删除成功')
                selectedRows.value = []
                fetchData()
            } catch {
                ElMessage.info('已取消删除')
            }
        }

        const handleSelectionChange = (selection) => {
            selectedRows.value = selection
        }

        const handleSizeChange = (size) => {
            pagination.pageSize = size
            fetchData()
        }

        const handleCurrentChange = (page) => {
            pagination.currentPage = page
            fetchData()
        }

        // 生命周期
        onMounted(() => {
            fetchData()
        })

        return {
            loading,
            tableData,
            selectedRows,
            searchForm,
            pagination,
            fetchData,
            handleAdd,
            handleRefresh,
            handleSearch,
            handleReset,
            handleEdit,
            handleDelete,
            handleBatchDelete,
            handleSelectionChange,
            handleSizeChange,
            handleCurrentChange,
            // Element Plus 图标
            Plus,
            Refresh,
            Search,
            RefreshLeft,
            Edit,
            Delete
        }
    }
}
</script>

<style lang="scss" scoped>
.admin-page-container {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 60px);

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .page-title {
            h1 {
                margin: 0 0 8px 0;
                font-size: 24px;
                font-weight: 600;
                color: #303133;
                display: flex;
                align-items: center;

                .page-icon {
                    margin-right: 12px;
                    color: #409eff;
                }
            }

            .page-description {
                margin: 0;
                color: #909399;
                font-size: 14px;
            }
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }
    }

    .search-section {
        margin-bottom: 20px;

        .search-card {
            .search-form {
                margin-bottom: 0;
            }
        }
    }

    .table-section {
        .table-card {
            .table-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding-bottom: 16px;
                border-bottom: 1px solid #ebeef5;

                .toolbar-left,
                .toolbar-right {
                    display: flex;
                    gap: 12px;
                }
            }

            .admin-table {
                margin-bottom: 20px;
            }

            .pagination-wrapper {
                display: flex;
                justify-content: flex-end;
                padding-top: 16px;
                border-top: 1px solid #ebeef5;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .admin-page-container {
        padding: 12px;

        .page-header {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;

            .page-actions {
                justify-content: flex-end;
            }
        }

        .search-form {
            .el-form-item {
                width: 100%;
                margin-right: 0;
            }
        }

        .table-toolbar {
            flex-direction: column;
            gap: 12px;
            align-items: stretch !important;

            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
        }

        .pagination-wrapper {
            justify-content: center;
        }
    }
}
</style>`
}

// 创建目录结构
function ensureDirectoryExists(filePath) {
    const dir = path.dirname(filePath)
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
    }
}

// 生成文件路径
function getFilePath(route) {
    const routeParts = route.split('/').slice(2) // 移除 /admin 部分
    return path.join(__dirname, '..', 'resources', 'js', 'admin', 'pages', ...routeParts, 'index.vue')
}

// 主函数
function generateAllPages() {
    console.log('开始生成管理后台页面文件...')
    
    let successCount = 0
    let skipCount = 0
    
    pageConfigs.forEach(config => {
        const filePath = getFilePath(config.route)
        
        // 检查文件是否已存在
        if (fs.existsSync(filePath)) {
            console.log(`跳过已存在的文件: ${filePath}`)
            skipCount++
            return
        }
        
        try {
            // 确保目录存在
            ensureDirectoryExists(filePath)
            
            // 生成页面内容
            const content = generatePageTemplate(config)
            
            // 写入文件
            fs.writeFileSync(filePath, content, 'utf8')
            
            console.log(`✓ 生成成功: ${filePath}`)
            successCount++
            
        } catch (error) {
            console.error(`✗ 生成失败: ${filePath}`, error.message)
        }
    })
    
    console.log(`\n生成完成！`)
    console.log(`成功生成: ${successCount} 个文件`)
    console.log(`跳过已存在: ${skipCount} 个文件`)
    console.log(`总计: ${pageConfigs.length} 个页面`)
}

// 执行生成
if (require.main === module) {
    generateAllPages()
}

module.exports = {
    generateAllPages,
    generatePageTemplate,
    pageConfigs
}
