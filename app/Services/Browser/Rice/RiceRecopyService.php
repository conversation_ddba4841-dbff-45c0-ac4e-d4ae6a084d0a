<?php

namespace App\Services\Browser\Rice;

use App\Models\IndustryBrace\Category;
use App\Models\PinCenter\Pin;
use App\Models\RiceCenter\Rice;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class RiceRecopyService extends BaseService
{
    /**
     * 批量将指定的 Rice 数据发布（转换并入库）到 Spare
     * @param $riceData
     * @return void
     * @throws Throwable
     */
    public static function batchFetchRecopyRice($riceData): void
    {
        //
        // 如果你的前端是直接传数组而不是 JSON 字符串，则不需要 json_decode，直接用 $request->input('form.rice_json') 即可。
        // 但这里假设它是字符串，需要 decode。且默认返回的是 stdClass 对象数组。
        try {
            //
            if (isset($riceData['draftBoxId'])) {
                //
                DB::beginTransaction();
                // $riceData 就是一个 stdClass 对象，属性通过 -> 访问
                $rice = Rice::where('draft_no', $riceData['draftBoxId'])->first();
                //dd($rice);
                $Category = Category::where('ali1688_cat_no', $riceData['categoryId'])->first();
                // 获取创建表单所需的数据
                $Shop = Shop::updateOrCreate(
                    ['shop_no' => $riceData['sourceUserId']],
                    array_filter([
                        //
                        'source_id' => 5,
                        'shop_name' => $riceData['sourceShopName'] ?? null,
                        'company_name' => $riceData['sourceShopName'] ?? null,
                    ])
                );
                //
                $Pin = Pin::updateOrCreate(
                    ['pin_no' => $riceData['sourceProductId']],
                    array_filter([
                        'source_id' => 5,
                        'shop_id' => $Shop->id,
                        'category_id' => $Category->id,
                        'pin_subject' => $riceData['subject'] ?? $riceData['sourceSubject'] ?? null,
                        'pin_title' => $riceData['subject'] ?? $riceData['sourceSubject'] ?? null,
                        'pin_pic' => $riceData['previewImage'] ?? null,
                        'pin_price' => $riceData['price'] ?? null,
                        'pin_category_no' => $riceData['categoryId'] ?? null,
                        'pin_category_name' => $riceData['categoryLevelName'] ?? null,
                        //
                        'shop_no' => $riceData['sourceUserId'] ?? null,
                        'shop_name' => $riceData['sourceShopName'] ?? null,
                        'shop_url' => $riceData['sourceShopUrl'] ?? null,
                        //
                        'is_rice' => 1 ?? null,
                    ])
                );
                //
                if (!isset($rice)) {
                    // 没有则新建
                    $rice = Rice::where('pin_no', $riceData['sourceProductId'])->first() ?? new Rice();
                }
                // 一对一赋值：从获取的 $riceData 中取出对应的值
                $rice->source_id = 5;
                $rice->category_id = $rice->category_id ?? $Category->id ?? null;
                $rice->shop_id = $rice->shop_id ?? $Pin->shop_id ?? null;
                $rice->pin_id = $rice->pin_id ?? $Pin->id ?? null;
                $rice->rice_category_no = $riceData['categoryId'] ?? $rice->rice_category_no;
                $rice->rice_category_name = $riceData['categoryLevelName'] ?? $rice->rice_category_name;
                $rice->group_name = $riceData['categoryGroupName'] ?? $rice->group_name;
                //
                $rice->shop_no = $riceData['sourceUserId'] ?? $rice->shop_no;
                $rice->shop_name = $riceData['sourceShopName'] ?? $rice->shop_name;
                $rice->shop_url = $riceData['sourceShopUrl'] ?? $rice->shop_url;
                //
                $rice->pin_no = $riceData['sourceProductId'] ?? $rice->pin_no;
                $rice->rice_title = $riceData['subject'] ?? $riceData['sourceSubject'] ?? $rice->rice_title;
                $rice->rice_number = $riceData['cargoNumber'] ?? $rice->rice_number;
                $rice->rice_image = $riceData['previewImage'] ?? $rice->rice_image;
                //
                $rice->draft_no = $riceData['draftBoxId'] ?? $rice->draft_no;
                //
                $rice->min_price = $riceData['price'] ?? $rice->min_price;
                $rice->amount_sale = $riceData['amountOnSale'] ?? $rice->amount_sale;
                // 处理时间戳字段，使用 Carbon 来标准化时间格式
                $rice->create_time = Carbon::parse($riceData['createTime'])->toDateTimeString() ?? null;
                $rice->update_time = Carbon::parse($riceData['updateTime'])->toDateTimeString() ?? null;
                $rice->publish_time = $Pin->publish_time ?? null;
                //$rice->timing_time  = Carbon::parse($riceData['timingTime'])->toDateTimeString() ?? null;
                //
                $rice->is_filter = $riceData['isFilter'] ?? false;
                $rice->is_copy = $riceData['isAuthCopy'] ?? false;
                $rice->is_optimize = $riceData['isAiOptimize'] ?? false;
                // 保存
                $rice->save();
                //
                if (isset($Category)) {
                    //
                    $rice->is_category = 1;
                    $rice->save();
                }
                //
                if (isset($Pin)) {
                    //
                    $rice->is_pin = 1;
                    $rice->save();
                }
                // 提交事务
                DB::commit();
            }
        } catch (Exception $e) {
            // 回滚事务
            DB::rollBack();
        }
    }
}
