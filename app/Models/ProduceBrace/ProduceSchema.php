<?php

namespace App\Models\ProduceBrace;

use App\Models\BrandBrace\Brand;
use App\Models\IndustryBrace\Category;
use App\Models\KeywordCenter\Keyword;
use App\Models\ProductCenter\Product;
use App\Models\ProductCenter\ProductSku;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProduceSchema extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'produce_brace';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 用户所拥有的角色
     */
    public function category()
    {
        //
        return $this->belongsTo(Category::class);
    }

    /**
     * 属于该角色的用户。
     */
    public function brands()
    {
        //
        return $this->belongsToMany(Brand::class, 'produce_brace.produce_schema_by_brand')->withTimestamps();
    }

    /**
     * 用户所拥有的角色
     */
    public function suit()
    {
        //
        return $this->belongsTo(ProduceSuit::class, 'suit_id', 'id');
    }

    /**
     * 属于该角色的用户。
     */
    public function custom()
    {
        //
        return $this->belongsTo(ProduceCustom::class, 'custom_id', 'id');
    }

    /**
     * 属于该角色的用户。
     */
    public function specs()
    {
        //
        return $this->belongsToMany(ProduceSpec::class, 'produce_brace.produce_schema_by_produce_spec')->withTimestamps();
    }


    public function fabrics()
    {
        return $this->belongsToMany(ProduceFabric::class, 'produce_brace.produce_schema_by_produce_fabric')->withTimestamps();
    }

    public function designs()
    {
        return $this->belongsToMany(ProduceDesign::class, 'produce_brace.produce_schema_by_produce_design')->withTimestamps();
    }

    public function trends()
    {
        return $this->belongsToMany(ProduceTrend::class, 'produce_brace.produce_schema_by_produce_trend')->withTimestamps();
    }

    public function crafts()
    {
        return $this->belongsToMany(ProduceCraft::class, 'produce_brace.produce_schema_by_produce_craft')->withTimestamps();
    }

    public function shades()
    {
        return $this->belongsToMany(ProduceShade::class, 'produce_brace.produce_schema_by_produce_shade')->withTimestamps();
    }

    public function purposes()
    {
        return $this->belongsToMany(ProducePurpose::class, 'produce_brace.produce_schema_by_produce_purpose')->withTimestamps();
    }

    public function accessories()
    {
        return $this->belongsToMany(ProduceAccessory::class, 'produce_brace.produce_schema_by_produce_accessory')->withTimestamps();
    }

    /**
     * 属于该角色的用户。
     */
    public function params()
    {
        //
        return $this->belongsToMany(ProduceParam::class, 'produce_brace.produce_schema_by_produce_param')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function keywords()
    {
        //
        return $this->belongsToMany(Keyword::class, 'produce_brace.produce_schema_by_keyword')->withTimestamps();
    }

    /**
     * 关联用户地址
     * @return HasMany
     */
    public function products()
    {
        //
        return $this->hasMany(Product::class, 'group_id', 'id');
    }

    /**
     * 关联用户地址
     * @return HasMany
     */
    public function product_skus()
    {
        //
        return $this->hasMany(ProductSku::class, 'group_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}

