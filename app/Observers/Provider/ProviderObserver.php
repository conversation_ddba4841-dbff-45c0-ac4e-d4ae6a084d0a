<?php

namespace App\Observers\Provider;

use App\Models\Provider\Provider;

class ProviderObserver
{
    /**
     * Handle the Provider "created" event.
     */
    public function created(Provider $Provider): void
    {
        //
    }

    /**
     * Handle the Provider "updated" event.
     */
    public function updated(Provider $Provider): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($Provider->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($Provider->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * Handle the Provider "deleted" event.
     */
    public function deleted(Provider $Provider): void
    {
        //
    }

    /**
     * Handle the Provider "restored" event.
     */
    public function restored(Provider $Provider): void
    {
        //
    }

    /**
     * Handle the Provider "force deleted" event.
     */
    public function forceDeleted(Provider $Provider): void
    {
        //
    }
}
