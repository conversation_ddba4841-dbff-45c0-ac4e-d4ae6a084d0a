<!--
/**
 * 用户权限管理页面
 *
 * 功能特性：
 * - 权限列表展示
 * - 权限分组管理
 * - 企业级管理界面
 * - 搜索和筛选功能
 *
 * 路由路径：/admin/user/permission
 * 页面标题：权限管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="user-permission-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索权限名称或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button type="success" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增权限
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="权限分组">
                            <el-select v-model="filters.group" clearable placeholder="选择分组">
                                <el-option label="全部分组" value=""></el-option>
                                <el-option label="用户管理" value="user"></el-option>
                                <el-option label="系统设置" value="system"></el-option>
                                <el-option label="内容管理" value="content"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="权限类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="菜单权限" value="menu"></el-option>
                                <el-option label="操作权限" value="action"></el-option>
                                <el-option label="数据权限" value="data"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 权限列表 -->
            <template #default>
                <PermissionListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总权限: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import PermissionListView from './components/PermissionListView.vue'

export default {
    name: 'AdminUserPermissionIndexPage',
    components: {
        BackendPageListLayout,
        PermissionListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                group: '',
                type: ''
            },

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部权限', icon: 'fas fa-key', badge: 0},
                {name: 'menu', label: '菜单权限', icon: 'fas fa-bars', badge: 0},
                {name: 'action', label: '操作权限', icon: 'fas fa-cog', badge: 0},
                {name: 'data', label: '数据权限', icon: 'fas fa-database', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟权限数据
                const mockData = [
                    {
                        id: 1,
                        name: '用户管理',
                        code: 'user.manage',
                        group: 'user',
                        type: 'menu',
                        description: '用户管理菜单权限',
                        resource: '/admin/user',
                        created_at: '2024-01-01 09:00:00'
                    },
                    {
                        id: 2,
                        name: '创建用户',
                        code: 'user.create',
                        group: 'user',
                        type: 'action',
                        description: '创建新用户的操作权限',
                        resource: 'POST:/api/users',
                        created_at: '2024-01-01 09:15:00'
                    },
                    {
                        id: 3,
                        name: '编辑用户',
                        code: 'user.edit',
                        group: 'user',
                        type: 'action',
                        description: '编辑用户信息的操作权限',
                        resource: 'PUT:/api/users/{id}',
                        created_at: '2024-01-01 09:30:00'
                    },
                    {
                        id: 4,
                        name: '用户数据查看',
                        code: 'user.data.view',
                        group: 'user',
                        type: 'data',
                        description: '查看用户敏感数据的权限',
                        resource: null,
                        created_at: '2024-01-01 09:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 45
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 15
            this.tabOptions[2].badge = 20
            this.tabOptions[3].badge = 10
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                group: '',
                type: ''
            }
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleCreate() {
            this.$message.info('创建权限功能开发中...')
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.user-permission-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
