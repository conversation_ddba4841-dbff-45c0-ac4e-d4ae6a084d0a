<?php

namespace App\Casts\Amount;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AmountToHundred implements CastsAttributes
{
    /**
     * 转换给定的值。
     * @param array<string, mixed> $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        //
        return sprintf('%0.2f', $value / 100);
    }

    /**
     * 为存储准备给定的值。
     * @param array<string, mixed> $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        //
        return $value * 100;
    }
}
