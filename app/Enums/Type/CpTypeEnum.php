<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self direct()
 * @method static self franchise()
 * @method static self lastMile()
 * @method static self directWithPoint()
 */
final class CpTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'direct' => 1,
            'franchise' => 2,
            'lastMile' => 3,
            'directWithPoint' => 4,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
            'direct' => '直营',
            'franchise' => '加盟',
            'lastMile' => '落地配',
            'directWithPoint' => '直营带网点',
        ];
    }


}
