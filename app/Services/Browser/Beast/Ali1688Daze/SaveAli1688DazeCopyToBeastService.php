<?php

namespace App\Services\Browser\Beast\Ali1688Daze;

use App\Enums\State\BeastStateEnum;
use App\Models\BeastCenter\Beast;
use App\Models\IndustryBrace\Category;
use App\Models\PinCenter\Pin;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class SaveAli1688DazeCopyToBeastService extends BaseService
{
    /**
     * 批量将指定的 Beast 数据发布（转换并入库）到 Spare
     * @param $BeastData
     * @return void
     * @throws Throwable
     */
    public static function batchFetchRecopyBeast($BeastData): void
    {
        //
        // 如果你的前端是直接传数组而不是 JSON 字符串，则不需要 json_decode，直接用 $request->input('form.beast_json') 即可。
        // 但这里假设它是字符串，需要 decode。且默认返回的是 stdClass 对象数组。
        try {
            //
            if (isset($BeastData['draftBoxId'])) {
                //
                DB::beginTransaction();
                // $BeastData 就是一个 stdClass 对象，属性通过 -> 访问
                $Beast = Beast::where('draft_no', $BeastData['draftBoxId'])->first();
                //dd($Beast);
                $Category = Category::where('ali1688_cat_no', $BeastData['categoryId'])->first();
                // 获取创建表单所需的数据
                $Shop = Shop::updateOrCreate(
                    ['shop_no' => $BeastData['sourceUserId']],
                    array_filter([
                        //
                        'source_id' => 5,
                        'shop_no' => $BeastData['sourceUserId'] ?? null,
                        'shop_name' => $BeastData['sourceShopName'] ?? null,
                        'company_name' => $BeastData['sourceShopName'] ?? null,
                    ])
                );
                //
                $Pin = Pin::updateOrCreate(
                    ['pin_no' => $BeastData['sourceProductId']],
                    array_filter([
                        'source_id' => 5,
                        'shop_id' => $Shop->id ?? null,
                        'category_id' => $Category->id ?? null,
                        //
                        'pin_no' => $BeastData['sourceProductId'] ?? null,
                        'pin_subject' => $BeastData['subject'] ?? $BeastData['sourceSubject'] ?? null,
                        'pin_title' => $BeastData['subject'] ?? $BeastData['sourceSubject'] ?? null,
                        'pin_pic' => $BeastData['previewImage'] ?? null,
                        'pin_price' => $BeastData['price'] ?? null,
                        //
                        'pin_category_no' => $BeastData['categoryId'] ?? null,
                        'pin_category_name' => $BeastData['categoryLevelName'] ?? null,
                        //
                        'shop_no' => $BeastData['sourceUserId'] ?? null,
                        'shop_name' => $BeastData['sourceShopName'] ?? null,
                        'shop_url' => $BeastData['sourceShopUrl'] ?? null,
                    ])
                );
                //
                if (!isset($Beast)) {
                    // 没有则新建
                    $Beast = Beast::where('pin_no', $BeastData['sourceProductId'])->first() ?? new Beast();
                }
                // 一对一赋值：从获取的 $BeastData 中取出对应的值
                $Beast->source_id = 5;
                $Beast->category_id = $Beast->category_id ?? $Category->id ?? null;
                $Beast->shop_id = $Beast->shop_id ?? $Pin->shop_id ?? null;
                $Beast->pin_id = $Beast->pin_id ?? $Pin->id ?? null;
                $Beast->beast_category_no = $BeastData['categoryId'] ?? $Beast->beast_category_no ?? null;
                $Beast->beast_category_name = $BeastData['categoryLevelName'] ?? $Beast->beast_category_name ?? null;
                $Beast->group_name = $BeastData['categoryGroupName'] ?? $Beast->group_name ?? null;
                //
                $Beast->shop_no = $BeastData['sourceUserId'] ?? $Beast->shop_no ?? null;
                $Beast->shop_name = $BeastData['sourceShopName'] ?? $Beast->shop_name ?? null;
                $Beast->shop_url = $BeastData['sourceShopUrl'] ?? $Beast->shop_url ?? null;
                //
                $Beast->pin_no = $BeastData['sourceProductId'] ?? $Beast->pin_no ?? null;
                $Beast->beast_subject = $BeastData['subject'] ?? $BeastData['sourceSubject'] ?? $Beast->beast_subject ?? null;
                $Beast->beast_number = $BeastData['cargoNumber'] ?? $Beast->beast_number ?? null;
                $Beast->beast_image = $BeastData['previewImage'] ?? $Beast->beast_image ?? null;
                //
                $Beast->draft_no = $BeastData['draftBoxId'] ?? $Beast->draft_no ?? null;
                //
                $Beast->min_price = $BeastData['price'] ?? $Beast->min_price ?? null;
                $Beast->amount_sale = $BeastData['amountOnSale'] ?? $Beast->amount_sale ?? null;
                // 处理时间戳字段，使用 Carbon 来标准化时间格式
                $Beast->create_time = Carbon::parse($BeastData['createTime'])->toDateTimeString() ?? null;
                $Beast->update_time = Carbon::parse($BeastData['updateTime'])->toDateTimeString() ?? null;
                $Beast->publish_time = $Pin->publish_time ?? null;
                //
                $Beast->is_filter = $BeastData['isFilter'] ?? false;
                $Beast->is_copy = $BeastData['isAuthCopy'] ?? false;
                $Beast->is_optimize = $BeastData['isAiOptimize'] ?? false;
                $Beast->save();
                //
                // 如果需要确保从数据库中刷新最新的值，可以调用 refresh()
                $Beast->refresh();
                // 检查 beast_state 是否小于 "审核中" 状态，如果是，则继续处理
                if ($Beast->beast_state < BeastStateEnum::review()) {
                    //
                    if (isset($Category)) {
                        // 如果存在 Category，则将 beast_state 设置为已选品状态
                        $Beast->beast_state = BeastStateEnum::selected(); // 设置为已选品状态
                        $Beast->is_category = 1;  // 标记为已分类
                        $Beast->save();  // 保存
                    }
                    if (isset($Pin)) {
                        // 如果存在 Pin，则将 beast_state 设置为机审通过状态
                        $Beast->beast_state = BeastStateEnum::approved(); // 设置为机审通过状态
                        $Beast->is_pin = 1;  // 标记为已置顶
                        $Beast->save();  // 保存
                    }
                    //
                    if (isset($Beast->pin_no) && $Beast->is_full && isset($Pin)) {
                        // 没有则新建
                        $Pin->is_rice = 1;
                        $Pin->save();
                    }
                }
                // 提交事务
                DB::commit();
            }
        } catch (Exception $e) {
            // 回滚事务
            DB::rollBack();
            // 捕获异常并记录日志
            Log::error('Error storing Beast data', ['error' => $e->getMessage(), 'BeastData' => $Beast]);
            // 打印出具体的错误信息和Beast对象的详细数据
            dd($e->getMessage(), $Beast->toArray()); // 使用 dd() 查看详细的Beast数据
        }
    }
}
