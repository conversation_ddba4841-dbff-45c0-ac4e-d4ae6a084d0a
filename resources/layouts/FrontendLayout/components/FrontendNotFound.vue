<template>
    <div class="frontend-not-found">
        <div :class="containerClasses">
            <div class="error-content">
                <!-- 错误图标和代码 -->
                <div class="error-visual">
                    <div class="error-code">404</div>
                    <div class="error-icon">
                        <i class="fas fa-search"></i>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div class="error-info">
                    <h1 class="error-title">{{ notFoundConfig.title || '页面未找到' }}</h1>
                    <p class="error-description">
                        {{
                            notFoundConfig.description || '抱歉，您访问的页面不存在或已被移除。请检查URL是否正确，或从导航菜单选择其他页面。'
                        }}
                    </p>
                </div>

                <!-- 操作按钮 -->
                <div class="error-actions">
                    <el-button size="large" type="primary" @click="goToHome">
                        <el-icon>
                            <House/>
                        </el-icon>
                        {{ notFoundConfig.homeButtonText || '返回首页' }}
                    </el-button>
                    <el-button size="large" @click="goBack">
                        <el-icon>
                            <ArrowLeft/>
                        </el-icon>
                        {{ notFoundConfig.backButtonText || '返回上页' }}
                    </el-button>
                </div>

                <!-- 建议链接 -->
                <div v-if="suggestions.length > 0" class="suggestions">
                    <h3 class="suggestions-title">{{ notFoundConfig.suggestionsTitle || '您可能需要访问：' }}</h3>
                    <div class="suggestions-grid">
                        <router-link
                            v-for="(suggestion, index) in suggestions"
                            :key="index"
                            :to="suggestion.route"
                            class="suggestion-item"
                        >
                            <div class="suggestion-icon">
                                <i :class="suggestion.icon"></i>
                            </div>
                            <div class="suggestion-content">
                                <span class="suggestion-label">{{ suggestion.label }}</span>
                                <span v-if="suggestion.description" class="suggestion-desc">{{
                                        suggestion.description
                                    }}</span>
                            </div>
                        </router-link>
                    </div>
                </div>

                <!-- 搜索建议 -->
                <div v-if="notFoundConfig.showSearch !== false" class="search-section">
                    <h3 class="search-title">{{ notFoundConfig.searchTitle || '或者尝试搜索：' }}</h3>
                    <div class="search-box">
                        <el-input
                            v-model="searchQuery"
                            :placeholder="notFoundConfig.searchPlaceholder || '输入关键词搜索...'"
                            size="large"
                            @keyup.enter="handleSearch"
                        >
                            <template #append>
                                <el-button type="primary" @click="handleSearch">
                                    <el-icon>
                                        <Search/>
                                    </el-icon>
                                </el-button>
                            </template>
                        </el-input>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {ElMessage} from 'element-plus'
import {ArrowLeft, House, Search} from '@element-plus/icons-vue'

/**
 * 前台404页面组件
 *
 * 功能说明：
 * - 显示友好的404错误页面
 * - 提供返回首页和上一页的操作
 * - 显示建议的页面链接
 * - 提供搜索功能
 * - 记录404访问日志
 *
 * 配置选项：
 * - title: 错误标题
 * - description: 错误描述
 * - homeRoute: 首页路由
 * - suggestions: 建议链接
 * - showSearch: 是否显示搜索
 *
 * 使用场景：
 * - 前台页面404错误
 * - 用户友好的错误提示
 * - 引导用户到正确页面
 */
export default {
    name: 'FrontendNotFound',
    components: {
        ArrowLeft,
        House,
        Search
    },

    props: {
        /**
         * 404页面配置
         * @type {Object}
         * @default null
         */
        notFoundConfig: {
            type: Object,
            default: () => ({
                homeRoute: '/',
                suggestions: [
                    {route: '/', icon: 'fas fa-home', label: '首页', description: '返回网站首页'},
                    {route: '/products', icon: 'fas fa-box', label: '产品', description: '查看我们的产品'},
                    {route: '/solutions', icon: 'fas fa-cogs', label: '解决方案', description: '了解解决方案'},
                    {route: '/contact', icon: 'fas fa-envelope', label: '联系我们', description: '获取帮助支持'}
                ]
            })
        }
    },

    data() {
        return {
            searchQuery: ''
        }
    },

    computed: {
        /**
         * 容器样式类
         */
        containerClasses() {
            const classes = ['error-container']
            const containerType = this.notFoundConfig?.containerType || 'container-1760'
            classes.push(containerType)

            if (containerType === 'container-1760') {
                classes.push('container-mobile')
            }

            return classes
        },

        /**
         * 获取建议链接列表
         */
        suggestions() {
            return this.notFoundConfig?.suggestions || []
        }
    },

    mounted() {
        // 记录404访问日志
        this.logNotFoundAccess()
    },

    methods: {
        /**
         * 返回首页
         */
        async goToHome() {
            try {
                const homeRoute = this.notFoundConfig?.homeRoute || '/'
                await this.$router.push(homeRoute)
                ElMessage.success('已返回首页')
            } catch (error) {
                console.error('导航错误:', error)
                ElMessage.error('导航失败，请重试')
            }
        },

        /**
         * 返回上页
         */
        goBack() {
            if (window.history.length > 1) {
                this.$router.back()
            } else {
                this.goToHome()
            }
        },

        /**
         * 处理搜索
         */
        handleSearch() {
            if (!this.searchQuery.trim()) {
                ElMessage.warning('请输入搜索关键词')
                return
            }

            // 如果配置了搜索路由，跳转到搜索页面
            if (this.notFoundConfig?.searchRoute) {
                this.$router.push({
                    path: this.notFoundConfig.searchRoute,
                    query: {q: this.searchQuery}
                })
            } else {
                // 否则跳转到首页并带上搜索参数
                this.$router.push({
                    path: '/',
                    query: {search: this.searchQuery}
                })
            }
        },

        /**
         * 记录404访问日志
         */
        logNotFoundAccess() {
            try {
                const logData = {
                    path: this.$route.path,
                    fullPath: this.$route.fullPath,
                    query: this.$route.query,
                    referrer: document.referrer,
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    type: 'frontend_404'
                }

                // 记录到本地存储
                const logs = JSON.parse(localStorage.getItem('frontend404Logs') || '[]')
                logs.push(logData)

                // 只保留最近50条记录
                if (logs.length > 50) {
                    logs.splice(0, logs.length - 50)
                }

                localStorage.setItem('frontend404Logs', JSON.stringify(logs))

                // 发送到分析服务（如果有的话）
                if (typeof window !== 'undefined' && window.gtag) {
                    window.gtag('event', 'frontend_404', {
                        page_path: this.$route.path,
                        page_title: '前台404',
                        custom_map: {metric1: 'frontend_errors'}
                    })
                }

                console.warn('Frontend 404 访问记录:', logData)
            } catch (error) {
                console.error('记录404日志失败:', error)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;

/* === 404页面容器 === */
.frontend-not-found {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    .error-container {
        margin: 0 auto;
        padding: 0 $spacing-lg;
        width: 100%;

        &.container {
            max-width: 1200px;
        }

        &.container-1760 {
            max-width: 1760px;

            &.container-mobile {
                @media (max-width: 768px) {
                    padding: 0 $spacing-base;
                }
            }
        }
    }
}

/* === 错误内容 === */
.error-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

/* === 错误视觉元素 === */
.error-visual {
    position: relative;
    margin-bottom: $spacing-xl;

    .error-code {
        font-size: 8rem;
        font-weight: 900;
        color: $primary-color;
        opacity: 0.1;
        line-height: 1;
        margin-bottom: -2rem;

        @media (max-width: 768px) {
            font-size: 6rem;
        }
    }

    .error-icon {
        font-size: 4rem;
        color: $primary-color;
        margin-bottom: $spacing-lg;

        @media (max-width: 768px) {
            font-size: 3rem;
        }
    }
}

/* === 错误信息 === */
.error-info {
    margin-bottom: $spacing-xl;

    .error-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: $text-primary;
        margin-bottom: $spacing-base;

        @media (max-width: 768px) {
            font-size: 2rem;
        }
    }

    .error-description {
        font-size: $font-size-lg;
        color: $text-secondary;
        line-height: 1.6;
        max-width: 600px;
        margin: 0 auto;

        @media (max-width: 768px) {
            font-size: $font-size-base;
        }
    }
}

/* === 操作按钮 === */
.error-actions {
    display: flex;
    gap: $spacing-base;
    justify-content: center;
    margin-bottom: $spacing-xl;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;

        .el-button {
            width: 200px;
        }
    }
}

/* === 建议链接 === */
.suggestions {
    margin-bottom: $spacing-xl;

    .suggestions-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: $spacing-lg;
    }

    .suggestions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: $spacing-base;
        max-width: 800px;
        margin: 0 auto;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }

    .suggestion-item {
        display: flex;
        align-items: center;
        padding: $spacing-base;
        background: white;
        border: 1px solid $border-color;
        border-radius: $border-radius-base;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
            border-color: $primary-color;
            box-shadow: 0 4px 12px rgba($primary-color, 0.15);
            transform: translateY(-2px);
        }

        .suggestion-icon {
            width: 48px;
            height: 48px;
            background: color.adjust($primary-color, $lightness: 45%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: $spacing-base;
            flex-shrink: 0;

            i {
                font-size: $font-size-lg;
                color: $primary-color;
            }
        }

        .suggestion-content {
            flex: 1;
            text-align: left;

            .suggestion-label {
                display: block;
                font-weight: 600;
                color: $text-primary;
                margin-bottom: $spacing-xs;
            }

            .suggestion-desc {
                display: block;
                font-size: $font-size-sm;
                color: $text-secondary;
            }
        }
    }
}

/* === 搜索区域 === */
.search-section {
    .search-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: $spacing-base;
    }

    .search-box {
        max-width: 400px;
        margin: 0 auto;
    }
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .frontend-not-found {
        min-height: calc(100vh - 160px);
        padding: $spacing-lg 0;
    }
}
</style>
