<?php

namespace App\Models\PegCenter;

use App\Models\CustomerCenter\Customer;
use App\Models\PinCenter\Pin;
use App\Models\ProductCenter\Product;
use App\Models\RiceCenter\Rice;
use App\Models\ShopCenter\Shop;
use App\Models\StoreCenter\Store;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Database\Factories\PegCenter\PegFactory;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Kalnoy\Nestedset\NodeTrait;

/**
 * Peg 模型
 * @package App\Models\PegCenter
 * @property int $uuid       主键ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @method static Builder|Peg query() 开始查询
 * @method static Builder|Peg where($column, $operator = null, $value = null) 条件查询
 */
class Peg extends Model
{
    /** @use HasFactory<PegFactory> */
    use HasFactory;
    use GeneratesUuid;
    use SoftDeletes;
    use NodeTrait;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'peg_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array<int, string>
     */
    protected $fillable = [
        //
    ];

    /**
     * 隐藏的属性（不会在数组/JSON中显示）
     * @var array<int, string>
     */
    protected $hidden = [
        //
    ];

    /**
     * 应该被转换成原生类型的属性
     * @var array<string, string>
     */
    protected $casts = [
        // 'options' => 'array',
        // 'is_active' => 'boolean',
        // 'price' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'rices.rice_number' => 10,
            'rices.rice_subject' => 10,
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $riceStatuses = [
            'new_count' => RiceStateEnum::new()->value,
            'category_count' => RiceStateEnum::category()->value,
            'schema_count' => RiceStateEnum::schema()->value,
            'store_count' => RiceStateEnum::store()->value,
            'product_count' => RiceStateEnum::product()->value,
            'word_count' => RiceStateEnum::word()->value,
            'image_count' => RiceStateEnum::image()->value,
            'ready_count' => RiceStateEnum::ready()->value,
            'change_count' => RiceStateEnum::change()->value,
            'progress_count' => RiceStateEnum::progress()->value,
            'spare_count' => RiceStateEnum::spare()->value,
        ];
        //
        //dd($riceStatuses);
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('rice_state', $riceStatuses);
    }

    /**
     * @return BelongsToMany
     */
    public function rices()
    {
        //
        return $this->belongsToMany(Rice::class, 'peg_center.peg_by_rice'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function pins()
    {
        //
        return $this->belongsToMany(Pin::class, 'peg_center.peg_by_pin'); // 定义多对多关系
    }


    /**
     * @return BelongsToMany
     */
    public function shops()
    {
        //
        return $this->belongsToMany(Shop::class, 'peg_center.peg_by_shop'); // 定义多对多关系
    }


    /**
     * @return BelongsToMany
     */
    public function stores()
    {
        //
        return $this->belongsToMany(Store::class, 'peg_center.peg_by_store'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function products()
    {
        //
        return $this->belongsToMany(Product::class, 'peg_center.peg_by_product'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function customers()
    {
        //
        return $this->belongsToMany(Customer::class, 'peg_center.peg_by_customer'); // 定义多对多关系
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
