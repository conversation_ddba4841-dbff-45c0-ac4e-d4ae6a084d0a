<?php

namespace App\Services\Chain\Rice;

use App\Enums\State\RiceStateEnum;
use App\Models\RiceCenter\Rice;
use App\Models\RiceCenter\RiceCorn;
use App\Models\RiceCenter\RiceProp;
use App\Models\RiceCenter\RiceSeed;
use App\Services\BaseService;

class RiceService extends BaseService
{
    /**
     * 将阿里巴巴的JSON数据转换为Rice, RiceCorn, RiceSeed模型
     * @param array $jsonData - 阿里巴巴传递的JSON数据
     * @return Rice - 返回创建的Rice实例
     */
    public static function createRiceFromAlibabaData($Beast)
    {
        // 验证数据格式
        $Rice = Rice::where('pin_no', $Beast->plant_no)->first();
        $jsonData = $Beast->rice_json;
        //
        if ($Beast->rice_json && $Rice) {
            //
            if (isset($jsonData['formValues'])) {
                //
                $formValues = $jsonData['formValues'];
                // 创建一个新的Rice实例
                $Rice->platform_id = config('app.platform_id'); // 设置平台ID
                $Rice->rice_title = $formValues['title'] ?? $Rice->rice_title; // 设置产品标题
                $Rice->rice_image = $formValues['primaryPicture']['imageList'][0]['url'] ?? null; // 设置主图
                $Rice->rice_description = $formValues['description']['detailList'][0]['content'] ?? null; // 设置描述
                $Rice->rice_descries = isset($formValues['description']['detailList'][0]['content']) ? self::processDescribes($formValues['description']['detailList'][0]['content']) : null; // 设置描述内容并提取图片
                $Rice->length = $formValues['volume']['length'] ?? 12; // 设置长度
                $Rice->width = $formValues['volume']['width'] ?? 10; // 设置宽度
                $Rice->height = $formValues['volume']['height'] ?? 3.5; // 设置高度
                $Rice->weight = $formValues['weight'] ?? $formValues['suttleWeight']; // 设置重量
                $Rice->cbu_unit = $formValues['cbuUnit']['unit'] ?? '个'; // 设置单位
                $Rice->total_sales = $formValues['totalSales'] ?? 420; // 设置总销售量
                $Rice->price_ranges = $formValues['priceRange'] ?? $Rice->price_ranges; // 设置价格区间
                $Rice->min_price = self::getMinPriceFromSku($formValues); // 设置最小价格
                $Rice->max_price = self::getMaxPriceFromSku($formValues); // 设置最大价格
                $Rice->quotation_type = $formValues['quotationType']['value'] ?? 2; // 设置报价类型
                $Rice->begin_amount = $formValues['beginAmount'] ?? 2; // 设置最小起订量
                $Rice->freight_no = $formValues['freight']['freightId'] ?? $Rice->freight_no; // 设置运费ID
                $Rice->freight_type = $formValues['freight']['freightType'] ?? 'T'; // 设置运费类型
                $Rice->online_trade = $formValues['onlineTrade']['value'] ?? 17410; // 设置是否支持线上交易
                $Rice->light_custom = $formValues['lightCustom']['value'] ?? -9999; // 设置是否支持自定义
                $Rice->shelf_time = $formValues['upshelfTime']['value'] ?? 1; // 设置上架时间
                $Rice->cbu_send_address = $formValues['cbuSendAddress']['value'] ?? 645456262; // 设置发货地址
                $Rice->total_sales2 = $formValues['totalSales2'] ?? 0; // 设置总销售量2
                $Rice->user_category = $formValues['userCategory'] ?? []; // 设置用户分类
                $Rice->detail_video = $formValues['detailVideo'] ?? 0; // 设置详情视频
                $Rice->primary_video = $formValues['primaryVideo'] ?? []; // 设置主视频
                $Rice->rice_state = RiceStateEnum::expand(); // 设置主视频
                $Rice->save(); // 保存Rice实例
                //
                $Beast->rice_state = RiceStateEnum::expand(); // 设置主视频
                $Beast->save(); // 保存Rice实例
                // 处理销售属性（saleProp）
                if (isset($formValues['saleProp'])) {
                    foreach ($formValues['saleProp'] as $seedPropKey => $seedPropValues) {
                        foreach ($seedPropValues as $seedPropValue) {
                            RiceSeed::updateOrCreate(
                                [
                                    'rice_id' => $Rice->id,
                                    'seed_name' => $seedPropKey,
                                    'seed_value' => $seedPropValue['value'] ?? '',
                                ],
                                [
                                    'category_no' => $Rice->category_no ?? null,
                                    'seed_text' => $seedPropValue['text'] ?? '',
                                    'seed_custom' => isset($seedPropValue['custom']) ? filter_var($seedPropValue['custom'], FILTER_VALIDATE_BOOLEAN) : false,
                                    'seed_image_url' => $seedPropValue['imgUrl'] ?? null,
                                ]);
                        }
                    }
                }

                // 处理SKU数据（RiceSeed）
                if (isset($formValues['skuTable'])) {
                    //
                    foreach ($formValues['skuTable'] as $sku) {
                        RiceCorn::updateOrCreate(
                            [
                                'rice_id' => $Rice->id,
                                'corn_key' => $sku['key'],
                            ],
                            [
                                'category_no' => $Rice->category_no ?? null,
                                'corn_no' => $sku['sku_skuId'],
                                'corn_price' => $sku['sku_price'],
                                'corn_take_price' => $sku['sku_takePrice'],
                                'corn_values' => $sku['values'],
                                'corn_props' => $sku['sku_props'],
                                'corn_sale' => $sku['sku_amountOnSale'],
                                'corn_process' => $sku['sku_amountOnProcess'],
                                'corn_sn' => $sku['sku_cargoNumber'] ?? '',
                                'corn_weight' => $sku['weight'] ?? 0,
                                'corn_length' => $sku['length'] ?? 0,
                                'corn_width' => $sku['width'] ?? 0,
                                'corn_height' => $sku['height'] ?? 0,
                                'corn_status' => $sku['sku_status'] ?? 1,
                            ]);
                    }
                }
                // 获取所有的 rice_corns
                $rice_corns = $Rice->rice_corns;
                //
                if (isset($rice_corns)) {
                    //
                    foreach ($rice_corns as $rice_corn) {
                        // 获取 corn_values
                        $corn_values = $rice_corn->corn_values;
                        // 获取所有相关的 RiceSeed 数据
                        $RiceSeeds = RiceSeed::where('rice_id', $Rice->id)->whereIn('seed_value', $corn_values)->get();
                        // 使用 syncWithoutDetaching 方法批量更新关联
                        if (count($RiceSeeds) > 0) {
                            //
                            $rice_corn->rice_corn_seeds()->syncWithoutDetaching($RiceSeeds->pluck('id')->toArray());
                        }
                    }
                }
                // 处理商品分类属性（catProp）
                if (isset($formValues['catProp'])) {
                    foreach ($formValues['catProp'] as $catNo => $catProp) {
                        //
                        //dd($formValues['catProp']);
                        //dd(count($catProp));
                        if (isset($catProp) && is_array($catProp)) {
                            //
                            foreach ($catProp as $cat) {
                                //
                                RiceProp::updateOrCreate(
                                    [
                                        'rice_id' => $Rice->id,
                                        'category_no' => $Rice->category_no ?? null,
                                        'prop_no' => $catNo,
                                        'prop_text' => $cat['text'] ?? null,
                                    ],
                                    [
                                        'prop_value' => $cat['value'] ?? null,
                                        'prop_name' => $cat['propName'] ?? null,
                                    ]);
                                //
                                if ($catNo === 'p-1398' || $catNo === 'custom') {
                                    //
                                    if (isset($cat) && is_string($cat['text'])) {
                                        //
                                        $Rice->rice_number = $cat['text'] ?? null; // 设置主视频
                                        $Rice->save(); // 保存Rice实例
                                    }
                                }
                            }
                        } else {
                            //
                            if ($catNo === 'p-1398') {
                                //
                                if (isset($cat) && is_string($cat)) {
                                    //
                                    $Rice->rice_number = $cat ?? null; // 设置主视频
                                    $Rice->save(); // 保存Rice实例
                                }
                            }
                        }
                    }
                }
            }
        }
        //
        return $Rice; // 返回创建的Rice实例
    }

    /**
     * 从SKU信息中获取最小价格
     * @param array $formValues - 表单数据
     * @return float - 最小价格
     */
    private static function getMinPriceFromSku($formValues)
    {
        //
        $minPrice = null;
        //
        if (isset($formValues['skuTable']) && count($formValues['skuTable']) > 0) {
            $prices = [];
            foreach ($formValues['skuTable'] as $sku) {
                $prices[] = $sku['sku_price'];
            }
            $minPrice = min($prices);
        }
        return $minPrice;
    }

    /**
     * 从SKU信息中获取最大价格
     * @param array $formValues - 表单数据
     * @return float - 最大价格
     */
    private static function getMaxPriceFromSku($formValues)
    {
        $maxPrice = null;
        if (isset($formValues['skuTable']) && count($formValues['skuTable']) > 0) {
            $prices = [];
            foreach ($formValues['skuTable'] as $sku) {
                $prices[] = $sku['sku_price'];
            }
            $maxPrice = max($prices);
        }
        return $maxPrice;
    }
}
