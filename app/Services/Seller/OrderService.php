<?php

namespace App\Services\Seller;

use App\Enums\State\OrderMainStateEnum;
use App\Enums\State\OrderStateEnum;
use App\Enums\Type\OrderTypeEnum;
use App\Http\Resources\Seller\Order\OrderResource;
use App\Services\BaseService;

class OrderService extends BaseService
{
    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderCountData($Store)
    {
        //
        $order_counts = [
            //
            'order_process_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderProcessing())->where('waybill_vest', 1)->count() ?? 0,
            'order_pick_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderPicking())->where('waybill_vest', 1)->count() ?? 0,
            'order_ship_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderShipping())->where('waybill_vest', 1)->where('order_type', OrderTypeEnum::SpreadOrder())->count() ?? 0,
            //
            'order_shipment_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderShipment())->where('waybill_vest', 1)->where('order_type', OrderTypeEnum::ShipperOrder())->count() ?? 0,
            'order_transport_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderTransport())->where('waybill_vest', 1)->where('order_type', OrderTypeEnum::ShipperOrder())->count() ?? 0,
            //
            'order_distribution_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderDistribution())->where('waybill_vest', 1)->count() ?? 0,
            'order_settlement_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderSettlement())->where('waybill_vest', 1)->count() ?? 0,
            'order_created_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderCreated())->where('waybill_vest', 1)->count() ?? 0,
            //
            'order_cancel_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderException())->where('waybill_vest', 1)->count() ?? 0,
            'order_refund_count' => $Store->orders()->where('is_lock', 0)->where('main_state', OrderMainStateEnum::OrderException())->where('waybill_vest', 1)->count() ?? 0,
            'order_lock_count' => $Store->orders()->where('is_lock', 1)->where('waybill_vest', 1)->count() ?? 0,
        ];
        //
        return $order_counts;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderStateTypeList($Store, $state_type)
    {
        //
        if ($state_type == 'created') {
            //
            $orders = OrderService::getStoreByOrderCreatedList($Store);
        } elseif ($state_type == 'process') {
            //
            $orders = OrderService::getStoreByOrderProcessList($Store);
        } elseif ($state_type == 'pick') {
            //
            $orders = OrderService::getStoreByOrderPickList($Store);
        } elseif ($state_type == 'ship') {
            //
            $orders = OrderService::getStoreByOrderShipList($Store);
        } elseif ($state_type == 'shipment') {
            //
            $orders = OrderService::getStoreByOrderShipmentList($Store);
        } elseif ($state_type == 'transport') {
            //
            $orders = OrderService::getStoreByOrderTransportList($Store);
        } elseif ($state_type == 'distribution') {
            //
            $orders = OrderService::getStoreByOrderDistributionList($Store);
        } elseif ($state_type == 'settlement') {
            //
            $orders = OrderService::getStoreByOrderSettlementList($Store);
        } elseif ($state_type == 'cancel') {
            //
            $orders = OrderService::getStoreByOrderCancelList($Store);
        } elseif ($state_type == 'lock') {
            //
            $orders = OrderService::getStoreByOrderLockList($Store);
        }
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderCreatedList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', OrderMainStateEnum::OrderCreated())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderProcessList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', OrderMainStateEnum::OrderProcessing())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderPickList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', OrderMainStateEnum::OrderPicking())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderShipList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('main_state', OrderMainStateEnum::OrderShipping())
            ->where('order_type', OrderTypeEnum::SpreadOrder())
            ->where('waybill_vest', 1)
            ->where('is_lock', 0)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderShipmentList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', OrderMainStateEnum::OrderShipment())
            ->where('order_type', OrderTypeEnum::ShipperOrder())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')
            ->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderTransportList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', OrderMainStateEnum::OrderTransport())
            ->where('order_type', OrderTypeEnum::ShipperOrder())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderDistributionList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', OrderMainStateEnum::OrderDistribution())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderSettlementList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', OrderMainStateEnum::OrderSettlement())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderCancelList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', "=>", OrderStateEnum::OrderCreated())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderLockList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 1)
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByOrderRefundList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))
            ->where('is_lock', 0)
            ->where('main_state', "=>", OrderStateEnum::OrderCreated())
            ->where('waybill_vest', 1)
            ->orderBy('id', 'desc')->paginate(15);
        $orders = OrderResource::collection($Order)->resource;
        //
        return $orders;
    }
}
