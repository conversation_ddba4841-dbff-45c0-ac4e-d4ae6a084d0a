<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * Class ProductLackStateEnum
 * @package App\Enums\State\Product
 * @method static self default() 默认状态
 * @method static self Processed() 已处理状态
 * @method static self Unprocessed() 未处理状态
 */
final class IsLackEnum extends BaseEnum
{


    //
    protected static function values(): array
    {
        return [
            'default' => 0,
            'Processed' => 0,
            'Unprocessed' => 1,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'Processed' => '已处理',
            'Unprocessed' => '未处理',
        ];
    }
}
