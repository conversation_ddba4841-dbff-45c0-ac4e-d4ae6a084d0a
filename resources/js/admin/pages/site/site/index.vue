<!--
/**
 * 站点设置管理页面
 *
 * 功能特性：
 * - 管理网站基础配置和设置
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/site/site
 * 页面标题：站点设置
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="site-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索站点名称、域名或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="站点状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in SITE_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="站点类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="主站" value="main"></el-option>
                                <el-option label="子站" value="sub"></el-option>
                                <el-option label="测试站" value="test"></el-option>
                                <el-option label="开发站" value="dev"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 站点列表 -->
            <template #default>
                <SiteListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总站点: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import SiteListView from './components/SiteListView.vue'

export default {
    name: 'AdminSiteSiteIndexPage',
    components: {
        BackendPageListLayout,
        SiteListView
    },
    data() {
        return {
            // 加载状态
            v_loading: false,
            loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部站点', icon: 'fas fa-list', badge: 0},
                {name: 'active', label: '活跃站点', icon: 'fas fa-check-circle', badge: 0},
                {name: 'inactive', label: '停用站点', icon: 'fas fa-times-circle', badge: 0},
                {name: 'maintenance', label: '维护中', icon: 'fas fa-wrench', badge: 0}
            ],

            // 数据相关
            dataList: [],
            selectedRows: [],

            // 状态标签映射
            SITE_STATUS_LABELS: {
                'active': '活跃',
                'inactive': '停用',
                'maintenance': '维护中',
                'pending': '待审核'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟站点数据
                const mockData = [
                    {
                        id: 1,
                        name: '主站点',
                        domain: 'www.example.com',
                        type: 'main',
                        status: 'active',
                        description: '企业主站点，提供核心业务服务',
                        created_at: '2024-01-01 10:00:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 2,
                        name: '移动端站点',
                        domain: 'm.example.com',
                        type: 'sub',
                        status: 'active',
                        description: '移动端专用站点，优化移动体验',
                        created_at: '2024-01-05 09:15:00',
                        updated_at: '2024-01-14 16:20:00'
                    },
                    {
                        id: 3,
                        name: '测试站点',
                        domain: 'test.example.com',
                        type: 'test',
                        status: 'maintenance',
                        description: '用于功能测试和开发调试',
                        created_at: '2024-01-10 11:30:00',
                        updated_at: '2024-01-15 10:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 25 // 模拟总数

                // 更新Tab徽章
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.dataList.filter(item => item.status === 'active').length
            this.tabOptions[2].badge = this.dataList.filter(item => item.status === 'inactive').length
            this.tabOptions[3].badge = this.dataList.filter(item => item.status === 'maintenance').length
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的站点吗？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleEdit(row) {
            this.$message.info(`编辑站点: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除站点 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        handleStatusChange(row, status) {
            this.$message.success(`站点状态已更新为: ${this.SITE_STATUS_LABELS[status]}`)
            this.loadData()
        },

        handleViewDetail(row) {
            this.$message.info(`查看站点详情: ${row.name}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.site-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;
        align-items: center;
        color: #666;
        font-size: 14px;

        .stat-item {
            strong {
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
