<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class UserPickupTypeEnum
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self Individual()
 * @method static self ProcurementDepartment()
 * @method static self LogisticsDepartment()
 * @method static self OperationsDepartment()
 * @method static self Customer()
 */
final class UserTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'Individual' => 1,
            'ProcurementDepartment' => 2,
            'LogisticsDepartment' => 3,
            'OperationsDepartment' => 4,
            'Customer' => 5,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'Individual' => '个人',
            'ProcurementDepartment' => '采购部',
            'LogisticsDepartment' => '物流部',
            'OperationsDepartment' => '运营部',
            'Customer' => '客户',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        //
        return [
            0 => '',
            1 => '',
            2 => 'success',
            3 => 'info',
            4 => 'warning',
            5 => 'danger',
            6 => 'info',
        ];
    }
}
