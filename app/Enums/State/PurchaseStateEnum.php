<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * Class PurchaseStateEnum
 * 用于表示购买状态的枚举类，涵盖了订单的不同状态，如待处理、已批准、已拒绝、已发货等。
 * 该枚举类包括四大类状态：
 * - 初始状态（1-10）
 * - 订单处理状态（11-30）
 * - 财务处理状态（31-40）
 * - 发货状态（41-50）
 * - 质量检查状态（51-60）
 * - 财务后续处理状态（61-70）
 * - 退货和退款状态（71-80）
 * - 特殊处理状态（81-90）
 * - 审查与反馈状态（91-100）
 * @package App\Enums\State\Order
 * @method static self default() 默认状态
 * @method static self pending() 待处理
 * @method static self approved() 已批准
 * @method static self rejected() 已拒绝
 * @method static self awaiting_approval() 等待批准
 * @method static self ordered() 已下单
 * @method static self backordered() 缺货待发
 * @method static self awaiting_reorder() 等待重新订购
 * @method static self processing() 处理中
 * @method static self customization_required() 需要定制
 * @method static self partial_shipped() 部分发货
 * @method static self awaiting_payment() 待付款
 * @method static self payment_received() 已收到付款
 * @method static self payment_confirmed() 付款已确认
 * @method static self packing() 打包中
 * @method static self ready_for_shipment() 准备发货
 * @method static self awaiting_shipment() 等待发货
 * @method static self shipped() 已发货
 * @method static self out_for_delivery() 配送中
 * @method static self delivery_attempted() 尝试配送
 * @method static self delivered() 已送达
 * @method static self delivered_partial() 部分送达
 * @method static self hold_for_quality() 因质量问题暂挂
 * @method static self quality_check() 质量检查中
 * @method static self awaiting_inspection() 等待检查
 * @method static self inspection_required() 需要检查
 * @method static self supplier_review() 供应商审查中
 * @method static self awaiting_documentation() 等待文档
 * @method static self awaiting_return_approval() 待退货批准
 * @method static self returned_to_vendor() 退还供应商
 * @method static self returned() 已退货
 * @method static self refund_pending() 退款待处理
 * @method static self refund_processed() 退款已处理
 * @method static self on_hold() 暂停处理
 * @method static self canceled() 已取消
 * @method static self disputed() 争议中
 * @method static self under_investigation() 调查中
 * @method static self compliance_check() 合规检查
 * @method static self awaiting_customs_clearance() 等待海关清关
 * @method static self expedited() 加急处理
 * @method static self invoiced() 已开发票
 * @method static self reconciled() 已对账
 * @method static self awaiting_feedback() 等待反馈
 * @method static self under_review() 审查中
 * @method static self completed() 已完成
 * @method static self awaiting_correction() 等待修正
 * @method static self resubmitted() 已重新提交
 */
final class PurchaseStateEnum extends BaseEnum
{
    // 定义状态值并进行分组整理和排序
    protected static function values(): array
    {
        return [
            // 默认状态 0
            'default' => 0,

            // 初始状态 1-10
            'pending' => 1,
            'approved' => 2,
            'rejected' => 3,
            'awaiting_approval' => 4,

            // 订单处理状态 11-30
            'ordered' => 11,
            'backordered' => 12,
            'awaiting_reorder' => 13,
            'processing' => 14,
            'customization_required' => 15,
            'partial_shipped' => 16,

            // 财务处理状态 31-40
            'awaiting_payment' => 31,
            'payment_received' => 32,
            'payment_confirmed' => 33,

            // 发货状态 41-50
            'packing' => 41,
            'ready_for_shipment' => 42,
            'awaiting_shipment' => 43,
            'shipped' => 44,
            'out_for_delivery' => 45,
            'delivery_attempted' => 46,
            'delivered' => 47,
            'delivered_partial' => 48,

            // 质量检查状态 51-60
            'hold_for_quality' => 51,
            'quality_check' => 52,
            'awaiting_inspection' => 53,
            'inspection_required' => 54,
            'supplier_review' => 55,
            'awaiting_documentation' => 56,

            // 财务后续处理状态 61-70
            'invoiced' => 61,
            'reconciled' => 62,

            // 退货和退款状态 71-80
            'awaiting_return_approval' => 71,
            'returned_to_vendor' => 72,
            'returned' => 73,
            'refund_pending' => 74,
            'refund_processed' => 75,

            // 特殊处理状态 81-90
            'on_hold' => 81,
            'canceled' => 82,
            'disputed' => 83,
            'under_investigation' => 84,
            'compliance_check' => 85,
            'awaiting_customs_clearance' => 86,
            'expedited' => 87,

            // 审查与反馈状态 91-100
            'awaiting_feedback' => 91,
            'under_review' => 92,
            'awaiting_correction' => 93, // 新增状态：等待修正
            'resubmitted' => 94, // 新增状态：已重新提交

            // 结束状态 101-110
            'completed' => 100,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            // 默认状态
            'default' => '默认状态',

            // 初始状态
            'pending' => '待处理',
            'approved' => '已批准',
            'rejected' => '已拒绝',
            'awaiting_approval' => '等待批准',

            // 订单处理状态
            'ordered' => '已下单',
            'backordered' => '缺货待发',
            'awaiting_reorder' => '等待重新订购',
            'processing' => '处理中',
            'customization_required' => '需要定制',
            'partial_shipped' => '部分发货',

            // 财务处理状态
            'awaiting_payment' => '待付款',
            'payment_received' => '已收到付款',
            'payment_confirmed' => '付款已确认',

            // 发货状态
            'packing' => '打包中',
            'ready_for_shipment' => '准备发货',
            'awaiting_shipment' => '等待发货',
            'shipped' => '已发货',
            'out_for_delivery' => '配送中',
            'delivery_attempted' => '尝试配送',
            'delivered' => '已送达',
            'delivered_partial' => '部分送达',

            // 质量检查状态
            'hold_for_quality' => '因质量问题暂挂',
            'quality_check' => '质量检查中',
            'awaiting_inspection' => '等待检查',
            'inspection_required' => '需要检查',
            'supplier_review' => '供应商审查中',
            'awaiting_documentation' => '等待文档',

            // 财务后续处理状态
            'invoiced' => '已开发票',
            'reconciled' => '已对账',

            // 退货和退款状态
            'awaiting_return_approval' => '待退货批准',
            'returned_to_vendor' => '退还供应商',
            'returned' => '已退货',
            'refund_pending' => '退款待处理',
            'refund_processed' => '退款已处理',

            // 特殊处理状态
            'on_hold' => '暂停处理',
            'canceled' => '已取消',
            'disputed' => '争议中',
            'under_investigation' => '调查中',
            'compliance_check' => '合规检查',
            'awaiting_customs_clearance' => '等待海关清关',
            'expedited' => '加急处理',

            // 审查与反馈状态
            'awaiting_feedback' => '等待反馈',
            'under_review' => '审查中',
            'awaiting_correction' => '等待修正',
            'resubmitted' => '已重新提交',

            // 结束状态
            'completed' => '已完成',
        ];
    }
}
