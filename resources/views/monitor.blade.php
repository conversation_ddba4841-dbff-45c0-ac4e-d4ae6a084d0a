@extends('layouts.app')

{{-- 页面特定脚本 --}}
@push('scripts')
    {{-- 监控Vue应用 --}}
    @vite(['resources/js/monitor/monitor.js'])
@endpush

{{-- 企业级监控页面 - 基于app.blade.php布局模板 --}}
@section('content')
    {{-- 设置页面变量 --}}
    @php
        $title = $title ?? '监控中心';
        $description = $description ?? '实时监控系统，提供商店监控、Pin监控、Rice监控等功能，确保系统稳定运行';
        $keywords = $keywords ?? '监控中心,商店监控,Pin监控,Rice监控,实时监控,系统监控';
        $pageTitle = $pageTitle ?? 'WorkHub - 监控中心';
        $pageDescription = $pageDescription ?? '专业的实时监控解决方案';
        $menuActive = $menuActive ?? 'monitor';
    @endphp

    {{-- 企业级主容器 - 完整响应式布局系统 --}}
    <div id="app" v-cloak class="min-h-screen flex flex-col responsive-container enterprise-container">
        {{-- 企业级顶部导航栏 - 黑色主题 --}}
        @include('layouts.partials.top-navigation')

        {{-- 企业级主要内容区域 - 完整响应式容器系统 --}}
        <main class="flex-1 page-content responsive-main enterprise-main" id="main-content" role="main"
              aria-label="主要内容区域">
            {{-- 企业级页面内容 - 响应式间距和布局 --}}
            <div class="space-y-6 responsive-content enterprise-content">
                {{-- Vue监控应用挂载点 --}}
                <MonitorFacade data-route-active="{{ $menuActive }}">
                    {{-- Vue Monitor组件将在这里挂载 --}}
                </MonitorFacade>
            </div>
        </main>
        {{-- 企业级底部版权栏 - 与顶部导航栏风格一致 --}}
        @include('layouts.partials.bottom-copyright')
    </div>
@endsection
