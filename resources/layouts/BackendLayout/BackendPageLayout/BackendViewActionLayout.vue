<template>
    <div class="backend-page-action-layout">
        <div class="page-container">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'BackendViewActionLayout'
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

.backend-page-action-layout {
    min-height: calc(100vh - 140px);
    padding: 20px;
    background-color: #f5f5f5;

    .page-container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #ebeef5;
        overflow: hidden;
    }
}

@media (max-width: 768px) {
    .backend-page-action-layout {
        padding: 16px;

        .page-container {
            margin: 0;
            border-radius: 6px;
        }
    }
}
</style>
