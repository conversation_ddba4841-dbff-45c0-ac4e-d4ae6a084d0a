<?php

namespace App\Services\Lesson;

use App\Enums\State\ProductStateEnum;
use App\Http\Resources\Advocate\Product\ProductResource;
use App\Models\ProductCenter\Product;
use App\Services\BaseService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class ProductService extends BaseService
{
    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus()
    {
        // 1. 计算产品的统计数据 (CountData)
        $product_counts = [
            'online_count' => Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::online())->count(),
            'release_count' => Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::release())->count(),
            'unavailable_count' => Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::unavailable())->count(),
            'discontinued_count' => Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::discontinued())->count(),
            'lock_count' => Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::frozen())->count(),
        ];
        //
        return $product_counts;
    }


    /**
     * 获取筛选后的产品列表
     * @param string|null $keyword
     * @param array $filters
     * @param string $is_state
     * @param string|null $sortBy
     * @param string|null $order
     * @return AnonymousResourceCollection
     */
    public static function getFilteredProductList(
        array  $filters = [],
        string $is_state = '1', // 默认状态
        string $sortBy = null,
        string $order = null
    )
    {
        // 开启查询日志（仅调试时使用）
        DB::enableQueryLog();
        // 构建基础查询
        $query = Product::query();
        // 关键词搜索
        if (isset($filters['keyword'])) {
            //
            $query->search($filters['keyword']);
        }
        // 日期过滤
        if (isset($filters['date_from'])) {
            //
            $query->whereDate('products.created_at', '>=', $filters['date_from']);
        }
        if (isset($filters['date_to'])) {
            //
            $query->whereDate('products.created_at', '<=', $filters['date_to']);
        }
        // 状态过滤：明确指定表名
        $query->where('products.product_state', $is_state);
        // 排序
        if ($sortBy && in_array($sortBy, ['product_title', 'created_at', 'updated_at'])) {
            //
            $sortBy = 'products.' . $sortBy;
            $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
            $query->orderBy($sortBy, $order);
        } else {
            //
            $query->orderBy('products.created_at', 'desc');
        }
        // 添加 distinct 以避免重复
        $query->distinct();
        // 分页
        $products = $query->paginate(15)->withQueryString()->fragment(md5('page'));
        // 返回结果
        return ProductResource::collection($products)->resource;
    }

    /**
     * 获取待审核产品的列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getProductOnlineList()
    {
        // 2. 获取待审核产品列表，并进行分页
        $Product = Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::online())->paginate(15);
        // 使用 ProductResource 进行资源封装和返回
        return ProductResource::collection($Product)->resource;
    }

    /**
     * 获取已审核产品的列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getProductOfflineList()
    {
        // 3. 获取已审核产品列表，并进行分页
        $Product = Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::unavailable())->paginate(15);
        // 使用 ProductResource 进行资源封装和返回
        return ProductResource::collection($Product)->resource;
    }

    /**
     * 获取拒绝的产品列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getProductClosureList()
    {
        // 4. 获取拒绝的产品列表，并进行分页
        $Product = Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::discontinued())->paginate(15);
        // 使用 ProductResource 进行资源封装和返回
        return ProductResource::collection($Product)->resource;
    }

    /**
     * 获取拒绝的产品列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getProductfrozenList()
    {
        // 4. 获取拒绝的产品列表，并进行分页
        $Product = Product::where('platform_id', '=', config('app.platform_id'))->where('product_state', ProductStateEnum::frozen())->paginate(15);
        // 使用 ProductResource 进行资源封装和返回
        return ProductResource::collection($Product)->resource;
    }

    /**
     * 获取单个产品的详细信息
     * @param string $uuid
     * @return ProductResource
     */
    public static function getProductByUuid(string $uuid)
    {
        //
        $Product = Product::where('uuid', $uuid)->first();
        $Product = new ProductResource($Product);
        //
        return $Product;
    }
}
