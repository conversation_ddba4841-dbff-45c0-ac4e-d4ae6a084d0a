<?php

namespace App\Exceptions\Request;

use App\Helpers\ApiResponse;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator as ValidatorFacade;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class RequestValidationException extends ValidationException
{
    use ApiResponse;

    /**
     * The summarized error message.
     * @var string
     */
    protected $summaryMessage;

    /**
     * Create a new validation exception instance.
     * @param Validator $validator
     * @param Response|null $response
     * @param string $errorBag
     * @return void
     */
    public function __construct($validator, $response = null, $errorBag = 'default')
    {
        parent::__construct($validator, $response, $errorBag);

        // Store the summarized message
        $this->summaryMessage = static::summarize($validator);
    }

    /**
     * Summarize the validation errors.
     * @param Validator $validator
     * @return string
     */
    protected static function summarize($validator)
    {
        $messages = $validator->errors()->all();

        if (!count($messages) || !is_string($messages[0])) {
            return $validator->getTranslator()->get('The given data was invalid.');
        }

        $message = array_shift($messages);

        if ($count = count($messages)) {
            $pluralized = $count === 1 ? 'error' : 'errors';
            $message .= ' ' . $validator->getTranslator()->choice("(and :count more $pluralized)", $count, compact('count'));
        }

        return $message;
    }

    /**
     * Create a new validation exception from a plain array of messages.
     * @param array $messages
     * @return static
     */
    public static function withMessages(array $messages)
    {
        $validator = tap(ValidatorFacade::make([], []), function ($validator) use ($messages) {
            foreach ($messages as $key => $value) {
                foreach (Arr::wrap($value) as $message) {
                    $validator->errors()->add($key, $message);
                }
            }
        });

        return new static($validator);
    }

    /**
     * Render the exception into an HTTP response.
     * @param Request $request
     * @return JsonResponse
     */
    public function render($request)
    {
        //
        return response()->json([
            'status' => 'fail',
            'code' => 422, // 这里可以自定义你的错误码
            'data' => [], // 如果有额外数据可以放在这里
            'info' => $this->validator->errors()->messages(), // 将错误信息放在 customInfo 字段中
            'message' => $this->summaryMessage, // 包含 summarized message
        ], 202); // 这里设置 HTTP 状态码为 202
    }
}
