<?php

namespace App\Notifications\Platform\Advocate\Product;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdvocateProductDiscontinuedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $office_template;

    /**
     * Create a new notification instance.
     * 创建一个新的通知实例
     */
    public
    function __construct()
    {
        //
        $this->office_template = 'VaxFf_m3iuz12epDuGoiV3GA2d7N95V9sNuZK109LnU';
    }

    /**
     * Get the notification's delivery channels.
     * 获取通知的传递渠道
     * @return array<int, string>
     */
    public
    function via(object $notifiable): array
    {
        //
        if ($notifiable->is_email == 1) {
            //
            $mail = 'mail';
        }

        //
        if ($notifiable->is_office == 1) {
            //
            $channel = null;
        }

        //
        return array_filter(['database', $channel ?? null, $mail ?? null]);
    }

    /**
     * Get the mail representation of the notification.
     * 获取通知的邮件表示形式
     */
    public
    function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage())
            ->line('通知简介.')
            ->action('通知动作', url('/'))
            ->line('感谢您使用我们的应用程序！');
    }

    /**
     * Get the array representation of the notification.
     * 获取通知的数组表示形式
     * @return array<string, mixed>
     */
    public
    function toArray(object $notifiable): array
    {
        return [
            //
            'user_id' => $notifiable->id ?? null,
        ];
    }
}
