<?php

namespace App\Traits\System\Models;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use InvalidArgumentException;

/**
 * Trait HasFilterableTrait
 * 提供 Eloquent Builder 的过滤功能，支持使用过滤类进行多字段过滤。
 */
trait HasFilterableTrait
{
    /**
     * 存储已应用的过滤条件。
     * @var array
     */
    protected $appliedFilters = [];

    /**
     * 作用域：应用给定的过滤条件到查询构建器。
     * @param Builder $query
     * @param array $filters 过滤参数，键为过滤字段，值为过滤值。
     * @return Builder 返回应用过滤后的查询构建器。
     * @throws InvalidArgumentException 如果过滤类不存在或处理失败。
     */
    public function scopeFilter(Builder $query, array $filters)
    {
        // 动态获取当前模型对应的过滤类。
        $filterClass = $this->getModelFilterClass();

        // 检查过滤类是否存在。
        if (class_exists($filterClass)) {
            // 实例化过滤类，并传入查询构建器和过滤参数。
            $filter = new $filterClass($query, $filters);
            // 调用过滤类的 handle 方法，应用过滤逻辑。
            $query = $filter->handle();

            // 获取过滤类中已应用的过滤条件（假设过滤类提供此方法）。
            if (method_exists($filter, 'getAppliedFilters')) {
                $this->appliedFilters = $filter->getAppliedFilters();
            } else {
                // 如果过滤类没有提供获取已应用过滤的方法，则使用传入的过滤参数。
                $this->appliedFilters = $filters;
            }

            return $query;
        }

        // 如果过滤类不存在，则抛出异常。
        throw new InvalidArgumentException("过滤类 {$filterClass} 不存在。");
    }

    /**
     * 动态获取当前模型对应的过滤类。
     * 假设过滤类位于 App\Filters 命名空间，并以模型名称加上 "Filter" 结尾。
     * 例如，模型 Store 对应的过滤类为 App\Filters\StoreFilter。
     * @return string 返回过滤类的完全限定类名。
     */
    protected function getModelFilterClass(): string
    {
        // 获取当前模型的基类名称，例如 "Store"。
        $modelClass = class_basename($this);
        // 构建过滤类的完全限定类名。
        return "App\\Filters\\{$modelClass}Filter";
    }

    /**
     * 获取已应用的过滤条件。
     * @return array 返回已应用的过滤条件。
     */
    public function getAppliedFilters(): array
    {
        return $this->appliedFilters;
    }

    /**
     * 作用域：分页并附加过滤参数到分页链接。
     * @param Builder $query 查询构建器实例。
     * @param int|null $perPage 每页记录数，默认为 15。
     * @param array $columns 查询字段，默认为 ['*']。
     * @param string $pageName 页码参数名，默认为 'page'。
     * @param int|null $page 当前页码，可选。
     * @return LengthAwarePaginator 分页器实例，包含已应用的过滤参数。
     */
    public function scopePaginateFilter(Builder $query, int $perPage = null, array $columns = ['*'], string $pageName = 'page', int $page = null): LengthAwarePaginator
    {
        // 如果未指定每页记录数，则默认为 15。
        $perPage = $perPage ?? 15;
        // 执行分页操作。
        $paginator = $query->paginate($perPage, $columns, $pageName, $page);
        // 附加已应用的过滤参数到分页链接。
        $paginator->appends($this->appliedFilters);

        return $paginator;
    }

    /**
     * 作用域：简单分页并附加过滤参数到分页链接。
     * @param Builder $query 查询构建器实例。
     * @param int|null $perPage 每页记录数，默认为 15。
     * @param array $columns 查询字段，默认为 ['*']。
     * @param string $pageName 页码参数名，默认为 'page'。
     * @param int|null $page 当前页码，可选。
     * @return LengthAwarePaginator 分页器实例，包含已应用的过滤参数。
     */
    public function scopeSimplePaginateFilter(Builder $query, int $perPage = null, array $columns = ['*'], string $pageName = 'page', int $page = null): LengthAwarePaginator
    {
        // 如果未指定每页记录数，则默认为 15。
        $perPage = $perPage ?? 15;
        // 执行简单分页操作。
        $paginator = $query->simplePaginate($perPage, $columns, $pageName, $page);
        // 附加已应用的过滤参数到分页链接。
        $paginator->appends($this->appliedFilters);

        return $paginator;
    }
}
