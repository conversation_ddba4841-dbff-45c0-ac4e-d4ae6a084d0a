<!--
/**
 * 格子管理页面
 *
 * 功能特性：
 * - 管理格子布局和配置
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/lattice/lattice
 * 页面标题：格子管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="lattice-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索格子名称、位置或类型'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="格子状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in LATTICE_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="格子类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="商品展示" value="product"></el-option>
                                <el-option label="广告位" value="advertisement"></el-option>
                                <el-option label="功能模块" value="function"></el-option>
                                <el-option label="导航菜单" value="navigation"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="位置区域">
                            <el-select v-model="filters.area" clearable placeholder="选择区域">
                                <el-option label="首页顶部" value="home_top"></el-option>
                                <el-option label="首页中部" value="home_middle"></el-option>
                                <el-option label="首页底部" value="home_bottom"></el-option>
                                <el-option label="侧边栏" value="sidebar"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 格子列表 -->
            <template #default>
                <LatticeListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @preview="handlePreview"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总格子: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import LatticeListView from './components/LatticeListView.vue'

export default {
    name: 'AdminLatticeLatticeIndexPage',
    components: {
        BackendPageListLayout,
        LatticeListView
    },
    data() {
        return {
            // 加载状态
            v_loading: false,
            loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                area: ''
            },

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部格子', icon: 'fas fa-th', badge: 0},
                {name: 'active', label: '启用中', icon: 'fas fa-check-circle', badge: 0},
                {name: 'inactive', label: '已停用', icon: 'fas fa-times-circle', badge: 0},
                {name: 'draft', label: '草稿', icon: 'fas fa-edit', badge: 0}
            ],

            // 数据相关
            dataList: [],
            selectedRows: [],

            // 状态标签映射
            LATTICE_STATUS_LABELS: {
                'active': '启用',
                'inactive': '停用',
                'draft': '草稿',
                'pending': '待审核'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟格子数据
                const mockData = [
                    {
                        id: 1,
                        name: '首页轮播图',
                        type: 'advertisement',
                        type_name: '广告位',
                        status: 'active',
                        area: 'home_top',
                        area_name: '首页顶部',
                        position: 1,
                        width: 1200,
                        height: 400,
                        content_type: 'image',
                        content_url: '/images/banner1.jpg',
                        link_url: '/products/featured',
                        click_count: 1580,
                        view_count: 25600,
                        created_at: '2024-01-01 10:00:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 2,
                        name: '热门商品展示',
                        type: 'product',
                        type_name: '商品展示',
                        status: 'active',
                        area: 'home_middle',
                        area_name: '首页中部',
                        position: 2,
                        width: 800,
                        height: 600,
                        content_type: 'product_list',
                        content_url: null,
                        link_url: '/products/hot',
                        click_count: 890,
                        view_count: 18200,
                        created_at: '2024-01-05 09:15:00',
                        updated_at: '2024-01-14 16:20:00'
                    },
                    {
                        id: 3,
                        name: '快速导航菜单',
                        type: 'navigation',
                        type_name: '导航菜单',
                        status: 'draft',
                        area: 'sidebar',
                        area_name: '侧边栏',
                        position: 1,
                        width: 200,
                        height: 300,
                        content_type: 'menu',
                        content_url: null,
                        link_url: null,
                        click_count: 0,
                        view_count: 0,
                        created_at: '2024-01-10 11:30:00',
                        updated_at: '2024-01-15 10:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 22 // 模拟总数

                // 更新Tab徽章
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.dataList.filter(item => item.status === 'active').length
            this.tabOptions[2].badge = this.dataList.filter(item => item.status === 'inactive').length
            this.tabOptions[3].badge = this.dataList.filter(item => item.status === 'draft').length
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                area: ''
            }
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的格子吗？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleEdit(row) {
            this.$message.info(`编辑格子: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除格子 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        handleStatusChange(row, status) {
            this.$message.success(`格子状态已更新为: ${this.LATTICE_STATUS_LABELS[status]}`)
            this.loadData()
        },

        handleViewDetail(row) {
            this.$message.info(`查看格子详情: ${row.name}`)
        },

        handlePreview(row) {
            this.$message.info(`预览格子: ${row.name}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.lattice-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;
        align-items: center;
        color: #666;
        font-size: 14px;

        .stat-item {
            strong {
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
