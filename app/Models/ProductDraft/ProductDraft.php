<?php

namespace App\Models\ProductDraft;

use App\Casts\Amount\AmountToHundred;
use App\Enums\Is\IsClosureEnum;
use App\Enums\Is\IsEditEnum;
use App\Enums\Is\IsOfflineEnum;
use App\Enums\Method\DeliveryMethodEnum;
use App\Enums\Method\GETMethodEnum;
use App\Enums\State\ProductDraftStateEnum;
use App\Enums\State\ProductStateEnum;
use App\Enums\Status\ProductDraftStatusEnum;
use App\Enums\Type\ActivityTypeEnum;
use App\Enums\Type\BusinessTypeEnum;
use App\Enums\Type\DelayTypeEnum;
use App\Enums\Type\DiyTypeEnum;
use App\Enums\Type\HandTypeEnum;
use App\Enums\Type\KindTypeEnum;
use App\Enums\Type\LimitTypeEnum;
use App\Enums\Type\PolicyTypeEnum;
use App\Enums\Type\PreTypeEnum;
use App\Enums\Type\ProductEditTypeEnum;
use App\Enums\Type\PurchaseTypeEnum;
use App\Enums\Type\RangeTypeEnum;
use App\Enums\Type\RebateTypeEnum;
use App\Enums\Type\RetailTypeEnum;
use App\Enums\Type\SaleTypeEnum;
use App\Enums\Type\SceneTypeEnum;
use App\Enums\Type\SkuTypeEnum;
use App\Enums\Vest\OwnerVestEnum;
use App\Enums\Vest\SourceVestEnum;
use App\Models\IndustryBrace\Category;
use App\Models\ProduceBrace\ProduceSchema;
use App\Models\ProductCenter\Product;
use App\Models\ReasonBrace\Reason;
use App\Models\StoreCenter\Store;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Oddvalue\LaravelDrafts\Concerns\HasDrafts;
use Spatie\Translatable\HasTranslations;

class ProductDraft extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;
    use HasDrafts;
    use DateSerializationTrait;
    use HasTranslations;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 可翻译的属性
     * @var array
     */
    public $translatable = ['alias_names'];
    /**
     * 定义哪些字段是可排序的。
     * @var array
     */
    public $sortable = ['created_at', 'updated_at'];

    /**
     * 设置当前模型使用的数据库连接名
     * @var string
     */
    protected $connection = 'product_draft';
    /**
     * 使用有序的 UUID 版本
     * @var string
     */
    protected $uuidVersion = 'ordered';
    /**
     * 不可批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];
    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];
    /**
     * Searchable rules.
     * @var array
     */
    protected $searchable = [
        'columns' => [
            'product_drafts.product_no' => 10,
            'product_drafts.product_title' => 9,
            'product_drafts.product_english_title' => 8,
            'product_drafts.product_name' => 7,
            'product_drafts.product_sn' => 6,
            'product_drafts.product_barcode' => 5,
            // 产品表字段
            'products.product_title' => 10,
            'products.product_no' => 10,
            'products.product_english_title' => 10,
            'products.product_name' => 10,
            //
            'stores.store_name' => 10, // 确保别名正确
            'stores.stall_number' => 10, // 确保别名正确
        ],
        'joins' => [
            'store_center.stores as stores' => ['product_drafts.store_id', 'stores.id'], // 使用别名
            'product_center.products as products' => ['product_drafts.product_id', 'products.id'], // 使用别名
        ],
    ];
    /**
     * 属性类型转换
     * @var array<string, string>
     */
    protected $casts = [
        'min_price' => AmountToHundred::class,
        'max_price' => AmountToHundred::class,
        //
        'media_picture' => 'array',
        'media_images' => 'array',
        'media_descries' => 'array',
        'media_white' => 'array',
        'media_video' => 'array',
        //
        'keywords' => 'array',
        //
        'copyright_uuids' => 'array',
        'fabric_ids' => 'array',
        'design_ids' => 'array',
        'trend_ids' => 'array',
        'craft_ids' => 'array',
        'shade_ids' => 'array',
        'purpose_ids' => 'array',
        'accessory_ids' => 'array',
        //
        'suit_ids' => 'array',
        //
        'suits' => 'array',
        'specs' => 'array',
        'customs' => 'array',
        //
        'arr_suit_ids' => 'array',
        'arr_spec_ids' => 'array',
        'arr_custom_ids' => 'array',
        //
        'arr_suits' => 'array',
        'arr_specs' => 'array',
        'arr_customs' => 'array',
        'arr_skus' => 'array',
        //
        'attributes' => 'array',
        //
        'pre_type' => PreTypeEnum::class,
        'purchase_type' => PurchaseTypeEnum::class,
        'diy_type' => DiyTypeEnum::class,
        'activity_type' => ActivityTypeEnum::class,
        'scene_type' => SceneTypeEnum::class,
        'kind_type' => KindTypeEnum::class,
        'business_type' => BusinessTypeEnum::class,
        'rebate_type' => RebateTypeEnum::class,
        'spec_type' => SkuTypeEnum::class,
        'range_type' => RangeTypeEnum::class,
        'delay_type' => DelayTypeEnum::class,
        'hand_type' => HandTypeEnum::class,
        'policy_type' => PolicyTypeEnum::class,
        'limit_type' => LimitTypeEnum::class,
        'sale_type' => SaleTypeEnum::class,
        'retail_type' => RetailTypeEnum::class,
        'delivery_method' => DeliveryMethodEnum::class,
        'get_method' => GETMethodEnum::class,
        'source_vest' => SourceVestEnum::class,
        'seller_vest' => OwnerVestEnum::class,
        'product_vest' => OwnerVestEnum::class,
        'provision_vest' => OwnerVestEnum::class,
        'delivery_vest' => OwnerVestEnum::class,
        'is_edit' => IsEditEnum::class,
        'is_offline' => IsOfflineEnum::class,
        'is_closure' => IsClosureEnum::class,
        //
        'edit_type' => ProductEditTypeEnum::class,
        'product_state' => ProductStateEnum::class,
        'draft_status' => ProductDraftStateEnum::class,
        //
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取产品草稿的统计数据
     * @return array
     */
    public static function getProductDraftCount(): array
    {
        // 定义状态值到返回键名的映射
        $draftStatuses = [
            'pending_count' => ProductDraftStatusEnum::pending()->value,
            'checked_count' => ProductDraftStatusEnum::checked()->value,
            'approved_count' => ProductDraftStatusEnum::approved()->value,
            'refused_count' => ProductDraftStatusEnum::refused()->value,
        ];

        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('draft_status', $draftStatuses);
    }

    /**
     * 模型的引导方法
     * 注册模型观察者等操作
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // 为 ProductDemo 模型注册观察者
        //self::observe(ProductDemoObserver::class);
    }

    /**
     * 保存为草稿
     * @return void
     */
    public function saveAsDraft(): void
    {
        $this->saveDraft();
    }

    /**
     * 初始化模型的默认属性
     * 在保存或更新模型之前，设置一些默认属性
     * @return $this
     */
    public function syncOriginal()
    {
        //        $this->platform_id = config('app.platform_id');
        //        $this->client_ip   = request()->ip();
        return $this;
    }

    /**
     * 获取产品所属的店铺
     * @return BelongsTo 返回店铺关联
     */
    public function store(): BelongsTo
    {
        //
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    /**
     * 获取产品所属的店铺
     * @return BelongsTo 返回店铺关联
     */
    public function product(): BelongsTo
    {
        //
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    /**
     * 获取产品所属的生产模式
     * @return BelongsTo 返回生产模式关联
     */
    public function category(): BelongsTo
    {
        //
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 获取产品所属的生产模式
     * @return BelongsTo 返回生产模式关联
     */
    public function schema(): BelongsTo
    {
        return $this->belongsTo(ProduceSchema::class, 'schema_id', 'id');
    }

    /**
     * 获取产品所属的店铺分类
     * @return BelongsToMany 返回店铺分类关联
     */
    public function reasons(): BelongsToMany
    {
        //
        return $this->belongsToMany(Reason::class, 'product_draft.product_draft_by_reason')->withTimestamps();
    }
}
