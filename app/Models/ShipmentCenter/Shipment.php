<?php

namespace App\Models\ShipmentCenter;

use App\Enums\Is\IsDefaultEnum;
use App\Enums\Is\IsStateEnum;
use App\Enums\Type\InteractTypeEnum;
use App\Enums\Vest\OwnerVestEnum;
use App\Enums\Vest\ReachVestEnum;
use App\Models\ProviderCenter\Provider;
use App\Models\ShipBrace\Ship;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shipment extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'shipment_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'shipment_vest' => ReachVestEnum::class,
        'conduct_vest' => OwnerVestEnum::class,
        'interact_type' => InteractTypeEnum::class,
        'is_default' => IsDefaultEnum::class,
        'is_state' => IsStateEnum::class,
    ];

    public function delivery()
    {
        //
        return $this->morphTo();
    }

    /**
     * @return BelongsTo
     */
    public function provider()
    {
        //
        return $this->belongsTo(Provider::class, 'provider_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function ship()
    {
        //
        return $this->belongsTo(Ship::class, 'ship_id', 'id');
    }

    /**
     * @return HasOne
     */
    public function shipment_pickup()
    {
        //
        return $this->hasOne(ShipmentPickup::class);
    }

    /**
     * @return MorphMany
     */
    public function shipmentExpress()
    {
        //
        return $this->morphMany(ShipmentExpress::class, 'model');
    }

    /**
     * @return MorphMany
     */
    public function shipmentPickup()
    {
        //
        return $this->morphMany(ShipmentPickup::class, 'model');
    }

    /**
     * @return MorphMany
     */
    public function shipment_address()
    {
        //
        return $this->morphMany(ShipmentAddress::class, 'model');
    }

    /**
     * @return MorphOne
     */
    public function shipment_lattice()
    {
        //
        return $this->morphOne(ShipmentLattice::class, 'model');
    }

    /**
     * @return MorphMany
     */
    public function shipment_lattices()
    {
        //
        return $this->morphMany(ShipmentLattice::class, 'model');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
