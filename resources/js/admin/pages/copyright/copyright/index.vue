<!--
/**
 * 版权管理页面
 *
 * 功能特性：
 * - 企业级版权管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 版权状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/copyright/copyright
 * 页面标题：版权管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="copyright-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索版权名称、版权号、持有人或作品名称'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button type="success" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增版权
                </el-button>
                <el-button :loading="exportLoading" type="info" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDelete"
                    type="warning"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchExpire"
                    type="danger"
                    @click="handleBatchExpire"
                >
                    <i class="fas fa-clock"></i>
                    批量过期 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="版权状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in COPYRIGHT_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="版权类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="文字作品" value="text"></el-option>
                                <el-option label="音乐作品" value="music"></el-option>
                                <el-option label="美术作品" value="art"></el-option>
                                <el-option label="摄影作品" value="photo"></el-option>
                                <el-option label="影视作品" value="video"></el-option>
                                <el-option label="软件作品" value="software"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="申请日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item label="持有人">
                            <el-input
                                v-model="filters.holder_name"
                                clearable
                                placeholder="输入持有人姓名"
                                style="width: 200px;"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 版权列表 -->
            <template #default>
                <CopyrightListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总版权: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						有效版权: <strong>{{ validCopyrightCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import CopyrightListView from './components/CopyrightListView.vue'

export default {
    name: 'AdminCopyrightCopyrightIndexPage',
    components: {
        BackendPageListLayout,
        CopyrightListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                start_date: '',
                end_date: '',
                holder_name: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            validCopyrightCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部版权', icon: 'fas fa-copyright', badge: 0},
                {name: 'active', label: '有效版权', icon: 'fas fa-check-circle', badge: 0},
                {name: 'pending', label: '审核中', icon: 'fas fa-clock', badge: 0},
                {name: 'expired', label: '已过期', icon: 'fas fa-times-circle', badge: 0},
                {name: 'rejected', label: '已拒绝', icon: 'fas fa-ban', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 版权状态标签
            COPYRIGHT_STATUS_LABELS: {
                'active': '有效',
                'pending': '审核中',
                'expired': '已过期',
                'rejected': '已拒绝',
                'suspended': '已暂停'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchDelete() {
            return this.selectedRows.some(row => ['pending', 'rejected'].includes(row.status))
        },
        canBatchExpire() {
            return this.selectedRows.some(row => row.status === 'active')
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟版权数据（敏感信息脱敏）
                const mockData = [
                    {
                        id: 1,
                        copyright_no: 'CR202401150001',
                        work_name: '企业管理系统V1.0',
                        copyright_type: 'software',
                        holder_name: '张三',
                        holder_phone: '138****5678', // 手机号脱敏
                        holder_email: 'zha***@example.com', // 邮箱脱敏
                        agent_name: '知识产权代理有限公司',
                        status: 'active',
                        apply_date: '2024-01-15',
                        expire_date: '2074-01-15',
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        copyright_no: 'CR202401150002',
                        work_name: '原创音乐作品《春天的故事》',
                        copyright_type: 'music',
                        holder_name: '李四',
                        holder_phone: '139****1234',
                        holder_email: 'li***@example.com',
                        agent_name: '音乐版权代理中心',
                        status: 'pending',
                        apply_date: '2024-01-15',
                        expire_date: '2074-01-15',
                        created_at: '2024-01-15 11:20:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 3,
                        copyright_no: 'CR202401150003',
                        work_name: '企业宣传画册设计',
                        copyright_type: 'art',
                        holder_name: '王五',
                        holder_phone: '136****9876',
                        holder_email: 'wan***@example.com',
                        agent_name: '设计版权服务中心',
                        status: 'expired',
                        apply_date: '2020-01-15',
                        expire_date: '2024-01-15',
                        created_at: '2020-01-15 12:45:00',
                        updated_at: '2024-01-15 12:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 856
                this.validCopyrightCount = 623
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 623
            this.tabOptions[2].badge = 156
            this.tabOptions[3].badge = 45
            this.tabOptions[4].badge = 32
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                start_date: '',
                end_date: '',
                holder_name: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleCreate() {
            this.$message.info('跳转到新增版权页面')
            // TODO: 跳转到新增页面
            // this.$router.push('/admin/copyright/copyright/create')
        },

        async handleExport() {
            try {
                this.exportLoading = true

                // 模拟导出
                await new Promise(resolve => setTimeout(resolve, 2000))

                this.$message.success('版权数据导出成功')

            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchDelete() {
            const deletableItems = this.selectedRows.filter(row =>
                ['pending', 'rejected'].includes(row.status)
            )

            if (deletableItems.length === 0) {
                this.$message.warning('没有可删除的版权记录')
                return
            }

            this.$confirm(`确定要删除选中的 ${deletableItems.length} 个版权记录吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${deletableItems.length} 个版权记录`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchExpire() {
            const expirableItems = this.selectedRows.filter(row =>
                row.status === 'active'
            )

            if (expirableItems.length === 0) {
                this.$message.warning('没有可过期的版权记录')
                return
            }

            this.$confirm(`确定要将选中的 ${expirableItems.length} 个版权设置为过期吗？`, '确认批量过期', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功设置 ${expirableItems.length} 个版权为过期`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看版权详情: ${row.copyright_no}`)
            // TODO: 跳转到版权详情页面
            // this.$router.push(`/admin/copyright/copyright/detail/${row.id}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑版权: ${row.copyright_no}`)
            // TODO: 跳转到版权编辑页面
            // this.$router.push(`/admin/copyright/copyright/edit/${row.id}`)
        },

        handleStatusChange(row, status) {
            this.$confirm(`确定要将版权 "${row.copyright_no}" 状态更改为 "${this.COPYRIGHT_STATUS_LABELS[status]}" 吗？`, '确认状态变更', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`版权状态已更新为: ${this.COPYRIGHT_STATUS_LABELS[status]}`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消状态变更')
            })
        },

        handleDelete(row) {
            this.$confirm(`确定要删除版权 "${row.copyright_no}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('版权删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.copyright-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;

        .el-form-item {
            margin-bottom: 16px;
        }
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .copyright-index-page {
        .advanced-search-form {
            padding: 12px;

            .el-form-item {
                margin-bottom: 12px;
            }
        }

        .footer-stats {
            flex-direction: column;
            gap: 8px;
        }
    }
}
</style>
