<?php

namespace App\Traits\Lesson;

use App\Models\KeywordCenter\Keyword;
use App\Models\ProductCenter\Product;
use App\Services\BaseService;

trait HandleKeywordsTrait
{
    /**
     * 处理关键词逻辑，保存或关联产品的关键词
     * @param array|string|null $keywords 关键词，可以是数组或字符串
     * @param Product $product 产品实例
     * @return void
     */
    public function handleKeywords($keywords, $product)
    {
        if (!empty($keywords)) {
            // 将关键词处理成数组，如果是字符串，用逗号分隔
            $keywordArray = is_array($keywords) ? $keywords : explode(',', $keywords);

            // 遍历关键词数组，查找或创建关键词
            $keywordIds = [];
            foreach ($keywordArray as $keywordName) {
                $keyword = Keyword::firstOrCreate(
                    ['word' => trim($keywordName)],
                    ['word_no' => BaseService::BaseGetNo()]
                );
                $keywordIds[] = $keyword->id;
            }

            // 将关键词与产品进行关联
            $product->keywords()->sync($keywordIds);
        }
    }
}
