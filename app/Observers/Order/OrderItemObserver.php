<?php

namespace App\Observers\Order;


use App\Models\OrderCenter\OrderItem;

class OrderItemObserver
{
    /**
     * Handle the OrderItem "created" event.
     */
    public function created(OrderItem $OrderItem): void
    {
        //
    }

    /**
     * Handle the OrderItem "updated" event.
     */
    public function updated(OrderItem $OrderItem): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($OrderItem->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($OrderItem->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * Handle the OrderItem "deleted" event.
     */
    public function deleted(OrderItem $OrderItem): void
    {
        //
    }

    /**
     * Handle the OrderItem "restored" event.
     */
    public function restored(OrderItem $OrderItem): void
    {
        //
    }

    /**
     * Handle the OrderItem "force deleted" event.
     */
    public function forceDeleted(OrderItem $OrderItem): void
    {
        //
    }
}
