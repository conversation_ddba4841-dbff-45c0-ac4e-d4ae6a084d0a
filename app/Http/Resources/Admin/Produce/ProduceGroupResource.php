<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceGroupResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            //
            'industry_id' => $this->industry_id ?? null,
            'suit_id' => $this->suit_id ?? null,
            //
            'group_name' => $this->group_name ?? null,
            'group_english_name' => $this->group_english_name ?? null,
            'group_description' => $this->group_description ?? null,
            //
            'sort' => $this->sort ?? null,
            //
            'cat_uuid' => isset($this->category) ? $this->category->uuid : null,
            'cat_name' => isset($this->category) ? $this->category->category_name : null,
            //
            'keywords' => isset($this->keywords) ? $this->keywords()->select('word')->pluck('word')->toArray() : null,
            //
            'spec_ids' => isset($this->specs) ? $this->specs()->select('id')->pluck('id')->toArray() : null,
            'theme_ids' => isset($this->themes) ? $this->themes()->select('id')->pluck('id')->toArray() : null,
            'brand_ids' => isset($this->brands) ? $this->brands()->select('id')->pluck('id')->toArray() : null,
            'param_ids' => isset($this->params) ? $this->params()->select('id')->pluck('id')->toArray() : null,
        ]);
    }
}
