<?php

namespace App\Enums\State;


use App\Enums\BaseEnum;

/**
 * Class ProductMainStateEnum
 * @package App\Enums\State\Product
 * @method static self default() 默认状态
 * @method static self Approved() 审核通过
 * @method static self PendingApproval() 待审核
 * @method static self Draft() 草稿
 * @method static self NotApproved() 审核未通过
 * @method static self Listing() 上架中
 * @method static self Unlisted() 已下架
 * @method static self OutOfStock() 售罄
 * @method static self OnHold() 暂停销售
 * @method static self Controlled() 管制中
 */
final class ProductMainStateEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'default' => 0, // 默认状态
            'Approved' => 1, // 审核通过
            'PendingApproval' => 2, // 待审核
            'Draft' => 3, // 草稿
            'NotApproved' => 4, // 审核未通过
            'Listing' => 5, // 上架中
            'Unlisted' => 6, // 已下架
            'OutOfStock' => 7, // 售罄
            'OnHold' => 8, // 暂停销售
            'Controlled' => 9, // 管制中
        ];
    }

    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
            'Approved' => '审核通过',
            'PendingApproval' => '待审核',
            'Draft' => '草稿',
            'NotApproved' => '审核未通过',
            'Listing' => '上架中',
            'Unlisted' => '已下架',
            'OutOfStock' => '售罄',
            'OnHold' => '暂停销售',
            'Controlled' => '管制中',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        //
        return [
            'Draft' => 'blue',
            'PendingApproval' => 'orange',
            'Approved' => 'green',
            'NotApproved' => 'red',
            'Listing' => 'purple',
            'Unlisted' => 'gray',
            'OutOfStock' => 'brown',
            'OnHold' => 'yellow',
            'Controlled' => 'pink',
        ];
    }

}
