<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688ElectronicWaybillApi 类
 * 此类用于处理与阿里巴巴1688平台电子面单相关的API调用，包括获取所有物流模板、
 * 面单订购查询、菜鸟打单取号、更新电子面单、面单取消、面单详情查询（根据面单号查询）
 * 以及根据订单号查询面单信息等功能。
 */
class Ali1688ElectronicWaybillApi extends Ali1688BaseApi
{
    /**
     * 获取所有物流模板
     * 用户订单筛选的订单标信息查询接口。
     * 此接口需要用户授权。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:waybill.templates.getAll-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @return mixed
     * @throws RuntimeException 如果API请求失败
     * @API  com.alibaba.logistics:waybill.templates.getAll-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.waybill/alibaba.waybill.templates.getAll/7587553
     */
    public static function getAllWaybillTemplates(array $Ali1688AccessToken)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.waybill/alibaba.waybill.templates.getAll/';

        // 初始化请求参数
        $params = [];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 面单订购查询
     * 查询商家订购物流信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:waybill.subscription.query-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $params 查询参数，包含：
     *                                  - waybillSubscriptionQueryDTO (对象, 是): 查询DTO对象
     *                                  - clientInfoFor1688DTO (对象, 是): 客户端信息DTO对象
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:waybill.subscription.query-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.waybill/alibaba.waybill.subscription.query/7587553
     */
    public static function queryWaybillSubscription(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.waybill/alibaba.waybill.subscription.query/';

        // 参数验证
        if (empty($params['waybillSubscriptionQueryDTO']) || !is_array($params['waybillSubscriptionQueryDTO'])) {
            throw new InvalidArgumentException('waybillSubscriptionQueryDTO 是必填参数，且必须为数组。');
        }

        if (empty($params['clientInfoFor1688DTO']) || !is_array($params['clientInfoFor1688DTO'])) {
            throw new InvalidArgumentException('clientInfoFor1688DTO 是必填参数，且必须为数组。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 菜鸟打单取号
     * 申请新的电子面单号。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:waybill.cloudPrint.applyNew-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $applyData 申请数据，包含：
     *                                  - waybillCloudPrintApplyNewRequest (对象, 是): 申请请求对象
     *                                  - clientInfoDTO (对象, 是): 客户端信息DTO对象
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:waybill.cloudPrint.applyNew-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.applyNew/7587553
     */
    public static function applyNewWaybillNumber(array $Ali1688AccessToken, array $applyData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.applyNew/';

        // 参数验证
        if (empty($applyData['waybillCloudPrintApplyNewRequest']) || !is_array($applyData['waybillCloudPrintApplyNewRequest'])) {
            throw new InvalidArgumentException('waybillCloudPrintApplyNewRequest 是必填参数，且必须为数组。');
        }

        if (empty($applyData['clientInfoDTO']) || !is_array($applyData['clientInfoDTO'])) {
            throw new InvalidArgumentException('clientInfoDTO 是必填参数，且必须为数组。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $applyData,                              // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 更新电子面单
     * 更新已有的电子面单信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:waybill.cloudPrint.applyUpdate-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $updateData 更新数据，包含：
     *                                  - waybillCloudPrintUpdateRequest (对象, 是): 更新请求对象
     *                                  - clientInfoDTO (对象, 是): 客户端信息DTO对象
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:waybill.cloudPrint.applyUpdate-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.applyUpdate/7587553
     */
    public static function applyUpdateWaybill(array $Ali1688AccessToken, array $updateData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.applyUpdate/';

        // 参数验证
        if (empty($updateData['waybillCloudPrintUpdateRequest']) || !is_array($updateData['waybillCloudPrintUpdateRequest'])) {
            throw new InvalidArgumentException('waybillCloudPrintUpdateRequest 是必填参数，且必须为数组。');
        }

        if (empty($updateData['clientInfoDTO']) || !is_array($updateData['clientInfoDTO'])) {
            throw new InvalidArgumentException('clientInfoDTO 是必填参数，且必须为数组。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $updateData,                             // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 面单取消
     * 取消已有的电子面单。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:waybill.cloudPrint.cancel-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $waybillCode 需要取消的面单号
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:waybill.cloudPrint.cancel-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.cancel/7587553
     */
    public static function cancelWaybill(array $Ali1688AccessToken, string $waybillCode)
    {
        // 验证必填参数
        if (empty($waybillCode)) {
            throw new InvalidArgumentException('waybillCode 是必填参数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.cancel/';

        // 初始化请求参数
        $params = [
            'waybillCode' => $waybillCode, // 需要取消的面单号
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 面单详情查询（根据面单号查询）
     * 根据面单号查询面单详情。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:waybill.query.queryByWaybillCodes-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $waybillCodes 需要查询的面单号数组
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:waybill.query.queryByWaybillCodes-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.waybill/alibaba.waybill.query.queryByWaybillCodes/7587553
     */
    public static function queryWaybillDetailsByWaybillCodes(array $Ali1688AccessToken, array $waybillCodes)
    {
        // 验证必填参数
        if (empty($waybillCodes)) {
            throw new InvalidArgumentException('waybillCodes 是必填参数，且必须为非空数组。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.waybill/alibaba.waybill.query.queryByWaybillCodes/';

        // 初始化请求参数
        $params = [
            'waybillCodes' => implode(',', $waybillCodes), // 面单号，以逗号分隔
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 根据订单号查询面单信息
     * 根据订单号查询面单信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:waybill.cloudPrint.queryByOrder-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $orderId 需要查询的订单ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.logistics:waybill.cloudPrint.queryByOrder-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.queryByOrder/7587553
     */
    public static function queryWaybillByOrderId(array $Ali1688AccessToken, string $orderId)
    {
        // 验证必填参数
        if (empty($orderId)) {
            throw new InvalidArgumentException('orderId 是必填参数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.waybill/alibaba.waybill.cloudPrint.queryByOrder/';

        // 初始化请求参数
        $params = [
            'orderId' => $orderId, // 需要查询的订单ID
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }
}
