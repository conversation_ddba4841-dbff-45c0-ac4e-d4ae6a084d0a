<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * Product Edit Type Enum
 * 描述：用于定义产品编辑的不同类型
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self new_product_release()
 * @method static self product_info_update()
 * @method static self product_picture_image_update()
 * @method static self product_image_update()
 * @method static self product_desc_update()
 * @method static self product_name_update()
 * @method static self product_title_update()
 * @method static self keyword_update()
 * @method static self param_update()
 * @method static self param_fabric_update()
 * @method static self param_design_update()
 * @method static self param_trend_update()
 * @method static self param_craft_update()
 * @method static self param_shade_update()
 * @method static self param_purpose_update()
 * @method static self param_accessory_update()
 * @method static self param_custom_update()
 * @method static self product_attribute_sku_update()
 * @method static self param_suit_update()
 * @method static self param_spec_update()
 * @method static self param_theme_update()
 * @method static self suit_update()
 * @method static self suit_name_update()
 * @method static self suit_increase_update()
 * @method static self suit_reduce_update()
 * @method static self spec_update()
 * @method static self spec_image_update()
 * @method static self spec_name_update()
 * @method static self spec_theme_update()
 * @method static self spec_increase_update()
 * @method static self spec_reduce_update()
 * @method static self custom_update()
 * @method static self custom_name_update()
 * @method static self custom_increase_update()
 * @method static self custom_reduce_update()
 * @method static self custom_sku_update()
 * @method static self product_sku_update()
 * @method static self sku_image_update()
 * @method static self sku_number_update()
 * @method static self sku_weight_update()
 * @method static self sku_stock_update()
 * @method static self sku_market_price_update()
 * @method static self sku_price_increase_update()
 * @method static self sku_price_decrease_update()
 * @method static self sku_offline_update()
 * @method static self sku_closure_update()
 */
final class ProductEditTypeEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    // 定义枚举值
    protected static function values(): array
    {
        return [
            'default' => 0,
            'new_product_release' => 10,
            'product_info_update' => 20,
            'product_picture_image_update' => 21,
            'product_image_update' => 22,
            'product_desc_update' => 23,
            'product_name_update' => 27,
            'product_title_update' => 28,
            'keyword_update' => 29,
            'param_update' => 30,
            'param_fabric_update' => 31,
            'param_design_update' => 32,
            'param_trend_update' => 33,
            'param_craft_update' => 34,
            'param_shade_update' => 35,
            'param_purpose_update' => 36,
            'param_accessory_update' => 37,
            'param_custom_update' => 38,
            'product_attribute_sku_update' => 40,
            'param_suit_update' => 41,
            'param_spec_update' => 42,
            'param_theme_update' => 43,
            'suit_update' => 51,
            'suit_name_update' => 52,
            'suit_increase_update' => 53,
            'suit_reduce_update' => 54,
            'spec_update' => 60,
            'spec_image_update' => 61,
            'spec_name_update' => 62,
            'spec_theme_update' => 63,
            'spec_increase_update' => 64,
            'spec_reduce_update' => 65,
            'custom_update' => 70,
            'custom_name_update' => 71,
            'custom_increase_update' => 72,
            'custom_reduce_update' => 73,
            'custom_sku_update' => 74,
            'product_sku_update' => 80,
            'sku_image_update' => 81,
            'sku_number_update' => 82,
            'sku_weight_update' => 83,
            'sku_stock_update' => 84,
            'sku_market_price_update' => 85,
            'sku_price_increase_update' => 86,
            'sku_price_decrease_update' => 87,
            'sku_offline_update' => 88,
            'sku_closure_update' => 89,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认状态',
            'new_product_release' => '新品发布',
            //
            'product_info_update' => '产品信息更新',
            //
            'product_picture_image_update' => '产品主图更新',
            'product_image_update' => '产品图片更新',
            'product_desc_update' => '产品详情更新',
            'product_name_update' => '产品名称更新',
            'product_title_update' => '产品标题更新',
            'keyword_update' => '关键词更新',
            'param_update' => '参数更新',
            //
            'param_fabric_update' => '面料参数更新',
            'param_design_update' => '设计参数更新',
            'param_trend_update' => '趋势参数更新',
            'param_craft_update' => '工艺参数更新',
            'param_shade_update' => '图案参数更新',
            'param_purpose_update' => '用途参数更新',
            'param_accessory_update' => '配件参数更新',
            'param_custom_update' => '自定义参数更新',
            //
            'product_attribute_sku_update' => '编辑规格增加SKU',
            //
            'param_suit_update' => '适用参数更新',
            'param_spec_update' => '规格参数更新',
            'param_theme_update' => '主题参数更新',
            'suit_update' => '适用更新',
            'suit_name_update' => '适用名称更新',
            'suit_increase_update' => '适用增加更新',
            'suit_reduce_update' => '适用减少更新',
            'spec_update' => '规格更新',
            'spec_image_update' => '规格图片更新',
            'spec_name_update' => '规格名称更新',
            'spec_theme_update' => '规格主题更新',
            'spec_increase_update' => '规格增加更新',
            'spec_reduce_update' => '规格减少更新',
            'custom_update' => '自定义更新',
            'custom_name_update' => '自定义名称更新',
            'custom_increase_update' => '自定义增加更新',
            'custom_reduce_update' => '自定义减少更新',
            'custom_sku_update' => '自定义SKU更新',
            'product_sku_update' => 'SKU更新',
            'sku_image_update' => 'SKU图片更新',
            'sku_number_update' => 'SKU货号更新',
            'sku_weight_update' => 'SKU重量更新',
            'sku_stock_update' => 'SKU库存更新',
            'sku_market_price_update' => 'SKU市场价更新',
            'sku_price_increase_update' => 'SKU价格涨价更新',
            'sku_price_decrease_update' => 'SKU价格降价更新',
            'sku_offline_update' => 'SKU下架更新',
            'sku_closure_update' => 'SKU停产更新',
        ];
    }
}
