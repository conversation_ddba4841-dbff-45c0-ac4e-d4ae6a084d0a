<?php

namespace App\Services\Seller;

use App\Models\ProductCenter\Product;
use App\Models\ProductCenter\ProductDemo;
use App\Models\StoreCenter\StoreClass;
use App\Models\UserCenter\User;
use App\Services\BaseService;
use Illuminate\Support\Facades\Auth;

class StoreService extends BaseService
{
    /**
     * @param $user_id
     * @return null
     */
    public static function getUserIdByStore($user_id = null)
    {
        //
        $id = Auth::id() ?? request()->user()->id ?? $user_id;
        $User = User::find($id);
        $store = $User->store;
        //
        return $store ?? null;
    }

    /**
     * @param $user_id
     * @return null
     */
    public static function getStoreIdToStore($store_id = null)
    {
        //
        $id = Auth::id() ?? request()->user()->id ?? $store_id;
        $User = User::find($id);
        $store = $User->store;
        //
        return $store ?? null;
    }

    /**
     * 通过店铺id，查询剩余可发布产品数量
     * @return mixed
     */
    public static function getRemainProductNumber($Store)
    {
        //
        $product_number = $Store->plan->product_number;
        $product_count = Product::where('platform_id', '=', config('app.platform_id'))->whereNotIn('is_closure', [0])->where('store_id', $Store->id)->count();
        $product_demo_count = ProductDemo::where('platform_id', '=', config('app.platform_id'))->whereIn('is_edit', [2])->where('store_id', $Store->id)->count();
        //
        $remain_product_number = $product_number - ($product_count + $product_demo_count);
        //
        return $remain_product_number;
    }

    /**
     * 通过店铺id，查询剩余可申请类目
     * @return mixed
     */
    public static function getRemainClassNumber($Store)
    {
        //
        $class_number = $Store->plan->class_number;
        $store_class_count = StoreClass::where('store_id', $Store->id)->whereIn('is_check', [1, 2])->count();
        //
        $remain_class_number = $class_number - $store_class_count;
        //
        return $remain_class_number;
    }
}
