<?php

namespace App\Http\Resources\Advocate\Product;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductSkuResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //dd($this->stock);
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'product_no' => $this->product_no ?? null,
            'sku_no' => $this->sku_no ?? null,
            //
            'spec_id' => $this->spec_id ?? null,
            'suit_id' => $this->suit_id ?? null,
            'custom_id' => $this->custom_id ?? null,
            'theme_id' => $this->theme_id ?? null,
            //
            'sku_name' => $this->sku_name ?? null,
            'sku_spec' => $this->sku_spec ?? null,
            //
            'suit_attr' => $this->suit_attr ?? null,
            'spec_attr' => $this->spec_attr ?? null,
            'custom_attr' => $this->custom_attr ?? null,
            //
            'sku_image_id' => $this->sku_image_id ?? null,
            'sku_image_path' => $this->sku_image_path ?? null,
            'sku_image_url' => $this->sku_image_url ?? null,
            //
            'sku_sn' => $this->sku_sn ?? null,
            'amount' => $this->amount ?? 0,
            'price' => $this->price ?? 0,
            'market_price' => $this->market_price ?? 0,
            'stock' => $this->stock,
            'jit_stock' => $this->jit_stock ?? 0,
            'weight' => $this->weight ?? 0,
            //
            'is_offline_label' => $this->is_offline->label ?? null,
            'is_offline_color' => $this->is_offline->color() ?? null,
            'is_closure_label' => $this->is_closure->label ?? null,
            'is_closure_color' => $this->is_closure->color() ?? null,
            //
            'is_offline' => $this->is_offline->key() ?? null,
            'is_closure' => $this->is_closure->key() ?? null,
        ]);
    }
}
