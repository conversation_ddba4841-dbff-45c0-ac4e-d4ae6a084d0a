<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688ProductManageApi 类
 * 此类用于处理与阿里巴巴1688平台商品管理相关的API调用，包括查询商品列表、
 * 获取商品详情、根据商品ID列表获取商品、检查商品是否可修改、转为过期、重新上架、
 * 删除商品、新版商品编辑、增量编辑商品信息以及修改商品快返库存等功能。
 */
class Ali1688ProductManageApi extends Ali1688BaseApi
{
    /**
     * 卖家查询商品列表
     * 根据条件分页查询商品列表信息。目前无法查询简易商品。如需遍历所有商品只能使用ID排序。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.list.get-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $params 查询参数，包含多个可选过滤条件
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.list.get-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.list.get/7587553
     */
    public static function listProducts(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.list.get/';

        // 参数验证
        if (isset($params['pageNo']) && (!is_int($params['pageNo']) || $params['pageNo'] < 1)) {
            throw new InvalidArgumentException('pageNo 必须为大于等于1的整数。');
        }

        if (isset($params['pageSize']) && (!is_int($params['pageSize']) || $params['pageSize'] < 1 || $params['pageSize'] > 20)) {
            throw new InvalidArgumentException('pageSize 必须为1到20之间的整数。');
        }

        if (isset($params['statusList']) && !is_array($params['statusList'])) {
            throw new InvalidArgumentException('statusList 必须为数组。');
        }

        if (isset($params['productIds']) && !is_array($params['productIds'])) {
            throw new InvalidArgumentException('productIds 必须为数组。');
        }

        // 其他参数的验证可以根据需要添加

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取商品详情
     * 根据商品ID获取商品详细信息。只能查询自己所有的产品。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.get-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID，必须为正整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.get-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.get/7587553
     */
    public static function getProduct(array $Ali1688AccessToken, int $productId)
    {
        // 验证必填参数
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.get/';

        // 初始化请求参数
        $params = [
            'productId' => $productId,
            // 根据API文档，可能需要额外的参数，如 webSite, scene 等
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 根据商品ID列表获取商品（卖家）
     * 根据商品ID列表获取卖家的商品信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.getByIdList-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $productIds 商品ID列表，必须为非空数组，且每个ID为正整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.getByIdList-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.getByIdList/7587553
     */
    public static function getProductsByIdList(array $Ali1688AccessToken, array $productIds)
    {
        // 验证必填参数
        if (empty($productIds)) {
            throw new InvalidArgumentException('productIds 是必填参数，且必须为非空数组。');
        }

        foreach ($productIds as $id) {
            if (!is_int($id) || $id <= 0) {
                throw new InvalidArgumentException('productIds 中的每个ID必须为正整数。');
            }
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.getByIdList/';

        // 初始化请求参数
        $params = [
            'productIdList' => implode(',', $productIds),
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }

    /**
     * 产品是否可以修改
     * 检查指定商品是否可以进行修改操作。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.isModifiable-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID，必须为正整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.isModifiable-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.isModifiable/7587553
     */
    public static function isProductModifiable(array $Ali1688AccessToken, int $productId)
    {
        // 验证必填参数
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.isModifiable/';

        // 初始化请求参数
        $params = [
            'productId' => $productId,
            'webSite' => '1688', // 根据API文档，必须指定webSite
            'scene' => '1688', // 根据API文档，必须指定scene
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }

    /**
     * 商品转为过期
     * 将指定商品转为过期状态。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.expire-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $productIds 过期商品的ID列表，必须为非空数组，且每个ID为正整数
     * @param string $webSite 站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.expire-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.expire/7587553
     */
    public static function expireProduct(array $Ali1688AccessToken, array $productIds, string $webSite = '1688')
    {
        // 验证必填参数
        if (empty($productIds)) {
            throw new InvalidArgumentException('productIds 是必填参数，且必须为非空数组。');
        }

        foreach ($productIds as $id) {
            if (!is_int($id) || $id <= 0) {
                throw new InvalidArgumentException('productIds 中的每个ID必须为正整数。');
            }
        }

        if (empty($webSite) || !in_array($webSite, ['1688', 'alibaba'], true)) {
            throw new InvalidArgumentException('webSite 必须为 "1688" 或 "alibaba"。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.expire/';

        // 初始化请求参数
        $params = [
            'productIds' => implode(',', $productIds),
            'webSite' => $webSite,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'POST' // 假设转为过期使用POST方法
        );
    }

    /**
     * 未上架商品重新上架
     * 将未上架的商品重新上架。请注意：
     * - 会员每天最多上架400条。
     * - 非诚信通或买家保障会员，食品美容行业如果有限制，则不能上架。
     * - 橡塑现货条数限制，已上网最多200条。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.republish-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $productIds 重发商品ID列表，必须为非空数组，且每个ID为正整数
     * @param string $webSite 站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.republish-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.republish/7587553
     */
    public static function republishProduct(array $Ali1688AccessToken, array $productIds, string $webSite = '1688')
    {
        // 验证必填参数
        if (empty($productIds)) {
            throw new InvalidArgumentException('productIds 是必填参数，且必须为非空数组。');
        }

        foreach ($productIds as $id) {
            if (!is_int($id) || $id <= 0) {
                throw new InvalidArgumentException('productIds 中的每个ID必须为正整数。');
            }
        }

        if (empty($webSite) || !in_array($webSite, ['1688', 'alibaba'], true)) {
            throw new InvalidArgumentException('webSite 必须为 "1688" 或 "alibaba"。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.republish/';

        // 初始化请求参数
        $params = [
            'productIds' => implode(',', $productIds),
            'webSite' => $webSite,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'POST' // 假设重新上架使用POST方法
        );
    }

    /**
     * 删除商品
     * 将指定商品删除到回收站中，可在网站手工清除或恢复。此API为国际站与1688通用。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.delete-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID，必须为正整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.delete-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.delete/7587553
     */
    public static function deleteProduct(array $Ali1688AccessToken, int $productId)
    {
        // 验证必填参数
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.delete/';

        // 初始化请求参数
        $params = [
            'productId' => $productId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 新版商品编辑
     * 编辑已有的商品信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.new.product.edit-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $productData 商品编辑数据，包含多个必填和可选字段
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.new.product.edit-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.new.product/alibaba.new.product.edit/7587553
     */
    public static function editNewProduct(array $Ali1688AccessToken, array $productData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.new.product/alibaba.new.product.edit/';

        // 参数验证
        if (empty($productData['catId']) || !is_int($productData['catId'])) {
            throw new InvalidArgumentException('catId 是必填参数，且必须为整数。');
        }

        if (empty($productData['offerId']) || !is_int($productData['offerId'])) {
            throw new InvalidArgumentException('offerId 是必填参数，且必须为整数。');
        }

        if (empty($productData['scene']) || !is_string($productData['scene'])) {
            throw new InvalidArgumentException('scene 是必填参数，且必须为字符串。');
        }

        if (empty($productData['dataBody']) || !is_array($productData['dataBody'])) {
            throw new InvalidArgumentException('dataBody 是必填参数，且必须为数组。');
        }

        // bizParam 是可选参数，但如果提供，则必须为数组
        if (isset($productData['bizParam']) && !is_array($productData['bizParam'])) {
            throw new InvalidArgumentException('bizParam 必须为数组。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $productData,                            // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 新版商品增量编辑
     * 增量编辑商品信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:product.increment.editInfo-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $incrementData 增量编辑数据，包含多个必填和可选字段
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:product.increment.editInfo-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/product.increment.editInfo/7587553
     */
    public static function incrementEditProductInfo(array $Ali1688AccessToken, array $incrementData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/product.increment.editInfo/';

        // 参数验证
        if (empty($incrementData['catId']) || !is_int($incrementData['catId'])) {
            throw new InvalidArgumentException('catId 是必填参数，且必须为整数。');
        }

        if (empty($incrementData['scene']) || !is_string($incrementData['scene'])) {
            throw new InvalidArgumentException('scene 是必填参数，且必须为字符串。');
        }

        if (empty($incrementData['offerId']) || !is_string($incrementData['offerId'])) {
            throw new InvalidArgumentException('offerId 是必填参数，且必须为字符串。');
        }

        if (empty($incrementData['dataBody']) || !is_array($incrementData['dataBody'])) {
            throw new InvalidArgumentException('dataBody 是必填参数，且必须为数组。');
        }

        // bizParam 是可选参数，但如果提供，则必须为数组
        if (isset($incrementData['bizParam']) && !is_array($incrementData['bizParam'])) {
            throw new InvalidArgumentException('bizParam 必须为数组。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $incrementData,
            'POST'
        );
    }

    /**
     * 修改商品快返库存
     * 修改指定商品的快返库存。支持增量修改和全量修改，通过入参控制，默认为增量修改。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.modifyQuickBackStock-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID，必须为正整数
     * @param int $quantity 库存数量，必须为整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.modifyQuickBackStock-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.modifyQuickBackStock/7587553
     */
    public static function modifyQuickBackStock(array $Ali1688AccessToken, int $productId, int $quantity)
    {
        // 验证必填参数
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        if ($quantity === 0) {
            throw new InvalidArgumentException('quantity 不能为0。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.modifyQuickBackStock/';

        // 初始化请求参数
        $params = [
            'productId' => $productId,
            'quantity' => $quantity,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'POST' // 修改商品快返库存使用POST方法
        );
    }

    // 其他商品管理相关的方法...

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
