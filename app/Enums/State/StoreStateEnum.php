<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * Class StoreStateEnum
 * @package App\Enums\State\Order
 * @method static self default() 默认状态
 * @method static self open() 营业中
 * @method static self soft() 试营业
 * @method static self pending() 待上线
 * @method static self grand() 开业庆典
 * @method static self limited() 限制营业
 * @method static self await() 等待下线
 * @method static self suspended() 暂停营业
 * @method static self relocating() 搬迁中
 * @method static self temp() 临时管制
 * @method static self closed() 关闭中
 */
final class StoreStateEnum extends BaseEnum
{
    // 定义状态值并进行分组整理和排序
    protected static function values(): array
    {
        //
        return [
            // 默认状态 0
            'default' => 0,
            //
            'open' => 10,//营业中
            'soft' => 20,//试营业
            'pending' => 30,//待上线
            'grand' => 40,//开业庆典
            'limited' => 50,//限制营业
            'await' => 60,//等待下线
            'suspended' => 70,//暂停营业
            'relocating' => 80,//搬迁中
            'temp' => 90,//临时管制
            'closed' => 100,//关闭中
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            // 默认状态
            'default' => '默认',
            //
            'open' => '营业中',
            'soft' => '试营业',
            'pending' => '待上线',
            'grand' => '开业庆典',
            'limited' => '限制营业',
            'await' => '等待下线',
            'suspended' => '暂停营业',
            'relocating' => '搬迁中',
            'temp' => '临时管制',
            'closed' => '关闭中',
        ];
    }
}
