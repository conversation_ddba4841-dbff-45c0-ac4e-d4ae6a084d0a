<!--
/**
 * 趋势管理页面
 *
 * 功能特性：
 * - 管理时尚趋势和流行元素
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/produce/trend
 * 页面标题：趋势管理
 *
 * 版本：v1.0.0
 * 更新时间：2025年1月
 * 作者：Augment Agent
 */
-->

<template>
    <div class="trend-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索趋势名称、关键词或季节'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="趋势类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option
                                    v-for="(label, type) in TREND_TYPE_LABELS"
                                    :key="type"
                                    :label="label"
                                    :value="type"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="适用季节">
                            <el-select v-model="filters.season" clearable placeholder="选择季节">
                                <el-option label="春季" value="spring"></el-option>
                                <el-option label="夏季" value="summer"></el-option>
                                <el-option label="秋季" value="autumn"></el-option>
                                <el-option label="冬季" value="winter"></el-option>
                                <el-option label="全年" value="all_year"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="热度等级">
                            <el-select v-model="filters.popularity" clearable placeholder="选择热度">
                                <el-option label="极热" value="hot"></el-option>
                                <el-option label="流行" value="popular"></el-option>
                                <el-option label="一般" value="normal"></el-option>
                                <el-option label="冷门" value="cold"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="活跃" value="active"></el-option>
                                <el-option label="过时" value="outdated"></el-option>
                                <el-option label="预测" value="predicted"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 趋势列表 -->
            <template #default>
                <TrendListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @analyze="handleAnalyze"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总趋势: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import TrendListView from './components/TrendListView.vue'

export default {
    name: 'AdminProduceTrendIndexPage',
    components: {
        BackendPageListLayout,
        TrendListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                type: '',
                season: '',
                popularity: '',
                status: ''
            },

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部趋势', value: 'all'},
                {label: '色彩趋势', value: 'color'},
                {label: '图案趋势', value: 'pattern'},
                {label: '材质趋势', value: 'material'},
                {label: '风格趋势', value: 'style'}
            ],

            // 数据相关
            dataList: [],
            selectedRows: [],

            // 常量定义
            TREND_TYPE_LABELS: {
                'color': '色彩趋势',
                'pattern': '图案趋势',
                'material': '材质趋势',
                'style': '风格趋势',
                'silhouette': '廓形趋势',
                'texture': '质感趋势'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            try {
                this.loading = true
                this.v_loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟趋势数据
                const mockData = [
                    {
                        id: 1,
                        name: '2025春季珊瑚橙',
                        code: 'TRD001',
                        type: 'color',
                        season: 'spring',
                        description: '温暖活力的珊瑚橙色调，象征着春季的生机与活力',
                        keywords: ['珊瑚橙', '春季', '活力', '温暖'],
                        popularity: 'hot',
                        trend_score: 95,
                        influence_regions: ['亚洲', '欧洲', '北美'],
                        status: 'active',
                        start_date: '2025-03-01',
                        end_date: '2025-05-31',
                        created_at: '2025-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        name: '极简几何图案',
                        code: 'TRD002',
                        type: 'pattern',
                        season: 'all_year',
                        description: '简洁的几何线条和形状，体现现代简约美学',
                        keywords: ['几何', '简约', '线条', '现代'],
                        popularity: 'popular',
                        trend_score: 88,
                        influence_regions: ['全球'],
                        status: 'active',
                        start_date: '2025-01-01',
                        end_date: '2025-12-31',
                        created_at: '2025-01-14 15:20:00'
                    }
                ]

                this.dataList = mockData
                this.total = 67 // 模拟总数

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(query) {
            this.searchQuery = query
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                type: '',
                season: '',
                popularity: '',
                status: ''
            }
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的趋势吗？', '批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑趋势: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除趋势 "${row.name}" 吗？`, '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看趋势详情: ${row.name}`)
        },

        handleAnalyze(row) {
            this.$message.info(`分析趋势数据: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`趋势状态已更新: ${row.name}`)
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.trend-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;

        .stat-item {
            color: #666;
            font-size: 14px;

            strong {
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
