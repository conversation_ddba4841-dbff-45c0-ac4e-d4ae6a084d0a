<?php

namespace App\Api\Ali1688Cps;

use InvalidArgumentException;

class Ali1688CpsOfferRecommendApi extends Ali1688CpsBaseApi
{
    /**
     * 获取同款推荐的商品 (cps品含推广链接)
     * 对应 API: com.alibaba.p4p:alibaba.cps.getCpsRecommendSameOfferList-1
     * 官方文档:
     * https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.p4p:alibaba.cps.getCpsRecommendSameOfferList-1
     * @param array $Ali1688AccessToken 授权信息，需包含 ['client_id', 'client_secret', 'access_token']
     * @param array $params 请求参数
     *                                  [
     *                                  'offerId'     => (int)    必填，对应商品的 offerId
     *                                  'offerType'   => (string) 必填，可选值：'1688' 或 'tao'
     *                                  'mediaZoneId' => (int)    必填，媒体推广位ID
     *                                  'mediaId'     => (int)    必填，媒体ID
     *                                  'ext'         => (string|array) 可选，自定义扩展字段，json格式
     *                                  'maxPrice'    => (float)  可选，最大价格
     *                                  'minPrice'    => (float)  可选，最小价格
     *                                  ]
     * @return mixed 接口响应数据
     */
    public static function getCpsRecommendSameOfferList($Ali1688AccessToken, array $params = [])
    {
        // 接口路径 (POST)
        // 对应文档中:
        //   POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.p4p/alibaba.cps.getCpsRecommendSameOfferList/${APPKEY}
        $apiInfo = 'param2/1/com.alibaba.p4p/alibaba.cps.getCpsRecommendSameOfferList/';

        // 必填校验
        $requiredFields = ['offerId', 'offerType', 'mediaZoneId', 'mediaId'];
        foreach ($requiredFields as $field) {
            if (!isset($params[$field])) {
                throw new InvalidArgumentException("缺少必填参数：{$field}");
            }
        }

        // 组装请求体参数
        $body_params = [
            'offerId' => $params['offerId'],
            'offerType' => $params['offerType'],
            'mediaZoneId' => $params['mediaZoneId'],
            'mediaId' => $params['mediaId'],
        ];

        // 可选参数处理
        if (isset($params['ext'])) {
            // 若 ext 是数组，需转成 JSON；若是字符串但不符合 JSON，可根据实际需要处理
            if (is_array($params['ext'])) {
                $body_params['ext'] = json_encode($params['ext']);
            } else {
                $body_params['ext'] = $params['ext'];
            }
        }
        if (isset($params['maxPrice'])) {
            $body_params['maxPrice'] = $params['maxPrice'];
        }
        if (isset($params['minPrice'])) {
            $body_params['minPrice'] = $params['minPrice'];
        }

        // 发起调用
        return Ali1688CpsBaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'POST'
        );
    }
}
