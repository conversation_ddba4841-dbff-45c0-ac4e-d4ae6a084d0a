<?php

namespace App\Models\ScheduledCenter;

use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Console\Events\ScheduledTaskFailed;
use Illuminate\Console\Events\ScheduledTaskFinished;
use Illuminate\Console\Events\ScheduledTaskSkipped;
use Illuminate\Console\Events\ScheduledTaskStarting;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use OhDear\PhpSdk\Resources\CronCheck;
use Spatie\ScheduleMonitor\Jobs\PingOhDearJob;
use Spatie\ScheduleMonitor\Support\Concerns\UsesMonitoredScheduledTasks;
use Spatie\ScheduleMonitor\Support\Concerns\UsesScheduleMonitoringModels;
use Spatie\ScheduleMonitor\Support\ScheduledTasks\ScheduledTaskFactory;

// 调度任务失败事件

// 调度任务完成事件

// 调度任务跳过事件

// 调度任务开始执行事件

// Laravel 调度事件

// Eloquent 工厂特性，用于模型工厂

// Laravel 的 Eloquent 基础模型

// 一对多关联关系

// Laravel 字符串处理辅助类

// 外部服务 OhDear 的 Cron 检查资源

// 用于向 OhDear 发送 ping 的任务

// 自定义 trait，用于处理监控调度任务

// 自定义 trait，用于使用监控模型

// 工厂类，根据事件创建调度任务对象

/**
 * 监控调度任务模型
 * 该模型用于记录和监控 Laravel 调度任务的状态和日志信息，
 * 包含了任务执行前后状态的更新、日志记录以及与外部服务 OhDear 的通信等功能。
 */

/**
 * ScheduledTask 模型
 * @package App\Models\ScheduledCenter
 * @property int $uuid       主键ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @method static Builder|ScheduledTask query() 开始查询
 * @method static Builder|ScheduledTask where($column, $operator = null, $value = null) 条件查询
 */
class ScheduledTask extends Model
{
    /** @use HasFactory<\Database\Factories\ScheduledCenter\ScheduledTaskFactory> */
    // 引入特性：与调度监控模型和任务相关的功能，以及 Eloquent 工厂
    use UsesScheduleMonitoringModels;
    use UsesMonitoredScheduledTasks;
    use HasFactory;
    use GeneratesUuid;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    public $guarded = [];

    // $guarded 数组为空，表示允许批量赋值所有属性
    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'schedule_center';
    /**
     * 定义属性类型转换
     * 将指定字段自动转换为 datetime 或 integer 类型
     */
    protected $casts = [
        'registered_on_oh_dear_at' => 'datetime',  // 记录任务在 OhDear 注册的时间
        'last_pinged_at' => 'datetime',  // 最后一次向 OhDear 发送 ping 的时间
        'last_started_at' => 'datetime',  // 最近一次任务开始执行的时间
        'last_finished_at' => 'datetime',  // 最近一次任务正常完成的时间
        'last_skipped_at' => 'datetime',  // 最近一次任务被跳过的时间
        'last_failed_at' => 'datetime',  // 最近一次任务失败的时间
        'grace_time_in_minutes' => 'integer',   // 设置的宽限时间（单位：分钟）
    ];

    /**
     * 根据 Laravel 调度事件查找对应的监控任务实例
     * @param Event $event Laravel 调度事件
     * @return self|null 如果任务名称为空或未找到对应监控任务，返回 null；否则返回相应监控任务实例
     */
    public static function findForTask(Event $event): ?self
    {
        // 通过工厂类为事件创建调度任务对象
        $task = ScheduledTaskFactory::createForEvent($event);
        $monitoredScheduledTask = new static();

        // 如果任务名称为空，则无法进行监控
        if (empty($task->name())) {
            return null;
        }

        return $monitoredScheduledTask
            ->getMonitoredScheduleTaskModel()
            ->findByName($task->name());
    }

    /**
     * 根据任务名称查找监控任务实例
     * @param string $name 任务名称
     * @return self|null 返回匹配的监控任务实例或 null（如果未找到）
     */
    public static function findByName(string $name): ?self
    {
        $monitoredScheduledTask = new static();

        return $monitoredScheduledTask
            ->getMonitoredScheduleTaskModel()
            ->where('name', $name)
            ->first();
    }

    /**
     * 根据 OhDear Cron 检查对象查找对应的监控任务实例
     * @param CronCheck $cronCheck OhDear 的 Cron 检查对象
     * @return self|null 返回匹配的监控任务实例或 null
     */
    public static function findForCronCheck(CronCheck $cronCheck): ?self
    {
        $monitoredScheduledTask = new static();

        return $monitoredScheduledTask
            ->getMonitoredScheduleTaskModel()
            ->findByName($cronCheck->name);
    }

    /**
     * 标记任务已在 OhDear 上注册
     * 如果任务的 registered_on_oh_dear_at 字段为空，则更新为当前时间
     * @return self 返回当前监控任务实例（链式调用）
     */
    public function markAsRegisteredOnOhDear(): self
    {
        if (is_null($this->registered_on_oh_dear_at)) {
            $this->update(['registered_on_oh_dear_at' => now()]);
        }

        return $this;
    }

    /**
     * 标记任务开始执行，并记录日志和相关元数据
     * @param ScheduledTaskStarting $event 任务开始执行的事件实例
     * @return self 返回当前监控任务实例（链式调用）
     */
    public function markAsStarting(ScheduledTaskStarting $event): self
    {
        // 创建一个日志条目，类型为“开始执行”
        $logItem = $this->createLogItem($this->getMonitoredScheduleTaskLogItemModel()::TYPE_STARTING);

        // 更新日志元数据，例如当前内存使用情况
        $logItem->updateMeta([
            'memory' => memory_get_usage(true),
        ]);

        // 更新监控任务的最近启动时间为当前时间
        $this->update([
            'last_started_at' => now(),
        ]);

        // 判断配置是否要求发送开始执行的 ping，如果是，则调用 ping 方法
        if (config('schedule-monitor.oh_dear.send_starting_ping') === true) {
            $this->pingOhDear($logItem);
        }

        return $this;
    }

    /**
     * 创建一个新的日志条目
     * @param string $type 日志条目的类型（例如：开始、完成、失败、跳过）
     * @return MonitoredScheduledTaskLogItem 返回新创建的日志条目实例
     */
    public function createLogItem(string $type): MonitoredScheduledTaskLogItem
    {
        return $this->logItems()->create([
            'type' => $type,
        ]);
    }

    /**
     * 定义与日志记录相关的一对多关系
     * @return HasMany 返回与当前任务关联的多个日志条目，并按 ID 降序排序
     */
    public function logItems(): HasMany
    {
        return $this->hasMany($this->getMonitoredScheduleTaskLogItemModel(), 'monitored_scheduled_task_id')
            ->orderByDesc('id');
    }

    /**
     * 向 OhDear 发送 ping 通知，基于日志条目的类型决定是否发送
     * 仅对开始、完成和失败类型的日志发送 ping
     * @param MonitoredScheduledTaskLogItem $logItem 需要通知的日志条目
     * @return self 返回当前监控任务实例（链式调用）
     */
    public function pingOhDear(MonitoredScheduledTaskLogItem $logItem): self
    {
        // 如果没有设置 ping 的 URL，则不进行 ping
        if (empty($this->ping_url)) {
            return $this;
        }

        // 检查日志条目类型是否在允许发送 ping 的类型列表中
        if (!in_array($logItem->type, [
            $this->getMonitoredScheduleTaskLogItemModel()::TYPE_STARTING,
            $this->getMonitoredScheduleTaskLogItemModel()::TYPE_FAILED,
            $this->getMonitoredScheduleTaskLogItemModel()::TYPE_FINISHED,
        ], true)) {
            return $this;
        }

        // 分发 PingOhDearJob 任务去异步发送 ping 通知
        dispatch(new PingOhDearJob($logItem));

        return $this;
    }

    /**
     * 标记任务执行完毕，并记录日志及结果数据
     * @param ScheduledTaskFinished $event 任务完成的事件实例
     * @return self 返回当前监控任务实例（链式调用）
     */
    public function markAsFinished(ScheduledTaskFinished $event): self
    {
        // 如果当前事件是背景任务但在前台完成，则直接返回（不处理）
        if ($this->eventConcernsBackgroundTaskThatCompletedInForeground($event)) {
            return $this;
        }

        // 如果任务退出代码不为 0 且不为空，则视为任务失败，调用 markAsFailed 方法
        if ($event->task->exitCode !== 0 && !is_null($event->task->exitCode)) {
            return $this->markAsFailed($event);
        }

        // 创建一个日志条目，类型为“正常结束”
        $logItem = $this->createLogItem($this->getMonitoredScheduleTaskLogItemModel()::TYPE_FINISHED);

        // 更新日志元数据，包括运行时长、退出码、内存使用情况和任务输出
        $logItem->updateMeta([
            'runtime' => $event->task->runInBackground ? 0 : $event->runtime,
            'exit_code' => $event->task->exitCode,
            'memory' => $event->task->runInBackground ? 0 : memory_get_usage(true),
            'output' => $this->getEventTaskOutput($event),
        ]);

        // 更新监控任务的最近完成时间为当前时间
        $this->update(['last_finished_at' => now()]);

        // 向 OhDear 发送 ping 通知任务完成
        $this->pingOhDear($logItem);

        return $this;
    }

    /**
     * 判断事件是否为背景任务但在前台完成的情况
     * @param ScheduledTaskFinished $event 任务完成的事件实例
     * @return bool 如果是背景任务但在前台完成，返回 true；否则返回 false
     */
    public function eventConcernsBackgroundTaskThatCompletedInForeground(ScheduledTaskFinished $event): bool
    {
        // 如果任务并非在后台运行，则返回 false
        if (!$event->task->runInBackground) {
            return false;
        }

        // 如果任务在后台运行但退出码为 null，则认为该情况成立
        return $event->task->exitCode === null;
    }

    /**
     * 标记任务为失败状态，并记录日志信息
     * 该方法可处理两种类型的事件：
     * - ScheduledTaskFailed：任务执行过程中发生异常
     * - ScheduledTaskFinished：任务执行完成，但退出码不为 0
     * @param ScheduledTaskFailed|ScheduledTaskFinished $event 任务失败或完成事件
     * @return self 返回当前监控任务实例（链式调用）
     */
    public function markAsFailed($event): self
    {
        // 创建一个日志条目，类型为“失败”
        $logItem = $this->createLogItem($this->getMonitoredScheduleTaskLogItemModel()::TYPE_FAILED);

        // 如果是 ScheduledTaskFailed 类型，则记录异常信息（限制为 255 个字符）
        if ($event instanceof ScheduledTaskFailed) {
            $logItem->updateMeta([
                'failure_message' => Str::limit(optional($event->exception)->getMessage(), 255),
            ]);
        }

        // 如果是 ScheduledTaskFinished 类型，则记录任务运行时长、退出码、内存使用情况和任务输出
        if ($event instanceof ScheduledTaskFinished) {
            $logItem->updateMeta([
                'runtime' => $event->runtime,
                'exit_code' => $event->task->exitCode,
                'memory' => memory_get_usage(true),
                'output' => $this->getEventTaskOutput($event),
            ]);
        }

        // 更新监控任务的最近失败时间为当前时间
        $this->update(['last_failed_at' => now()]);

        // 发送 ping 给 OhDear，通知任务失败
        $this->pingOhDear($logItem);

        return $this;
    }

    /**
     * 获取任务执行过程中的输出内容
     * 该方法会判断是否需要将任务输出存入数据库，并过滤掉默认输出及无效文件
     * @param ScheduledTaskFailed|ScheduledTaskFinished $event 任务失败或完成事件实例
     * @return string|null 返回任务输出内容或 null（如果不满足条件）
     */
    public function getEventTaskOutput($event): ?string
    {
        // 判断是否配置为存储输出到数据库，不满足则返回 null
        if (!($this->getMonitoredScheduledTasks()->getStoreOutputInDb($event->task) ?? false)) {
            return null;
        }

        // 如果任务输出为空，则返回 null
        if (is_null($event->task->output)) {
            return null;
        }

        // 如果任务输出与默认输出一致，则认为没有有效输出，返回 null
        if ($event->task->output === $event->task->getDefaultOutput()) {
            return null;
        }

        // 如果任务输出不是一个有效文件路径，则返回 null
        if (!is_file($event->task->output)) {
            return null;
        }

        // 获取任务输出文件的内容，并返回文件内容（如果内容为空，则返回 null）
        $output = file_get_contents($event->task->output);

        return $output ?: null;
    }

    /**
     * 标记任务被跳过，并记录相应的日志和状态更新
     * @param ScheduledTaskSkipped $event 任务跳过事件实例
     * @return self 返回当前监控任务实例（链式调用）
     */
    public function markAsSkipped(ScheduledTaskSkipped $event): self
    {
        // 创建一个日志条目，类型为“跳过”
        $this->createLogItem($this->getMonitoredScheduleTaskLogItemModel()::TYPE_SKIPPED);

        // 更新监控任务的最近跳过时间为当前时间
        $this->update(['last_skipped_at' => now()]);

        return $this;
    }
}
