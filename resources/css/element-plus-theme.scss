// ========================================
// Workhub Core Element Plus 企业级主题文件
// 技术栈: Laravel Blade + Vue 3 + Element Plus + Tailwind CSS + Vite + SCSS
// 设计风格: Modern, Clean, Professional, Enterprise-grade
// 中文注释用于中国合规
// ========================================

@use 'variables' as *;

// ========================================
// Element Plus 企业级主题变量覆盖 - 完美配合
// ========================================

// Element Plus 主色调覆盖
$--color-primary: $primary-color; // #5247ef
$--color-success: $success-color; // #04c717
$--color-warning: $warning-color; // #FF5000
$--color-danger: $danger-color; // #FF0036
$--color-error: $danger-color; // 错误色同危险色
$--color-info: $info-color; // #97a6ba

// Element Plus 字体系统覆盖 - 12px 基础 (基于HTML根元素12px计算)
$--font-size-extra-small: $font-size-xs; // 12px (1rem)
$--font-size-small: $font-size-sm; // 14px (1.167rem)
$--font-size-base: $font-size-sm; // 14px 作为Element Plus基础大小
$--font-size-medium: $font-size-base; // 16px (1.333rem)
$--font-size-large: $font-size-md; // 18px (1.5rem)
$--font-size-extra-large: $font-size-lg; // 20px (1.667rem)

// Element Plus 字体家族
$--font-family: $font-family-primary;

// Element Plus 边框圆角系统 (基于12px根元素)
$--border-radius-base: $border-radius-base; // 6px (0.5rem)
$--border-radius-small: $border-radius-sm; // 4px (0.333rem)
$--border-radius-round: $border-radius-xl; // 16px (1.333rem)
$--border-radius-circle: 50%;

// Element Plus 间距系统 (基于12px根元素)
$--spacing-base: $spacing-md; // 16px (1.333rem)
$--spacing-small: $spacing-base; // 12px (1rem)
$--spacing-large: $spacing-xl; // 24px (2rem)

// Element Plus 边框颜色
$--border-color-base: $border-color;
$--border-color-light: $border-color-light;
$--border-color-lighter: $border-color-lighter;
$--border-color-extra-light: $border-color-extra-light;
$--border-color-dark: $border-color-dark;
$--border-color-darker: $border-color-darker;

// Element Plus 文字颜色
$--color-text-primary: $text-primary;
$--color-text-regular: $text-regular;
$--color-text-secondary: $text-secondary;
$--color-text-placeholder: $text-placeholder;

// Element Plus 背景颜色
$--color-white: $white;
$--color-black: $black;
$--background-color-base: $bg-white;
$--background-color-page: $body-bg;

// Element Plus 填充颜色
$--fill-base: $gray-100;
$--fill-light: $gray-50;
$--fill-lighter: $gray-50;
$--fill-extra-light: $bg-lighter;
$--fill-blank: $white;
$--fill-dark: $gray-200;
$--fill-darker: $gray-300;

// Element Plus 企业级无动画设计
$--transition-duration: 0ms; // 无动画
$--transition-duration-fast: 0ms;
$--transition-function-ease-in-out-bezier: linear;
$--transition-function-fast-bezier: linear;

// Element Plus 阴影系统
$--box-shadow-base: $box-shadow-base;
$--box-shadow-light: $box-shadow-sm;
$--box-shadow-lighter: $box-shadow-xs;
$--box-shadow-dark: $box-shadow-lg;
