<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * 溯源状态枚举
 * @package App\Enums\Type\User
 * @method static self untraceable()
 * @method static self traced()
 */
final class IsSourceEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    // 定义状态值
    protected static function values(): array
    {
        return [
            'untraceable' => 0,  // 未溯源
            'traced' => 1,       // 已溯源
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'untraceable' => '未溯源',  // 未溯源标签
            'traced' => '已溯源',       // 已溯源标签
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'red',    // 未溯源：红色
            1 => 'green',  // 已溯源：绿色
        ];
    }
}
