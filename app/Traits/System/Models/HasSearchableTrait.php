<?php

namespace App\Traits\System\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * HasSearchableTrait 提供模型的搜索功能，包括多字段搜索、相关性评分和跨数据库支持。
 */
trait HasSearchableTrait
{
    /**
     * 存储搜索时的绑定参数。
     * @var array
     */
    protected $search_bindings = [];

    /**
     * 获取相关性字段的名称，默认为 'relevance'。
     * @var string
     */
    protected $relevanceField = 'relevance';

    /**
     * 创建搜索作用域。
     * @param Builder $query
     * @param string $search 搜索关键词
     * @param float|null $threshold 相关性阈值
     * @param bool $entireText 是否匹配整个文本
     * @param bool $entireTextOnly 是否仅匹配整个文本
     * @return Builder
     */
    public function scopeSearch(Builder $query, $search, $threshold = null, $entireText = false, $entireTextOnly = false)
    {
        return $this->scopeSearchRestricted($query, $search, null, $threshold, $entireText, $entireTextOnly);
    }

    /**
     * 创建带限制条件的搜索作用域。
     * @param Builder $query
     * @param string $search 搜索关键词
     * @param mixed $restriction 限制条件，可以是闭包
     * @param float|null $threshold 相关性阈值
     * @param bool $entireText 是否匹配整个文本
     * @param bool $entireTextOnly 是否仅匹配整个文本
     * @return Builder
     */
    public function scopeSearchRestricted(Builder $query, $search, $restriction, $threshold = null, $entireText = false, $entireTextOnly = false)
    {
        // 检测当前是否为 count 查询，如果是，则跳过搜索逻辑
        if ($this->isCountQuery($query)) {
            return $query;
        }

        // 确保主查询选择所有列
        $query->select($this->getTable() . '.*');

        // 添加必要的连接
        $this->makeJoins($query);

        // 如果搜索条件为 false，返回原始查询
        if ($search === false) {
            return $query;
        }

        // 处理搜索关键词，转为小写并分割为单词
        $search = mb_strtolower(trim($search));
        preg_match_all('/(?:")((?:\\\\.|[^\\\\"])*)(?:")|(\S+)/', $search, $matches);
        $words = array_filter(array_merge($matches[1], $matches[2]));

        Log::info("搜索关键词: ", $words);

        // 初始化相关性评分和选择语句
        $selects = [];
        $this->search_bindings = [];
        $relevance_total = 0;

        // 遍历所有可搜索的列
        foreach ($this->getColumns() as $column => $relevance) {
            $relevance_total += $relevance;

            // 获取每个列的搜索查询
            if (!$entireTextOnly) {
                $queries = $this->getSearchQueriesForColumn($column, $relevance, $words);
            } else {
                $queries = [];
            }

            // 如果需要匹配整个文本，添加额外的查询
            if (($entireText && count($words) > 1) || $entireTextOnly) {
                $queries[] = $this->getSearchQuery($column, $relevance, [$search], 50, '', '');
                $queries[] = $this->getSearchQuery($column, $relevance, [$search], 30, '%', '%');
            }

            // 收集所有有效的选择语句
            foreach ($queries as $select) {
                if (!empty($select)) {
                    $selects[] = $select;
                }
            }
        }

        Log::info("添加前的选择语句: ", $selects);

        // 将所有选择语句添加到查询中，并绑定参数
        $this->addSelectsToQuery($query, $selects);

        Log::info("添加后的选择语句: ", $selects);
        Log::info("搜索绑定参数: ", $this->search_bindings);

        // 如果未设置阈值，默认使用总相关性除以列数
        if (is_null($threshold)) {
            $threshold = $relevance_total / count($this->getColumns());
        }

        Log::info("相关性阈值: ", [$threshold]);

        // 如果有选择语句，应用相关性过滤
        if (!empty($selects)) {
            $this->filterQueryWithRelevance($query, $selects, $threshold);
        }

        // 添加分组条件，避免重复结果
        $this->makeGroupBy($query);

        // 如果有限制条件，应用限制
        if (is_callable($restriction)) {
            $restriction($query);
        }

        return $query;
    }

    /**
     * 检测当前查询是否为 count 查询。
     * @param Builder $query
     * @return bool
     */
    protected function isCountQuery(Builder $query)
    {
        $columns = $query->getQuery()->columns;

        // 确保 $columns 是数组
        if (!is_array($columns)) {
            return false;
        }

        if (count($columns) === 1) {
            $column = $columns[0];
            if (strpos(strtolower($column), 'count(') === 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 为查询添加必要的连接。
     * @param Builder $query
     */
    protected function makeJoins(Builder $query)
    {
        foreach ($this->getJoins() as $table => $keys) {
            $query->leftJoin($table, function ($join) use ($keys) {
                // 添加连接条件
                $join->on($keys[0], '=', $keys[1]);
                // 如果有额外的条件，添加到连接中
                if (isset($keys[2], $keys[3])) {
                    $join->whereRaw("{$keys[2]} = ?", [$keys[3]]);
                }
            });
        }
    }

    /**
     * 获取需要加入的表及连接条件。
     * @return array
     */
    protected function getJoins()
    {
        return Arr::get($this->searchable, 'joins', []);
    }

    /**
     * 获取可搜索的列及其相关性权重。
     * @return array
     */
    protected function getColumns()
    {
        if (isset($this->searchable['columns']) && is_array($this->searchable['columns'])) {
            $columns = [];
            foreach ($this->searchable['columns'] as $column => $priority) {
                // 确保列名带有表名或别名前缀
                $columns[$column] = $priority;
            }
            Log::info("可搜索的列: ", $columns);
            return $columns;
        } else {
            // 如果未定义可搜索的列，获取表的所有列
            $columns = DB::connection($this->connection)->getSchemaBuilder()->getColumnListing($this->getTable());
            Log::info("表的所有列: ", $columns);
            return array_fill_keys($columns, 1); // 默认相关性权重为1
        }
    }

    /**
     * 生成指定列的搜索查询语句。
     * @param string $column 列名，需包含表名或别名
     * @param float $relevance 相关性权重
     * @param array $words 搜索关键词数组
     * @return array
     */
    protected function getSearchQueriesForColumn($column, $relevance, array $words)
    {
        return [
            $this->getSearchQuery($column, $relevance, $words, 15),          // 无通配符
            $this->getSearchQuery($column, $relevance, $words, 5, '', '%'), // 后缀 %
            $this->getSearchQuery($column, $relevance, $words, 1, '%', '%') // 前后缀 %%
        ];
    }

    /**
     * 生成单个搜索查询片段。
     * @param string $column 列名，需包含表名或别名
     * @param float $relevance 相关性权重
     * @param array $words 搜索关键词数组
     * @param float $multiplier 相关性乘数
     * @param string $preWord 关键词前缀
     * @param string $postWord 关键词后缀
     * @return string
     */
    protected function getSearchQuery($column, $relevance, array $words, $multiplier, $preWord = '', $postWord = '')
    {
        // 根据数据库类型选择比较符
        $likeComparator = $this->isPostgresqlDatabase() ? 'ILIKE' : 'LIKE';
        $cases = [];

        foreach ($words as $word) {
            // 生成 CASE WHEN 语句
            $cases[] = $this->getCaseCompare($column, $likeComparator, $relevance * $multiplier);
            // 添加绑定参数，包含前后缀
            $this->search_bindings[] = $preWord . $word . $postWord;
        }

        return implode(' + ', $cases);
    }

    /**
     * 确认当前数据库是否为 PostgreSQL。
     * @return bool
     */
    private function isPostgresqlDatabase()
    {
        return $this->getDatabaseDriver() === 'pgsql';
    }

    /**
     * 获取数据库驱动名称，如 mysql、pgsql、sqlsrv。
     * @return string
     */
    protected function getDatabaseDriver()
    {
        $connection = $this->connection ?: Config::get('database.default');
        return Config::get("database.connections.$connection.driver");
    }

    /**
     * 生成 CASE WHEN 比较语句。
     * @param string $column 列名，需包含表名或别名
     * @param string $comparator 比较运算符，如 LIKE 或 ILIKE
     * @param float $relevance 相关性分数
     * @return string
     */
    protected function getCaseCompare($column, $comparator, $relevance)
    {
        if ($this->isPostgresqlDatabase()) {
            $field = "LOWER($column) $comparator ?";
        } else {
            // 处理包含点号的列名，例如关联表的列
            $escapedColumn = str_replace('.', '`.`', $column);
            $field = "LOWER(`$escapedColumn`) $comparator ?";
        }

        return "(CASE WHEN $field THEN $relevance ELSE 0 END)";
    }

    /**
     * 为查询添加选择语句，计算相关性评分。
     * @param Builder $query
     * @param array $selects
     */
    protected function addSelectsToQuery(Builder $query, array $selects)
    {
        if (!empty($selects)) {
            $relevanceField = $this->getRelevanceField();
            // 创建相关性评分的表达式
            $selectExpression = 'MAX(' . implode(' + ', $selects) . ') as ' . $relevanceField;
            Log::info("选择表达式: ", [$selectExpression]);
            Log::info("搜索绑定参数: ", $this->search_bindings);
            // 使用 selectRaw 追加相关性评分，同时绑定参数
            $query->selectRaw($selectExpression, $this->search_bindings);
        } else {
            // 如果 $selects 为空，至少选择主键，避免子查询无效
            $primaryKey = $this->getKeyName();
            $query->addSelect($this->getTable() . '.' . $primaryKey);
            Log::warning("未生成搜索选择语句。选择主键。");
        }
    }

    /**
     * 获取相关性字段的名称，默认为 'relevance'。
     * @return string
     */
    protected function getRelevanceField()
    {
        return $this->relevanceField ?? 'relevance';
    }

    /**
     * 通过相关性评分过滤查询结果。
     * @param Builder $query
     * @param array $selects
     * @param float $threshold
     */
    protected function filterQueryWithRelevance(Builder $query, array $selects, $threshold)
    {
        $relevanceField = $this->getRelevanceField();

        // 确定比较符，MySQL 使用别名字段，其他数据库可能需要重新计算
        $comparator = $this->isMysqlDatabase() ? $relevanceField : implode(' + ', $selects);

        // 格式化阈值
        $threshold = number_format($threshold, 2, '.', '');

        Log::info("应用相关性过滤: $comparator >= $threshold");

        if ($this->isMysqlDatabase()) {
            // 对于 MySQL，直接使用别名字段进行比较
            $query->havingRaw("$comparator >= ?", [$threshold]);
        } else {
            // 对于其他数据库，如 PostgreSQL，只传递 threshold 作为绑定参数
            $query->havingRaw("$comparator >= ?", [$threshold]);
        }

        // 按相关性评分降序排序
        $query->orderBy($relevanceField, 'desc');
    }

    /**
     * 确认当前数据库是否为 MySQL。
     * @return bool
     */
    private function isMysqlDatabase()
    {
        return $this->getDatabaseDriver() === 'mysql';
    }

    /**
     * 为查询添加 GROUP BY 子句，避免重复结果。
     * @param Builder $query
     */
    protected function makeGroupBy(Builder $query)
    {
        if ($groupBy = $this->getGroupBy()) {
            $query->groupBy($groupBy);
        } else {
            if ($this->isSqlsrvDatabase()) {
                $columns = $this->getTableColumns();
            } else {
                $columns = $this->getTable() . '.' . $this->primaryKey;
            }

            $query->groupBy($columns);

            $joins = array_keys($this->getJoins());

            // 如果列属于关联表，添加到 GROUP BY 中
            foreach ($this->getColumns() as $column => $relevance) {
                foreach ($joins as $join) {
                    if (Str::contains($column, $join)) {
                        $query->groupBy($column);
                    }
                }
            }
        }
    }

    /**
     * 获取分组条件，避免重复结果。
     * @return mixed
     */
    protected function getGroupBy()
    {
        return $this->searchable['groupBy'] ?? false;
    }

    /**
     * 确认当前数据库是否为 SQL Server。
     * @return bool
     */
    protected function isSqlsrvDatabase()
    {
        return $this->getDatabaseDriver() === 'sqlsrv';
    }

    /**
     * 获取表的列。
     * @return array
     */
    public function getTableColumns()
    {
        return $this->searchable['table_columns'] ?? [];
    }
}
