<?php

namespace App\Filters;

/**
 * Class ProductDraftFilter
 * 提供 Store 模型的过滤逻辑。
 */
class ProductDraftFilter extends ModelFilter
{
    /**
     * 相关模型及其对应的过滤输入键。
     * 例如：['relationMethod' => ['input_key1', 'input_key2']]
     * @var array
     */
    public $relations = [];

    /**
     * 按关键词搜索商店名称和短名称。
     * @param string $keyword 关键词
     * @return $this
     */
    public function keyword($keyword)
    {
        // 在商店名称和短名称中进行模糊搜索
        return $this->search($keyword);
    }

    /**
     * 按创建日期起始范围过滤。
     * @param string $dateFrom 起始日期（格式：YYYY-MM-DD）
     * @return $this
     */
    public function date_from($dateFrom)
    {
        // 筛选创建日期大于等于 $dateFrom 的记录
        return $this->whereDate('created_at', '>=', $dateFrom);
    }

    /**
     * 按创建日期结束范围过滤。
     * @param string $dateTo 结束日期（格式：YYYY-MM-DD）
     * @return $this
     */
    public function date_to($dateTo)
    {
        // 筛选创建日期小于等于 $dateTo 的记录
        return $this->whereDate('created_at', '<=', $dateTo);
    }

    /**
     * 按状态过滤商店。
     * @param string $state 商店状态
     * @return $this
     */
    public function state($state)
    {
        // 筛选指定状态的商店
        return $this->where('product_drafts.draft_status', $state);
    }
}
