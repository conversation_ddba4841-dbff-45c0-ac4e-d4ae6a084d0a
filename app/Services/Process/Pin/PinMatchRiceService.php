<?php

namespace App\Services\Process\Pin;

use App\Models\PinCenter\Pin;
use App\Models\RiceCenter\Rice;
use App\Services\BaseService;
use Illuminate\Support\Carbon;

/**
 * PinMatchRiceService 服务类
 * 功能说明：
 * 该服务负责处理Pin与Rice的匹配关系。
 * 主要用于在Pin和Rice之间建立关联，同步数据，确保数据一致性。
 * 核心功能：
 * 1. Pin-Rice匹配：通过pin_no、标题等信息匹配Pin和Rice
 * 2. 数据同步：在Pin和Rice之间同步店铺、商品等信息
 * 3. 状态管理：更新Pin和Rice的关联状态
 * 4. 批量处理：支持批量Pin-Rice匹配
 * 应用场景：
 * - 新Rice导入后自动关联Pin
 * - 数据修复：修正Pin-Rice关联错误
 * - 定期数据同步和维护
 */
class PinMatchRiceService extends BaseService
{
    /**
     * 匹配Pin和Rice
     * 匹配策略：
     * 1. 优先通过pin_no精确匹配
     * 2. 其次通过标题相似度匹配
     * 3. 最后通过图片URL匹配
     * @param Pin $pin 需要匹配的Pin
     * @return Rice|null 匹配到的Rice，没有匹配返回null
     */
    public static function processPinMatchRice($pin): int
    {
        /*------------------------------------------------------------------
        | 2. 取出待处理 Pin
        ------------------------------------------------------------------*/
        //
        $now = Carbon::now();
        //
        $Rice = Rice::where('pin_no', $pin->pin_no)->where('rice_state', '>', 0)->first();
        /*--------------------------------------------------------------
         | 4. 写回数据库
         --------------------------------------------------------------*/
        if (isset($Rice)) {
            // 命中
            $pin->pin_number = $pin->pin_number ?? $Rice->rice_number;
            $pin->shop_id = $pin->shop_id ?? $Rice->shop_id;
            $pin->shop_no = $pin->shop_no ?? $Rice->shop_no;
            $pin->shop_name = $pin->shop_name ?? $Rice->shop_name;
            $pin->shop_url = $pin->shop_url ?? $Rice->shop_url;
            $pin->pin_subject = $pin->pin_subject ?? $Rice->pin_subject;
            $pin->rice_state = 1;
            $pin->is_rice = 1;
            $pin->rice_time = $now;
            //
            if ($pin->pin_state < 10) {
                //
                $pin->pin_state = 10;
            }
            //
            $pin->save();
            //
            $Rice->shop_id = $Rice->shop_id ?? $pin->shop_id;
            $Rice->shop_no = $Rice->shop_no ?? $pin->shop_no;
            $Rice->shop_name = $Rice->shop_name ?? $pin->shop_name;
            $Rice->shop_url = $Rice->shop_url ?? $pin->shop_url;
            $Rice->pin_subject = $Rice->pin_subject ?? $pin->pin_subject;
            $Rice->is_pin = 1;
            $Rice->save();
        }
    }
}
