<?php

namespace App\Services\Process\Rice;

use App\Enums\State\RiceStateEnum;
use App\Models\PinCenter\Pin;
use App\Models\RiceCenter\Rice;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RiceMatchMainRiceService extends BaseService
{

    /**
     * 处理单条 Rice 记录
     * @param Rice $rice
     */
    public static function processMainRice(Rice $rice): void
    {
        //
        // 建议使用闭包形式的 DB::transaction，可自动处理 commit / rollback
        DB::transaction(function () use ($rice) {
            /* --------------------------------------------------------- *
             | 1. 获取对应 Pin（优先以 pin_no 精确查，再退回关联）
             * --------------------------------------------------------- */
            $pin = Pin::query()
                ->where('platform_id', config('app.platform_id', 1))
                ->where('pin_no', $rice->pin_no)
                ->first() ?? $rice->pin;   // $rice->pin 为 hasOne 关系
            //
            if (!$pin) {
                //
                Log::warning('⚠ 未找到对应 Pin，跳过处理', ['rice_id' => $rice->id]);
                return; // 无法继续则直接返回
            }

            // 先去掉尺寸后缀，再执行正则匹配
            $cleanUrl = RiceMatchMainRiceService::removeImageSize($rice->rice_image);
            //
            //RiceMatchMainRiceService::info("✅ 图片链接{$cleanUrl}号制品！");
            /* --------------------------------------------------------- *
             | 2. 从图片 URL 提取出店铺编号（imageShopNo）
             * --------------------------------------------------------- */
            $imageShopNo = RiceMatchMainRiceService::extractShopNoFromImage($cleanUrl);

            // 数据库中的店铺编号：优先用 Rice->shop_no，否则使用已关联 Shop->shop_no
            $riceShopNo = $rice->shop_no ?: ($rice->shop->shop_no ?? null);

            /* --------------------------------------------------------- *
             | 3. 如图片中的 shop_no 与数据库不一致，则以图片为准重新关联
             * --------------------------------------------------------- */
            if ($imageShopNo && $imageShopNo !== $riceShopNo) {
                //
                RiceMatchMainRiceService::syncByImageShopNo($imageShopNo, $rice, $pin);
            } elseif ($imageShopNo === $riceShopNo) {
                //
                // 否则可直接标记当前 Rice/Pin 为主记录
                RiceMatchMainRiceService::markAsMain($rice, $pin);
            }
        });
    }

    /* =========================================================================
     | 下方均为辅助私有方法
     |========================================================================= */

    /**
     * 去除图片 URL 中的尺寸参数（如 .310x310 / .120x120）
     * @param string $url
     * @return string
     */
    private static function removeImageSize(string $url): string
    {
        //
        return preg_replace('/\.\d+x\d+(?=\.)/', '', $url);
    }

    /**
     * 解析图片 URL，提取店铺编号
     * URL 示例：
     *   https://cbu01.alicdn.com/img/ibank/O1CN01EbPwde243wWhDUuTT_!!*************-0-cib.jpg
     * 提取结果：
     *   *************
     * @param string $imageUrl
     * @return string|null 提取失败返回 null
     */
    private static function extractShopNoFromImage(string $imageUrl): ?string
    {
        //
        return preg_match('/!!(\d+)-/', $imageUrl, $m) ? $m[1] : null;
    }

    /**
     * 处理“图片 shop_no 与数据库 shop_no 不一致”的场景
     * @param string $imageShopNo 图片 URL 中提取到的 shop_no
     * @param Rice $rice 当前 Rice
     * @param Pin $pin 当前 Pin
     */
    private static function syncByImageShopNo(string $imageShopNo, Rice $rice, Pin $pin): void
    {
        /* -- 3.1 查找 Shop --------------------------------------------------- */
        $shop = Shop::query()->where('platform_id', config('app.platform_id', 1))->where('shop_no', $imageShopNo)->first();
        //
        if (!$shop) {
            //
            Log::warning('⚠ 根据图片中的 shop_no 未找到 Shop', [
                'shop_no' => $imageShopNo,
                'rice_id' => $rice->id,
            ]);
            //
            return; // 找不到 Shop 也无法继续
        }
        //
        /* -- 3.2 找主 Pin ---------------------------------------------------- */
        // 优先通过“图片关键名”匹配，失败后退回“商品标题”模糊匹配
        $imageKey = RiceMatchMainRiceService::extractImageKey($rice->rice_image);
        //
        $mainPinQuery = Pin::query()->where('platform_id', config('app.platform_id', 1))->where('shop_id', $shop->id);
        //
        if (isset($imageKey)) {
            //
            $mainPinQuery->Where('pin_pic', 'like', "%{$imageKey}%");
        } else {
            //
            $mainPinQuery->Where('pin_subject', 'like', "%{$pin->pin_subject}%");
        }
        //
        $mainPin = $mainPinQuery->first();
        //
        if ($mainPin) {
            /* -- 3.3 找到主 Pin：建立关联并更新主/副标记 ---------------------- */
            // 建立多对多关联（不会重复插入）
            $mainPin->pins()->syncWithoutDetaching($pin);
            $mainPin->rices()->syncWithoutDetaching($rice);

            // 主记录
            RiceMatchMainRiceService::markAsMain($rice, $mainPin);

            // 当前记录设置为副本
            RiceMatchMainRiceService::markAsCopy($rice, $pin);

            // 维护与 Shop 的关联
            $rice->shops()->syncWithoutDetaching($shop);
            $pin->shops()->syncWithoutDetaching($shop);
        } else {
            //
            /* -- 3.4 未找到主 Pin：仅维持与 Shop 的关系 ---------------------- */
            Log::notice('ℹ 未找到主 Pin，跳过主/副标记，仅同步店铺关系', [
                'rice_id' => $rice->id,
                'image_shop_no' => $imageShopNo,
            ]);
            //
            $rice->shops()->syncWithoutDetaching($shop);
            $pin->shops()->syncWithoutDetaching($shop);
        }
    }

    /**
     * 提取图片“关键名”，用于匹配 rice_image 字段
     * 示例：
     *   https://.../O1CN01EbPwde243wWhDUuTT_!!xxxxx.jpg → O1CN01EbPwde243wWhDUuTT
     * @param string $imageUrl
     * @return string|null 提取失败返回 null
     */
    private static function extractImageKey(string $imageUrl): ?string
    {
        //
        $cleanUrl = RiceMatchMainRiceService::removeImageSize($imageUrl);
        //
        return preg_match('#/([^/]+?)(?:_[^/]*|\.jpg|\.png)#i', $cleanUrl, $m) ? $m[1] : null;
    }

    /**
     * 标记 Rice / Pin 为主记录
     */
    private static function markAsMain(Rice $rice, Pin $mainPin): void
    {
        // 标记主 Pin
        $mainPin->is_main = 1;
        $mainPin->save();
        //
        if ($rice->pin_no === $mainPin->pin_no) {
            // Rice 标记为主
            $rice->is_main = 1;
            $rice->save();
            //
            // 3. 若Rice已经绑定店铺，修改其状态
            if ($rice->rice_state === RiceStateEnum::mate() && isset($rice->store_id)) {
                //
                $rice->is_store = 1;
                $rice->rice_state = RiceStateEnum::shine();
                $rice->save(); // 保存Rice数据
            }
        } else {
            // Rice 标记为主
            $rice->is_main = 0;
            $rice->save();
        }
    }

    /**
     * 标记 Rice / Pin 为副本
     */
    private static function markAsCopy(Rice $rice, Pin $pin): void
    {
        //
        // 标记主 Pin
        $pin->is_copy = 1;
        $pin->save();
        //
        // Rice 标记为主
        $rice->is_copy = 1;
        $rice->save();
    }
}
