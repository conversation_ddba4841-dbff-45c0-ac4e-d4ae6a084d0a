<?php

namespace App\Http\Resources\Desktop\Product;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProductImageResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'image_url' => $this->image_url ?? null,
        ]);
    }
}
