<?php

namespace App\Notify\Provider;

use Exception;
use Guanguans\Notify\WeWork\Authenticator;
use Guanguans\Notify\WeWork\Client;
use Guanguans\Notify\WeWork\Messages\FileMessage;
use Guanguans\Notify\WeWork\Messages\ImageMessage;
use Guanguans\Notify\WeWork\Messages\MarkdownMessage;
use Guanguans\Notify\WeWork\Messages\NewsMessage;
use Guanguans\Notify\WeWork\Messages\TemplateCardMessage;
use Guanguans\Notify\WeWork\Messages\TextMessage;
use Guanguans\Notify\WeWork\Messages\UploadMediaMessage;
use Guanguans\Notify\WeWork\Messages\VoiceMessage;
use Illuminate\Notifications\Notification;

class ProviderWeWorkToWebhookChannelNotify
{
    /**
     * @var Client 企业微信客户端实例，用于发送消息
     */
    protected $client;

    /**
     * 构造函数，初始化 WeWork 客户端.
     */
    public function __construct()
    {
        // 从配置文件中获取企业微信的身份认证令牌
        $authToken = config('services.wework.token', 'dad7ed18-f47e-4d9d-b11c-b48d910a702b');
        // 创建认证器实例，使用提供的身份认证令牌
        $authenticator = new Authenticator($authToken);
        // 使用认证器创建 WeWork 客户端实例，用于后续的消息发送
        $this->client = new Client($authenticator);
    }

    /**
     * 发送给定的通知.
     * @param mixed $notifiable 通知的接收者
     * @param Notification $notification 通知实例
     */
    public function send($notifiable, Notification $notification): void
    {
        // 从通知实例中获取 WeWork 消息数据，通常由 toWeWork 方法返回
        $data = $notification->toWeWorkWebhook($notifiable);
        // 根据消息类型选择相应的消息发送方法
        switch ($data['type']) {
            case 'text':
                // 创建文本消息实例
                $message = TextMessage::make($data['message']);
                break;
            case 'markdown':
                // 创建 Markdown 消息实例
                $message = MarkdownMessage::make()->content($data['message']);
                break;
            case 'news':
                // 创建图文消息实例
                $message = NewsMessage::make()->addArticle($data['message']);
                break;
            case 'image':
                // 创建图片消息实例
                $message = ImageMessage::make()->image($data['message']);
                break;
            case 'file':
                // 创建文件消息实例，并设置媒体ID
                $message = FileMessage::make()->mediaId($data['media_id']);
                break;
            case 'upload_media':
                // 创建上传媒体文件消息实例
                $message = UploadMediaMessage::make($data['message']);
                break;
            case 'voice':
                // 创建语音消息实例，并设置媒体ID
                $message = VoiceMessage::make()->mediaId($data['media_id']);
                break;
            case 'template_card':
                // 创建模板卡片消息实例
                $message = TemplateCardMessage::make($data['message']);
                break;
            default:
                // 抛出异常，表示不支持的消息类型
                throw new Exception("Unsupported WeWork message type: {$data['type']}");
        }
        // 使用客户端发送消息
        $this->client->send($message);
    }

    public function toText($notifiable)
    {
        return [
            'type' => 'text',
            'message' => [
                'content' => '这是一条文本消息。',
                'mentioned_list' => ['@all'], // 提及的用户
                'mentioned_mobile_list' => ['13800001111'], // 提及的用户手机号
            ],
        ];
    }

    public function toMarkdown($notifiable)
    {
        return [
            'type' => 'markdown',
            'message' => "# 标题\n这是 *Markdown* 消息内容。",
        ];
    }

    public function toNews($notifiable)
    {
        return [
            'type' => 'news',
            'message' => [
                'title' => '这是标题',
                'description' => '这是描述内容。',
                'url' => 'https://example.com',
                'picurl' => 'https://example.com/image.jpg',
            ],
        ];
    }

    public function toImage($notifiable)
    {
        return [
            'type' => 'image',
            'message' => 'path/to/image.png', // 图片文件路径
        ];
    }

    public function toFile($notifiable)
    {
        return [
            'type' => 'file',
            'media_id' => 'your-media-id', // 文件的媒体ID
        ];
    }

    public function toUpload_media($notifiable)
    {
        return [
            'type' => 'upload_media',
            'message' => [
                'media' => 'path/to/file.png', // 媒体文件路径
                'type' => 'file', // 媒体类型
            ],
        ];
    }

    public function toVoice($notifiable)
    {
        return [
            'type' => 'voice',
            'media_id' => 'your-media-id', // 语音文件的媒体ID
        ];
    }

    public function toTemplate_card($notifiable)
    {
        //
        return [
            'type' => 'template_card',
            'message' => [
                'card_type' => 'text_notice', // 卡片类型
                'source' => [
                    'icon_url' => 'https://example.com/icon.png',
                    'desc' => '企业微信',
                    'desc_color' => 0,
                ],
                'main_title' => [
                    'title' => '欢迎使用企业微信',
                    'desc' => '这是主标题的描述',
                ],
                'emphasis_content' => [
                    'title' => '100',
                    'desc' => '数据含义',
                ],
                'sub_title_text' => '副标题文本',
                'horizontal_content_list' => [
                    [
                        'keyname' => '项目名称',
                        'value' => '项目值',
                    ],
                ],
                'card_action' => [
                    'type' => 1,
                    'url' => 'https://example.com',
                ],
            ],
        ];
    }
}
