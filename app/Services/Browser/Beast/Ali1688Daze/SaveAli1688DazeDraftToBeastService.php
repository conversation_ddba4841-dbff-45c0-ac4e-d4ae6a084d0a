<?php

namespace App\Services\Browser\Beast\Ali1688Daze;

use App\Enums\State\BeastStateEnum;
use App\Models\BeastCenter\Beast;
use App\Models\IndustryBrace\Category;
use App\Models\PinCenter\Pin;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class SaveAli1688DazeDraftToBeastService extends BaseService
{
    /**
     * 批量将指定的 Beast 数据发布（转换并入库）到 Spare
     * @param $BeastData
     * @param $Source
     * @param $keywords
     * @return void
     * @throws Throwable
     */
    public static function batchFetchDraftBeast($BeastData, $Source = [], $keywords = []): void
    {
        //
        // 如果你的前端是直接传数组而不是 JSON 字符串，则不需要 json_decode，直接用 $request->input('form.beast_json') 即可。
        // 但这里假设它是字符串，需要 decode。且默认返回的是 stdClass 对象数组。
        DB::beginTransaction();
        //
        try {
            // $BeastData 就是一个 stdClass 对象，属性通过 -> 访问
            $Beast = Beast::where('draft_no', $BeastData['draftBoxId'])->first();
            $Category = Category::where('ali1688_cat_no', $BeastData['categoryId'])->first();
            //
            if (!isset($Beast)) {
                // 没有则新建
                $Beast = new Beast();
            }
            // 以下为一对一赋值（使用对象属性而非数组下标）
            $Beast->category_id = $Beast->category_id ?? $Category->id ?? null;
            $Beast->source_id = $Beast->source_id ?? $Source->id ?? null;
            $Beast->beast_category_no = $BeastData['categoryId'] ?? $Beast->beast_category_no ?? null;
            $Beast->beast_category_name = $BeastData['categoryName'] ?? $Beast->beast_category_name ?? null;
            $Beast->group_name = $BeastData['categoryGroupName'] ?? $Beast->group_name ?? null;
            //
            $Beast->beast_subject = $Beast->beast_subject ?? $BeastData['subject'] ?? null;
            $Beast->beast_title = $Beast->beast_title ?? $BeastData['subject'] ?? null;
            $Beast->beast_number = $Beast->beast_number ?? $BeastData['cargoNumber'] ?? null;
            $Beast->beast_images = json_decode($BeastData['images']) ?? $Beast->beast_images ?? null;
            $Beast->beast_preview = $Beast->beast_preview ?? (isset($BeastData['images']) ? json_decode($BeastData['images'])[0] : $Beast->beast_preview) ?? null;
            $Beast->beast_image = $Beast->beast_image ?? (isset($BeastData['images']) ? json_decode($BeastData['images'])[0] : $Beast->beast_image) ?? null;
            //
            $Beast->from_data = $BeastData['dataFrom'] ?? $Beast->from_data ?? null;
            $Beast->beast_json = json_decode($BeastData['productInfoJson']) ?? $Beast->beast_json ?? null;
            $Beast->product_json = json_decode($BeastData['showProductInfoJson']) ?? $Beast->product_json ?? null;
            $Beast->relation_json = json_decode($BeastData['relationDataJson']) ?? $Beast->relation_json ?? null;
            //
            $Beast->amount_sale = $BeastData['amountOnSale'] ?? $Beast->amount_sale ?? null;
            $Beast->price_ranges = json_decode($BeastData['priceRanges']) ?? $Beast->price_ranges ?? null;
            $Beast->full_tips = $BeastData['dataFullTips'] ?? $Beast->full_tips ?? null;
            $Beast->beast_scene = $BeastData['productScene'] ?? $Beast->beast_scene ?? null;
            //
            $Beast->publish_id = $BeastData['publishProductId'] ?? $Beast->publish_id ?? null;
            $Beast->draft_no = $BeastData['draftBoxId'] ?? $Beast->draft_no ?? null;
            $Beast->relation_no = $BeastData['relationDataId'] ?? $Beast->relation_no ?? null;
            $Beast->plan_no = $BeastData['planNo'] ?? $Beast->plan_no ?? null;
            $Beast->record_no = $BeastData['detailMakingRecordId'] ?? $Beast->record_no ?? null;
            //
            $Beast->create_time = $BeastData['createTime'] ?? $Beast->create_time ?? null;
            $Beast->modify_time = $BeastData['modifyTime'] ?? $Beast->modify_time ?? null;
            $Beast->timing_time = $BeastData['timingPublishTime'] ?? $Beast->timing_time ?? null;
            //
            $Beast->is_product = $BeastData['isIdentityProduct'] ?? 0;
            $Beast->is_identity = $BeastData['isIdentity'] ?? 0;
            $Beast->is_trade = $BeastData['isSupportOnlineTrade'] ?? 0;
            $Beast->is_mix = $BeastData['isSupportMix'] ?? 0;
            $Beast->is_full = $BeastData['isDataFull'] ?? 0;
            //
            $Beast->publish_tips = $BeastData['publishTips'] ?? $Beast->publish_tips ?? null;
            $Beast->publish_state = $BeastData['timingPublishState'] ?? $Beast->publish_state ?? null;
            // 保存
            $Beast->save();
            // 如果需要确保从数据库中刷新最新的值，可以调用 refresh()
            $Beast->refresh();
            //
            if ($Beast->beast_state < BeastStateEnum::draft()) {
                //
                if ($Beast->beast_state > BeastStateEnum::selected()) {
                    //
                    if (count($Beast->beast_json) > 0 && $Beast->is_full) {
                        //
                        $Beast->is_valid = 1;
                        $Beast->beast_state = BeastStateEnum::draft();
                    } else {
                        $Beast->is_valid = 0;
                        $Beast->beast_state = BeastStateEnum::expired();
                    }
                } else {
                    //
                    $Beast->beast_state = BeastStateEnum::review();
                }
            }
            //
            if (isset($Beast->pin_no) && $Beast->is_full) {
                // 没有则新建
                $Pin = Pin::where('pin_no', $Beast->pin_no)->first();
                $Pin->is_rice = 1;
                $Pin->save();
            }
            // 保存
            $Beast->save();
            // 提交事务
            DB::commit();
        } catch (Exception $e) {
            // 回滚事务
            DB::rollBack();
            // 捕获异常并记录日志
            Log::error('Error storing Beast data', ['error' => $e->getMessage(), 'BeastData' => $Beast]);
            // 打印出具体的错误信息和Beast对象的详细数据
            dd($e->getMessage(), $Beast->toArray()); // 使用 dd() 查看详细的Beast数据
        }
    }
}
