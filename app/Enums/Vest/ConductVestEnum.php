<?php

namespace App\Enums\Vest;

use App\Enums\BaseEnum;


/**
 * Class ConductVestEnum
 * @package App\Enums\Vest\Conduct
 * @method static self default() 默认
 * @method static self User() 用户
 * @method static self Store() 商家
 * @method static self Provider() 服务商
 * @method static self Supplier() 供应商
 * @method static self Founder() 品牌商
 * @method static self Merchant() 批发商
 * @method static self Sale() 销售商
 * @method static self Transport() 运输商
 * @method static self Soft() 软件服务商
 * @method static self Platform() 平台
 * @method static self Site() 站点
 * @method static self Retail() 渠道
 * @method static self Insurer() 保险公司
 * @method static self Designer() 设计师
 */
final class ConductVestEnum extends BaseEnum
{


    //
    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'User' => 1,
            'Store' => 2,
            'Provider' => 3,
            'Supplier' => 4,
            'Founder' => 5,
            'Merchant' => 6,
            'Sale' => 7,
            'Transport' => 8,
            'Soft' => 9,
            'Platform' => 10,
            'Site' => 11,
            'Retail' => 12,
            'Insurer' => 13,
            'Designer' => 14,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'User' => '用户',
            'Store' => '商家',
            'Provider' => '服务商',
            'Supplier' => '供应商',
            'Founder' => '品牌商',
            'Merchant' => '批发商',
            'Sale' => '销售商',
            'Transport' => '运输商',
            'Soft' => '软件服务商',
            'Platform' => '平台',
            'Site' => '站点',
            'Retail' => '渠道',
            'Insurer' => '保险公司',
            'Designer' => '设计师',
        ];
    }

    // Define color mapping

    public function color(): string
    {
        //
        $value = $this->value;
        $colors = static::colors();
        return $colors[$value] ?? 'default_color';
    }

    protected static function colors(): array
    {
        //
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
            6 => 'blue',
            7 => 'indigo',
            8 => 'cyan',
            9 => 'teal',
            10 => 'pink',
            11 => 'brown',
            12 => 'grey',
            13 => 'lime',
            14 => 'amber',
        ];
    }
}
