import {defineConfig} from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import {fileURLToPath} from "node:url";
import {visualizer} from 'rollup-plugin-visualizer';

export default defineConfig({
                                plugins: [
                                    laravel({
                                                input: ['resources/css/base.scss', 'resources/css/layouts.scss', 'resources/css/client-layout.scss', 'resources/js/admin/admin.js'],
                                                refresh: true,
                                            }),
                                    vue({
                                            template: {
                                                transformAssetUrls: {
                                                    base: null, // 不修改资源的 base 路径
                                                    includeAbsolute: false, // 禁止生成绝对路径的链接
                                                },
                                            },
                                        }),
                                    // 构建分析插件 - 仅在分析模式下启用
                                    process.env.ANALYZE && visualizer({
                                                                          filename: 'dist/stats.html',
                                                                          open: true,
                                                                          gzipSize: true,
                                                                          brotliSize: true,
                                                                      }),
                                ].filter(Boolean),

                                resolve: {
                                    alias: {
                                        vue: 'vue/dist/vue.esm-bundler.js',
                                        '@': fileURLToPath(new URL('./resources/js', import.meta.url)), // 设置 @ 指向 js 目录
                                        '@component': fileURLToPath(new URL('./resources/component/', import.meta.url)), // 设置 @component 指向 component 目录
                                        '@images': fileURLToPath(new URL('./resources/images/', import.meta.url)), // 设置 @images 指向 images 目录
                                        '@css': fileURLToPath(new URL('./resources/css/', import.meta.url)), // 设置 @css 指向 css 目录
                                        '@utils': fileURLToPath(new URL('./resources/utils/', import.meta.url)), // 设置 @css 指向 css 目录
                                        '@layouts': fileURLToPath(new URL('./resources/layouts/', import.meta.url)), // 设置 @css 指向 css 目录
                                        '@stores': fileURLToPath(new URL('./resources/stores/', import.meta.url)), // 设置 @css 指向 css 目录
                                    },
                                },

                                define: {
                                    __VUE_OPTIONS_API__: true,
                                    __VUE_PROD_DEVTOOLS__: false,
                                },

                                // 构建优化配置
                                build: {
                                    // 设置 chunk 大小警告限制为 1000KB (1MB)
                                    chunkSizeWarningLimit: 1000,

                                    // 启用 CSS 代码分割
                                    cssCodeSplit: true,
                                    minify: false, // 关闭压缩，便于排查 CSS 语法
                                    // 启用源码映射（生产环境可选）
                                    sourcemap: process.env.NODE_ENV === 'development',

                                    // Rollup 构建选项
                                    rollupOptions: {
                                        output: {
                                            // 手动分块策略 - 将大型依赖库分离到独立的 chunk
                                            manualChunks: {
                                                // Vue 生态系统
                                                'vue-vendor': ['vue', 'vue-router', 'pinia'],

                                                // Element Plus UI 框架
                                                'element-plus': ['element-plus', '@element-plus/icons-vue'],

                                                // 图表库
                                                'charts': [
                                                    'chart.js',
                                                    'vue-chartjs',
                                                    'apexcharts-clevision',
                                                    'vue3-apexcharts'
                                                ],

                                                // 工具库
                                                'utils': [
                                                    'axios',
                                                    '@vueuse/core',
                                                    '@vueuse/math',
                                                    'js-md5',
                                                    'jwt-decode'
                                                ],

                                                // UI 增强库
                                                'ui-libs': [
                                                    '@floating-ui/dom',
                                                    'vue-flatpickr-component',
                                                    'vue3-perfect-scrollbar',
                                                    'prismjs',
                                                    'vue-prism-component'
                                                ],

                                                // 权限和安全
                                                'auth': ['@casl/ability', '@casl/vue'],
                                            },

                                            // 文件命名策略
                                            chunkFileNames: (chunkInfo) => {
                                                const facadeModuleId = chunkInfo.facadeModuleId
                                                                       ? chunkInfo.facadeModuleId.split('/').pop().replace(/\.[^/.]+$/, '')
                                                                       : 'chunk';
                                                return `js/${facadeModuleId}-[hash].js`;
                                            },
                                            entryFileNames: 'js/[name]-[hash].js',
                                            assetFileNames: (assetInfo) => {
                                                const info = assetInfo.name.split('.');
                                                const ext = info[info.length - 1];
                                                if (/\.(css|scss|sass|less|styl)$/.test(assetInfo.name)) {
                                                    return `css/[name]-[hash].${ext}`;
                                                }
                                                if (/\.(png|jpe?g|gif|svg|webp|avif)$/.test(assetInfo.name)) {
                                                    return `images/[name]-[hash].${ext}`;
                                                }
                                                if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name)) {
                                                    return `fonts/[name]-[hash].${ext}`;
                                                }
                                                return `assets/[name]-[hash].${ext}`;
                                            },
                                        },

                                        // 外部依赖处理（如果需要 CDN 加载）
                                        external: [],
                                    },

                                    // 压缩配置
                                    // minify: 'terser',
                                    terserOptions: {
                                        compress: {
                                            // 移除 console.log（生产环境）
                                            drop_console: process.env.NODE_ENV === 'production',
                                            drop_debugger: true,
                                            // 移除未使用的代码
                                            dead_code: true,
                                            // 优化条件表达式
                                            conditionals: true,
                                        },
                                        mangle: {
                                            // 保留类名（用于调试）
                                            keep_classnames: process.env.NODE_ENV === 'development',
                                            keep_fnames: process.env.NODE_ENV === 'development',
                                        },
                                    },
                                },

                                // 开发服务器配置
                                server: {
                                    hmr: {
                                        overlay: false,
                                    },
                                },

                                // 优化依赖预构建
                                optimizeDeps: {
                                    include: [
                                        'vue',
                                        'vue-router',
                                        'pinia',
                                        'element-plus',
                                        '@element-plus/icons-vue',
                                        'axios',
                                    ],
                                    exclude: [
                                        // 排除不需要预构建的大型库
                                    ],
                                },
                            });
