<?php

namespace Tests\Feature\Auth;

use App\Helpers\Auth\AuthHelper;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\UserCenter\User;
use App\Services\Auth\AuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

/**
 * 增强认证系统测试类
 * 
 * 测试增强认证系统的各项功能，包括：
 * - 用户登录和注册
 * - 密码验证和安全检查
 * - 会话管理
 * - 频率限制
 * - 助手类功能
 * 
 * @package Tests\Feature\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class EnhancedAuthTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);
    }

    /**
     * 测试用户登录功能
     */
    public function test_user_can_login_successfully(): void
    {
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect(route('dashboard', absolute: false));
    }

    /**
     * 测试用户登录失败
     */
    public function test_user_cannot_login_with_invalid_credentials(): void
    {
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ]);

        $this->assertGuest();
        $response->assertSessionHasErrors(['email']);
    }

    /**
     * 测试用户注册功能
     */
    public function test_user_can_register_successfully(): void
    {
        $response = $this->post('/register', [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect(route('dashboard', absolute: false));
        
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'New User',
        ]);
    }

    /**
     * 测试用户注册验证
     */
    public function test_user_registration_validation(): void
    {
        // 测试邮箱重复
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>', // 已存在的邮箱
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertSessionHasErrors(['email']);
        $this->assertGuest();
    }

    /**
     * 测试用户登出功能
     */
    public function test_user_can_logout(): void
    {
        $this->actingAs($this->testUser);
        
        $response = $this->post('/logout');
        
        $this->assertGuest();
        $response->assertRedirect('/');
    }

    /**
     * 测试AuthService登录功能
     */
    public function test_auth_service_login(): void
    {
        $authService = new AuthService();
        $request = Request::create('/login', 'POST');
        
        $result = $authService->login([
            'email' => '<EMAIL>',
            'password' => 'password123'
        ], false, $request);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(User::class, $result['user']);
        $this->assertAuthenticated();
    }

    /**
     * 测试AuthService注册功能
     */
    public function test_auth_service_register(): void
    {
        $authService = new AuthService();
        $request = Request::create('/register', 'POST');
        
        $result = $authService->register([
            'name' => 'Service User',
            'email' => '<EMAIL>',
            'password' => 'password123'
        ], $request);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(User::class, $result['user']);
        $this->assertAuthenticated();
        
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Service User',
        ]);
    }

    /**
     * 测试AuthService登出功能
     */
    public function test_auth_service_logout(): void
    {
        $this->actingAs($this->testUser);
        
        $authService = new AuthService();
        $request = Request::create('/logout', 'POST');
        
        $result = $authService->logout($request);

        $this->assertTrue($result['success']);
        $this->assertGuest();
    }

    /**
     * 测试AuthHelper用户状态检查
     */
    public function test_auth_helper_user_status(): void
    {
        // 测试未认证状态
        $this->assertFalse(AuthHelper::isAuthenticated());
        $this->assertTrue(AuthHelper::isGuest());
        $this->assertNull(AuthHelper::getCurrentUser());
        $this->assertNull(AuthHelper::getCurrentUserId());

        // 测试已认证状态
        $this->actingAs($this->testUser);
        
        $this->assertTrue(AuthHelper::isAuthenticated());
        $this->assertFalse(AuthHelper::isGuest());
        $this->assertInstanceOf(User::class, AuthHelper::getCurrentUser());
        $this->assertEquals($this->testUser->id, AuthHelper::getCurrentUserId());
        $this->assertEquals($this->testUser->email, AuthHelper::getCurrentUserEmail());
        $this->assertEquals($this->testUser->name, AuthHelper::getCurrentUserName());
    }

    /**
     * 测试密码强度验证
     */
    public function test_password_strength_validation(): void
    {
        // 测试弱密码
        $weakResult = AuthHelper::validatePasswordStrength('123');
        $this->assertFalse($weakResult['valid']);
        $this->assertGreaterThan(0, count($weakResult['messages']));

        // 测试强密码
        $strongResult = AuthHelper::validatePasswordStrength('StrongP@ssw0rd123');
        $this->assertTrue($strongResult['valid']);
        $this->assertGreaterThan(3, $strongResult['score']);

        // 测试常见弱密码
        $commonWeakResult = AuthHelper::validatePasswordStrength('password');
        $this->assertFalse($commonWeakResult['valid']);
    }

    /**
     * 测试安全密码生成
     */
    public function test_secure_password_generation(): void
    {
        $password = AuthHelper::generateSecurePassword(12, true);
        
        $this->assertEquals(12, strlen($password));
        
        // 验证生成的密码强度
        $validation = AuthHelper::validatePasswordStrength($password);
        $this->assertGreaterThan(3, $validation['score']);
    }

    /**
     * 测试密码检查功能
     */
    public function test_password_checking(): void
    {
        $password = 'testpassword123';
        $hashedPassword = AuthHelper::hashPassword($password);
        
        $this->assertTrue(AuthHelper::checkPassword($password, $hashedPassword));
        $this->assertFalse(AuthHelper::checkPassword('wrongpassword', $hashedPassword));
    }

    /**
     * 测试登录状态信息
     */
    public function test_login_status_info(): void
    {
        // 测试未登录状态
        $status = AuthHelper::getLoginStatus();
        $this->assertFalse($status['authenticated']);
        $this->assertNull($status['user']);

        // 测试已登录状态
        $this->actingAs($this->testUser);
        Session::put('login_time', now());
        Session::put('last_activity_time', now());
        Session::put('login_ip', '127.0.0.1');
        
        $status = AuthHelper::getLoginStatus();
        $this->assertTrue($status['authenticated']);
        $this->assertInstanceOf(User::class, $status['user']);
        $this->assertNotNull($status['login_time']);
        $this->assertEquals('127.0.0.1', $status['login_ip']);
    }

    /**
     * 测试会话过期检查
     */
    public function test_session_expiry_check(): void
    {
        // 设置最后活动时间为很久以前
        Session::put('last_activity_time', now()->subHours(3));
        
        $this->assertTrue(AuthHelper::isSessionNearExpiry(10));
        $this->assertEquals(0, AuthHelper::getSessionRemainingMinutes());
    }

    /**
     * 测试LoginRequest频率限制
     */
    public function test_login_request_rate_limiting(): void
    {
        $request = new LoginRequest();
        $request->merge([
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);
        
        // 模拟请求
        $request->setMethod('POST');
        $request->server->set('REMOTE_ADDR', '127.0.0.1');
        
        $throttleKey = $request->throttleKey();
        $this->assertIsString($throttleKey);
        $this->assertStringContainsString('<EMAIL>', $throttleKey);
        $this->assertStringContainsString('127.0.0.1', $throttleKey);
        
        // 测试尝试次数
        $this->assertEquals(0, $request->getAttempts());
        $this->assertEquals(5, $request->getRemainingAttempts());
        $this->assertFalse($request->isNearRateLimit());
    }

    /**
     * 测试用户活动记录
     */
    public function test_user_activity_logging(): void
    {
        $this->actingAs($this->testUser);
        
        // 这个测试主要验证方法不会抛出异常
        AuthHelper::logUserActivity('test_activity', ['test_data' => 'value']);
        
        // 由于日志记录是异步的，我们主要测试方法调用成功
        $this->assertTrue(true);
    }

    /**
     * 测试会话数据清理
     */
    public function test_session_data_cleanup(): void
    {
        // 设置一些会话数据
        Session::put('login_success', true);
        Session::put('login_time', now());
        Session::put('failed_login_attempts', 3);
        
        // 清理会话数据
        AuthHelper::clearUserSession();
        
        // 验证数据已被清理
        $this->assertNull(Session::get('login_success'));
        $this->assertNull(Session::get('login_time'));
        $this->assertNull(Session::get('failed_login_attempts'));
    }

    /**
     * 测试可信IP检查
     */
    public function test_trusted_ip_check(): void
    {
        // 测试本地IP（通常在可信列表中）
        $this->assertTrue(AuthHelper::isTrustedIp('127.0.0.1'));
        
        // 测试外部IP
        $this->assertFalse(AuthHelper::isTrustedIp('*************'));
    }

    /**
     * 清理测试环境
     */
    protected function tearDown(): void
    {
        // 清理会话数据
        Session::flush();
        
        parent::tearDown();
    }
}
