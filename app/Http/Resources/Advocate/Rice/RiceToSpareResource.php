<?php

namespace App\Http\Resources\Advocate\Rice;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RiceToSpareResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'rice_uuid' => $this->uuid ?? null,
            //
            'store_name' => $this->store->store_name ?? null,
            //
            'rice_title' => $this->rice_title ?? '无标题',
            'rice_number' => $this->rice_number ?? '无货号',
            //
            'rice_subject' => static::getTitle($this->keywords()->select('word')->pluck('word')->toArray(), 60) ?? null,
            'rice_corns' => isset($this->rice_corns) ? RiceCornToSpareSaleResource::collection($this->rice_corns()->orderBy('id', 'desc')->get()) : null,
            //
            'rice_preview' => $this->rice_preview ?? null,
            'rice_images' => $this->rice_images ?? null,
            'rice_descries' => count($this->rice_descries) ? $this->rice_descries : $this->rice_images,
            //
            'keywords' => isset($this->keywords) ? $this->keywords()->get()->pluck('word') : null,
            'keyword' => isset($this->keywords) ? $this->keywords()->get()->pluck('word') : null,
            //
            'rice_name' => $this->rice_name ?? $this->rice_title ?? '无标题',
            'rice_image_url' => $this->rice_image_url ?? null,
            'rice_description' => $this->rice_description ?? null,
        ]);
    }

    /**
     * 设置产品标题
     * @param $subTitle
     * @param $groupName
     * @param $keywords
     * @return string
     */
    public static function getTitle($titleParts, $length = 60)
    {
        // 去除重复的词组
        $allParts = array_unique($titleParts);
        // 随机打乱词组顺序
        shuffle($allParts);
        // 初始化标题
        $title = '适用';
        // 处理长度为 2 的词汇，确保不重复
        $usedTwoCharWords = [];  // 用于记录已使用的两个字的词汇
        $allParts = array_map(function ($part) use (&$usedTwoCharWords) {
            // 如果是长度为2的词且之前未出现过，则返回它
            if (mb_strlen($part) === 2) {
                if (!in_array($part, $usedTwoCharWords)) {
                    $usedTwoCharWords[] = $part;  // 记录已使用的词汇
                    return $part;
                }
                return null;  // 如果是重复的长度为 2 的词汇，跳过
            }
            return $part;  // 其他词汇直接返回
        }, $allParts);

        // 移除 null 的元素
        $allParts = array_filter($allParts);

        // 去重处理
        $allParts = array_unique($allParts);

        // 遍历其他词组，检查是否已经包含在标题中
        foreach ($allParts as $part) {
            // 去除 "系列" 两个字
            $part = str_replace('系列', '', $part);
            $part = str_replace('无', '', $part);
            // 检查当前标题是否已经包含该部分
            if (mb_strpos($title, $part) === false && mb_strlen($title . $part) <= $length) {
                // 如果没有包含且拼接后不会超长，则拼接该词组
                $title .= $part;
            }
        }
        // 返回拼接后的标题
        return $title;
    }
}
