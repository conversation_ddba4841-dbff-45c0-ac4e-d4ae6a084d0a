<?php

/**
 * 微信服务配置文件
 *
 * 此配置文件包含了微信公众号、小程序等服务的配置选项
 * 包括认证信息、API配置、回调设置等
 *
 * @package Config
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */

return [

    /*
    |--------------------------------------------------------------------------
    | 微信公众号配置
    |--------------------------------------------------------------------------
    |
    | 微信公众号相关的配置信息
    |
    */

    'official_account' => [
        // 基础配置
        'app_id' => env('WECHAT_OFFICIAL_ACCOUNT_APPID'),
        'app_secret' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET'),
        'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN'),
        'aes_key' => env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY'),

        // 服务器配置
        'server' => [
            'url' => env('WECHAT_OFFICIAL_ACCOUNT_SERVER_URL', '/api/wechat/callback'),
            'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN'),
            'aes_key' => env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY'),
        ],

        // OAuth配置
        'oauth' => [
            'scopes' => ['snsapi_userinfo'],
            'callback' => env('WECHAT_OFFICIAL_ACCOUNT_OAUTH_CALLBACK', '/auth/wechat/callback'),
        ],

        // 支付配置（如果需要）
        'payment' => [
            'merchant_id' => env('WECHAT_PAYMENT_MERCHANT_ID'),
            'key' => env('WECHAT_PAYMENT_KEY'),
            'cert_path' => env('WECHAT_PAYMENT_CERT_PATH'),
            'key_path' => env('WECHAT_PAYMENT_KEY_PATH'),
            'notify_url' => env('WECHAT_PAYMENT_NOTIFY_URL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 微信小程序配置
    |--------------------------------------------------------------------------
    |
    | 微信小程序相关的配置信息
    |
    */

    'mini_program' => [
        'app_id' => env('WECHAT_MINI_PROGRAM_APPID'),
        'app_secret' => env('WECHAT_MINI_PROGRAM_SECRET'),
        'token' => env('WECHAT_MINI_PROGRAM_TOKEN'),
        'aes_key' => env('WECHAT_MINI_PROGRAM_AES_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 微信开放平台配置
    |--------------------------------------------------------------------------
    |
    | 微信开放平台相关的配置信息
    |
    */

    'open_platform' => [
        'app_id' => env('WECHAT_OPEN_PLATFORM_APPID'),
        'app_secret' => env('WECHAT_OPEN_PLATFORM_SECRET'),
        'token' => env('WECHAT_OPEN_PLATFORM_TOKEN'),
        'aes_key' => env('WECHAT_OPEN_PLATFORM_AES_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 企业微信配置
    |--------------------------------------------------------------------------
    |
    | 企业微信相关的配置信息
    |
    */

    'work' => [
        'corp_id' => env('WECHAT_WORK_CORP_ID'),
        'agent_id' => env('WECHAT_WORK_AGENT_ID'),
        'secret' => env('WECHAT_WORK_SECRET'),
        'token' => env('WECHAT_WORK_TOKEN'),
        'aes_key' => env('WECHAT_WORK_AES_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | API配置
    |--------------------------------------------------------------------------
    |
    | 微信API相关的配置选项
    |
    */

    'api' => [
        // API基础URL
        'base_url' => 'https://api.weixin.qq.com/cgi-bin',

        // 请求超时时间（秒）
        'timeout' => env('WECHAT_API_TIMEOUT', 30),

        // 重试次数
        'retry_times' => env('WECHAT_API_RETRY_TIMES', 3),

        // 重试延迟（毫秒）
        'retry_delay' => env('WECHAT_API_RETRY_DELAY', 1000),

        // 是否验证SSL证书
        'verify_ssl' => env('WECHAT_API_VERIFY_SSL', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | 缓存配置
    |--------------------------------------------------------------------------
    |
    | 微信相关缓存配置
    |
    */

    'cache' => [
        // 缓存前缀
        'prefix' => env('WECHAT_CACHE_PREFIX', 'wechat'),

        // 缓存驱动
        'store' => env('WECHAT_CACHE_STORE', 'default'),

        // Access Token缓存时间（秒）
        'access_token_ttl' => env('WECHAT_ACCESS_TOKEN_TTL', 7200),

        // JS API Ticket缓存时间（秒）
        'jsapi_ticket_ttl' => env('WECHAT_JSAPI_TICKET_TTL', 7200),

        // 用户信息缓存时间（秒）
        'user_info_ttl' => env('WECHAT_USER_INFO_TTL', 3600),
    ],

    /*
    |--------------------------------------------------------------------------
    | 二维码配置
    |--------------------------------------------------------------------------
    |
    | 微信二维码相关配置
    |
    */

    'qrcode' => [
        // 临时二维码默认过期时间（秒）
        'temp_expire_seconds' => env('WECHAT_QRCODE_TEMP_EXPIRE', 604800), // 7天

        // 二维码图片存储路径
        'storage_path' => env('WECHAT_QRCODE_STORAGE_PATH', 'qrcodes'),

        // 是否自动下载二维码图片
        'auto_download' => env('WECHAT_QRCODE_AUTO_DOWNLOAD', false),

        // 二维码图片质量
        'image_quality' => env('WECHAT_QRCODE_IMAGE_QUALITY', 80),
    ],

    /*
    |--------------------------------------------------------------------------
    | 消息配置
    |--------------------------------------------------------------------------
    |
    | 微信消息处理相关配置
    |
    */

    'message' => [
        // 消息加密模式
        'encryption_mode' => env('WECHAT_MESSAGE_ENCRYPTION_MODE', 'safe'), // safe, compatible, plain

        // 是否启用消息去重
        'enable_deduplication' => env('WECHAT_MESSAGE_ENABLE_DEDUPLICATION', true),

        // 消息去重时间窗口（秒）
        'deduplication_window' => env('WECHAT_MESSAGE_DEDUPLICATION_WINDOW', 300),

        // 默认回复消息
        'default_reply' => env('WECHAT_MESSAGE_DEFAULT_REPLY', '感谢您的消息，我们会尽快回复。'),

        // 关注回复消息
        'subscribe_reply' => env('WECHAT_MESSAGE_SUBSCRIBE_REPLY', '欢迎关注我们的公众号！'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 菜单配置
    |--------------------------------------------------------------------------
    |
    | 微信自定义菜单配置
    |
    */

    'menu' => [
        // 是否启用自定义菜单
        'enabled' => env('WECHAT_MENU_ENABLED', true),

        // 菜单配置文件路径
        'config_file' => env('WECHAT_MENU_CONFIG_FILE', 'wechat_menu.json'),

        // 是否自动创建菜单
        'auto_create' => env('WECHAT_MENU_AUTO_CREATE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | 日志配置
    |--------------------------------------------------------------------------
    |
    | 微信相关日志配置
    |
    */

    'logging' => [
        // 是否启用日志
        'enabled' => env('WECHAT_LOGGING_ENABLED', true),

        // 日志级别
        'level' => env('WECHAT_LOGGING_LEVEL', 'info'),

        // 是否记录API请求
        'log_api_requests' => env('WECHAT_LOG_API_REQUESTS', true),

        // 是否记录消息处理
        'log_message_handling' => env('WECHAT_LOG_MESSAGE_HANDLING', true),

        // 是否记录用户操作
        'log_user_actions' => env('WECHAT_LOG_USER_ACTIONS', true),

        // 日志保留天数
        'retention_days' => env('WECHAT_LOG_RETENTION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | 安全配置
    |--------------------------------------------------------------------------
    |
    | 微信安全相关配置
    |
    */

    'security' => [
        // 是否启用IP白名单
        'ip_whitelist_enabled' => env('WECHAT_IP_WHITELIST_ENABLED', false),

        // IP白名单
        'ip_whitelist' => [
            // 微信服务器IP段
            '*************/25',
            '*************/25',
            '************/24',
            '*************/24',
        ],

        // 是否验证消息签名
        'verify_signature' => env('WECHAT_VERIFY_SIGNATURE', true),

        // 是否启用消息加密
        'enable_encryption' => env('WECHAT_ENABLE_ENCRYPTION', false),

        // 签名验证失败时的处理方式
        'signature_fail_action' => env('WECHAT_SIGNATURE_FAIL_ACTION', 'reject'), // reject, log, ignore
    ],

    /*
    |--------------------------------------------------------------------------
    | 开发配置
    |--------------------------------------------------------------------------
    |
    | 开发环境相关配置
    |
    */

    'development' => [
        // 是否启用调试模式
        'debug' => env('WECHAT_DEBUG', false),

        // 是否使用沙箱环境
        'sandbox' => env('WECHAT_SANDBOX', false),

        // 测试用户OpenID列表
        'test_openids' => [
            // 可以添加测试用户的OpenID
        ],

        // 是否模拟微信服务器响应
        'mock_server' => env('WECHAT_MOCK_SERVER', false),

        // 模拟响应延迟（毫秒）
        'mock_delay' => env('WECHAT_MOCK_DELAY', 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | 监控配置
    |--------------------------------------------------------------------------
    |
    | 微信服务监控相关配置
    |
    */

    'monitoring' => [
        // 是否启用监控
        'enabled' => env('WECHAT_MONITORING_ENABLED', false),

        // 监控指标
        'metrics' => [
            'api_calls' => true,
            'response_time' => true,
            'error_rate' => true,
            'user_interactions' => true,
        ],

        // 告警阈值
        'alerts' => [
            'api_error_rate_threshold' => env('WECHAT_ALERT_API_ERROR_RATE_THRESHOLD', 0.1), // 10%
            'response_time_threshold' => env('WECHAT_ALERT_RESPONSE_TIME_THRESHOLD', 5000), // 5秒
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 第三方服务配置
    |--------------------------------------------------------------------------
    |
    | 微信相关的第三方服务配置
    |
    */

    'third_party' => [
        // 微信开发者工具配置
        'devtools' => [
            'enabled' => env('WECHAT_DEVTOOLS_ENABLED', false),
            'port' => env('WECHAT_DEVTOOLS_PORT', 9420),
        ],

        // 微信调试工具配置
        'debugger' => [
            'enabled' => env('WECHAT_DEBUGGER_ENABLED', false),
            'url' => env('WECHAT_DEBUGGER_URL', 'https://mp.weixin.qq.com/debug/'),
        ],
    ],

];
