import {defineStore} from 'pinia'
import {ElNotification} from 'element-plus'

/**
 * 通知系统控制
 * 统一管理所有通知的状态，提供方法来控制显示、隐藏通知等
 */
export const useNotificationStore = defineStore('notification', {
    state: () => ({
        // 通知列表
        notifications: [],
        // 通知配置
        config: {
            maxNotifications: 10,      // 最大通知数量
            defaultDuration: 4500,     // 默认显示时长
            position: 'top-right',     // 显示位置
            showClose: true,           // 显示关闭按钮
            enableSound: true,         // 启用声音
            enableBadge: true          // 启用徽标
        },
        // 未读通知数量
        unreadCount: 0,
        // 通知历史
        history: [],
        // 活跃的 Element Plus 通知实例
        activeInstances: new Map()
    }),

    getters: {
        // 获取活跃通知
        getActiveNotifications: (state) => {
            return state.notifications.filter(n => n.visible)
        },

        // 按类型获取通知
        getNotificationsByType: (state) => (type) => {
            return state.notifications.filter(n => n.type === type)
        },

        // 获取未读通知数量
        getUnreadCount: (state) => state.unreadCount,

        // 获取最新通知
        getLatestNotifications: (state) => (limit = 5) => {
            return state.history.slice(0, limit)
        }
    },

    actions: {
        /**
         * 添加通知
         */
        addNotification(notification) {
            const id = Date.now() + Math.random()
            const newNotification = {
                id,
                type: notification.type || 'info',
                title: notification.title || '',
                message: notification.message || '',
                duration: notification.duration !== undefined ? notification.duration : this.config.defaultDuration,
                showClose: notification.showClose !== false,
                visible: true,
                timestamp: new Date().toISOString(),
                read: false,
                ...notification
            }

            // 检查通知数量限制
            if (this.notifications.length >= this.config.maxNotifications) {
                this.notifications.shift()
            }

            this.notifications.push(newNotification)
            this.unreadCount++

            // 添加到历史记录
            this.history.unshift({...newNotification})
            if (this.history.length > 100) {
                this.history.pop()
            }

            // 使用 Element Plus 显示通知
            const elNotificationInstance = ElNotification({
                title: newNotification.title,
                message: newNotification.message,
                type: newNotification.type,
                duration: newNotification.duration,
                showClose: newNotification.showClose,
                position: this.config.position,
                onClick: () => {
                    // 标记为已读
                    this.markAsRead(id)
                    // 记录用户行为
                    this.recordNotificationClick(newNotification)
                },
                onClose: () => {
                    // 从活跃实例中移除
                    this.activeInstances.delete(id)
                    // 隐藏通知
                    this.hideNotification(id)
                }
            })

            // 保存实例引用
            this.activeInstances.set(id, elNotificationInstance)

            return id
        },

        /**
         * 隐藏通知
         */
        hideNotification(id) {
            const index = this.notifications.findIndex(n => n.id === id)
            if (index > -1) {
                this.notifications[index].visible = false

                // 关闭 Element Plus 通知实例
                const instance = this.activeInstances.get(id)
                if (instance && instance.close) {
                    instance.close()
                }
                this.activeInstances.delete(id)

                // 延迟移除以允许动画播放
                setTimeout(() => {
                    this.removeNotification(id)
                }, 300)
            }
        },

        /**
         * 移除通知
         */
        removeNotification(id) {
            const index = this.notifications.findIndex(n => n.id === id)
            if (index > -1) {
                this.notifications.splice(index, 1)
            }
        },

        /**
         * 标记通知为已读
         */
        markAsRead(id) {
            const notification = this.notifications.find(n => n.id === id)
            if (notification && !notification.read) {
                notification.read = true
                this.unreadCount = Math.max(0, this.unreadCount - 1)
            }
        },

        /**
         * 标记所有通知为已读
         */
        markAllAsRead() {
            this.notifications.forEach(n => {
                if (!n.read) {
                    n.read = true
                }
            })
            this.unreadCount = 0
        },

        /**
         * 记录通知点击行为
         */
        recordNotificationClick(notification) {
            // 这里可以集成行为分析
            console.log('通知被点击:', notification.title || notification.message)
        },

        /**
         * 清除所有通知
         */
        clearAllNotifications() {
            // 关闭所有活跃的通知实例
            this.activeInstances.forEach(instance => {
                if (instance && instance.close) {
                    instance.close()
                }
            })
            this.activeInstances.clear()

            this.notifications = []
            this.unreadCount = 0
        },

        /**
         * 清除通知历史
         */
        clearHistory() {
            this.history = []
        },

        /**
         * 更新通知配置
         */
        updateConfig(config) {
            this.config = {...this.config, ...config}
        },

        /**
         * 显示成功通知
         */
        success(options) {
            if (typeof options === 'string') {
                options = {message: options}
            }
            return this.addNotification({...options, type: 'success'})
        },

        /**
         * 显示警告通知
         */
        warning(options) {
            if (typeof options === 'string') {
                options = {message: options}
            }
            return this.addNotification({...options, type: 'warning'})
        },

        /**
         * 显示错误通知
         */
        error(options) {
            if (typeof options === 'string') {
                options = {message: options}
            }
            return this.addNotification({...options, type: 'error', duration: 0})
        },

        /**
         * 显示信息通知
         */
        info(options) {
            if (typeof options === 'string') {
                options = {message: options}
            }
            return this.addNotification({...options, type: 'info'})
        }
    },

    persist: {
        key: 'notification-settings',
        storage: localStorage,
        paths: ['config', 'unreadCount']
    }
})
