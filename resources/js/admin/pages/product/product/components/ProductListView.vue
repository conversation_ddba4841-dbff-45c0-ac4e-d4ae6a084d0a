<!--
/**
 * 产品列表视图组件
 *
 * 功能特性：
 * - 产品数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 * - 敏感信息脱敏处理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="product-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 产品信息 -->
            <el-table-column
                label="产品信息"
                min-width="280"
            >
                <template #default="{ row }">
                    <div class="product-info">
                        <div class="product-image">
                            <img :alt="row.product_name" :src="row.image_url" @error="handleImageError">
                        </div>
                        <div class="product-details">
                            <div class="product-name">
                                <span class="name">{{ row.product_name }}</span>
                            </div>
                            <div class="product-sku">
                                <i class="fas fa-barcode sku-icon"></i>
                                <span class="sku">{{ row.sku }}</span>
                            </div>
                            <div class="product-brand">
                                <i class="fas fa-tag brand-icon"></i>
                                <span class="brand">{{ row.brand }}</span>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 分类 -->
            <el-table-column
                align="center"
                label="分类"
                prop="category"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getCategoryType(row.category)"
                        size="small"
                    >
                        <i :class="getCategoryIcon(row.category)" class="category-icon"></i>
                        {{ getCategoryName(row.category) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 价格 -->
            <el-table-column
                align="right"
                label="价格"
                prop="price"
                sortable
                width="120"
            >
                <template #default="{ row }">
                    <div class="price-cell">
                        <span class="price">¥{{ formatPrice(row.price) }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 库存 -->
            <el-table-column
                align="center"
                label="库存"
                prop="stock"
                sortable
                width="100"
            >
                <template #default="{ row }">
                    <div class="stock-cell">
                        <span :class="getStockClass(row.stock)">{{ row.stock }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 销量 -->
            <el-table-column
                align="center"
                label="销量"
                prop="sales"
                sortable
                width="100"
            >
                <template #default="{ row }">
                    <div class="sales-cell">
                        <span class="sales">{{ row.sales }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 产品状态 -->
            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        <i :class="getStatusIcon(row.status)" class="status-icon"></i>
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="datetime-cell">
                        <div class="date">{{ formatDate(row.created_at) }}</div>
                        <div class="time">{{ formatTime(row.created_at) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleView(row)"
                    >
                        查看
                    </el-button>
                    <el-button
                        v-if="canEdit(row)"
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        编辑
                    </el-button>
                    <el-dropdown trigger="click">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    v-if="row.status === 'draft'"
                                    @click="handleStatusChange(row, 'online')"
                                >
                                    <i class="fas fa-eye"></i>
                                    上架产品
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'online'"
                                    @click="handleStatusChange(row, 'offline')"
                                >
                                    <i class="fas fa-eye-slash"></i>
                                    下架产品
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'offline'"
                                    @click="handleStatusChange(row, 'online')"
                                >
                                    <i class="fas fa-eye"></i>
                                    重新上架
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'out_of_stock'"
                                    @click="handleStatusChange(row, 'online')"
                                >
                                    <i class="fas fa-box"></i>
                                    补充库存
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="handleDelete(row)">
                                    <i class="fas fa-trash"></i>
                                    删除产品
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'ProductListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'status-change',
        'delete',
        'data-change'
    ],
    data() {
        return {
            statusMap: {
                'online': '在售中',
                'offline': '已下架',
                'draft': '草稿',
                'out_of_stock': '缺货'
            },
            categoryMap: {
                'electronics': '电子产品',
                'clothing': '服装配饰',
                'home': '家居用品',
                'sports': '运动户外'
            }
        }
    },
    methods: {
        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleView(row) {
            this.$emit('view-detail', row)
        },

        // 编辑产品
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 状态变化
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 删除产品
        handleDelete(row) {
            this.$emit('delete', row)
            // 触发数据变化事件
            this.$emit('data-change')
        },

        // 图片加载错误处理
        handleImageError(event) {
            event.target.src = '/images/placeholder-product.png'
        },

        // 格式化价格
        formatPrice(price) {
            return Number(price).toFixed(2)
        },

        // 格式化日期
        formatDate(datetime) {
            return datetime.split(' ')[0]
        },

        // 格式化时间
        formatTime(datetime) {
            return datetime.split(' ')[1]
        },

        // 获取分类名称
        getCategoryName(category) {
            return this.categoryMap[category] || category
        },

        // 获取分类图标
        getCategoryIcon(category) {
            const iconMap = {
                'electronics': 'fas fa-mobile-alt',
                'clothing': 'fas fa-tshirt',
                'home': 'fas fa-home',
                'sports': 'fas fa-running'
            }
            return iconMap[category] || 'fas fa-cube'
        },

        // 获取分类标签类型
        getCategoryType(category) {
            const typeMap = {
                'electronics': 'primary',
                'clothing': 'success',
                'home': 'warning',
                'sports': 'info'
            }
            return typeMap[category] || 'info'
        },

        // 获取库存样式类
        getStockClass(stock) {
            if (stock === 0) return 'stock-empty'
            if (stock < 10) return 'stock-low'
            return 'stock-normal'
        },

        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态图标
        getStatusIcon(status) {
            const iconMap = {
                'online': 'fas fa-eye',
                'offline': 'fas fa-eye-slash',
                'draft': 'fas fa-edit',
                'out_of_stock': 'fas fa-exclamation-triangle'
            }
            return iconMap[status] || 'fas fa-question-circle'
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'online': 'success',
                'offline': 'info',
                'draft': 'warning',
                'out_of_stock': 'danger'
            }
            return typeMap[status] || 'info'
        },

        // 判断是否可以编辑
        canEdit(row) {
            return ['online', 'offline', 'draft'].includes(row.status)
        }
    }
}
</script>

<style lang="scss" scoped>
.product-list-view {
    .admin-table {
        // 产品信息单元格
        .product-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .product-image {
                width: 60px;
                height: 60px;
                border-radius: 6px;
                overflow: hidden;
                border: 1px solid #e4e7ed;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .product-details {
                flex: 1;

                .product-name {
                    margin-bottom: 4px;

                    .name {
                        font-weight: 500;
                        color: #303133;
                        font-size: 14px;
                    }
                }

                .product-sku,
                .product-brand {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    margin-bottom: 2px;
                    font-size: 12px;
                    color: #606266;

                    .sku-icon,
                    .brand-icon {
                        color: #909399;
                        font-size: 11px;
                    }
                }
            }
        }

        // 价格单元格
        .price-cell {
            .price {
                font-weight: 600;
                color: #e6a23c;
                font-size: 14px;
            }
        }

        // 库存单元格
        .stock-cell {
            .stock-normal {
                color: #67c23a;
                font-weight: 500;
            }

            .stock-low {
                color: #e6a23c;
                font-weight: 500;
            }

            .stock-empty {
                color: #f56c6c;
                font-weight: 500;
            }
        }

        // 销量单元格
        .sales-cell {
            .sales {
                color: #606266;
                font-weight: 500;
            }
        }

        // 时间单元格
        .datetime-cell {
            .date {
                font-weight: 500;
                color: #303133;
                margin-bottom: 2px;
            }

            .time {
                font-size: 12px;
                color: #909399;
            }
        }

        // 图标样式
        .category-icon,
        .status-icon {
            margin-right: 4px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .product-list-view {
        .admin-table {
            font-size: 12px;

            .product-info {
                gap: 8px;

                .product-image {
                    width: 50px;
                    height: 50px;
                }

                .product-details {
                    .product-name .name {
                        font-size: 13px;
                    }

                    .product-sku,
                    .product-brand {
                        font-size: 11px;
                    }
                }
            }

            .price-cell .price {
                font-size: 13px;
            }

            .datetime-cell {
                .date {
                    font-size: 12px;
                }

                .time {
                    font-size: 11px;
                }
            }
        }
    }
}
</style>
