<?php

namespace App\Services\Seller;

use App\Http\Resources\Advocate\Produce\ProduceSpecResource;
use App\Models\ProduceBrace\ProduceSpec;
use App\Services\BaseService;

class ProduceSpecService extends BaseService
{
    /**
     * @param $spec_id
     * @return null
     */
    public static function getProduceSpecIdToProduceSpec($spec_id = null)
    {
        //
        $ProduceSpec = ProduceSpec::where('platform_id', '=', config('app.platform_id'))->find($spec_id);
        $produce_spec = new ProduceSpecResource($ProduceSpec);
        //
        return $produce_spec ?? null;
    }
}
