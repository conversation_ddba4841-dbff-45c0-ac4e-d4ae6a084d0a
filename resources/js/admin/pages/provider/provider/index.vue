<!--
/**
 * 供应商管理页面
 *
 * 功能特性：
 * - 企业级供应商管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 供应商状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/provider/provider
 * 页面标题：供应商管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="provider-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索供应商名称、联系人或联系电话'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出供应商
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchApprove"
                    type="warning"
                    @click="handleBatchApprove"
                >
                    <i class="fas fa-check"></i>
                    批量审核 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDisable"
                    type="danger"
                    @click="handleBatchDisable"
                >
                    <i class="fas fa-ban"></i>
                    批量禁用 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="供应商状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in PROVIDER_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="供应商类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="制造商" value="manufacturer"></el-option>
                                <el-option label="批发商" value="wholesaler"></el-option>
                                <el-option label="代理商" value="agent"></el-option>
                                <el-option label="服务商" value="service"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="所在地区">
                            <el-select v-model="filters.region" clearable placeholder="选择地区">
                                <el-option label="全部地区" value=""></el-option>
                                <el-option label="华北地区" value="north"></el-option>
                                <el-option label="华东地区" value="east"></el-option>
                                <el-option label="华南地区" value="south"></el-option>
                                <el-option label="华中地区" value="central"></el-option>
                                <el-option label="西南地区" value="southwest"></el-option>
                                <el-option label="西北地区" value="northwest"></el-option>
                                <el-option label="东北地区" value="northeast"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="注册日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 供应商列表 -->
            <template #default>
                <ProviderListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总供应商: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已认证: <strong>{{ approvedCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import ProviderListView from './components/ProviderListView.vue'

export default {
    name: 'AdminProviderProviderIndexPage',
    components: {
        BackendPageListLayout,
        ProviderListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                region: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            approvedCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部供应商', icon: 'fas fa-list', badge: 0},
                {name: 'pending', label: '待审核', icon: 'fas fa-clock', badge: 0},
                {name: 'approved', label: '已认证', icon: 'fas fa-check-circle', badge: 0},
                {name: 'rejected', label: '已拒绝', icon: 'fas fa-times-circle', badge: 0},
                {name: 'disabled', label: '已禁用', icon: 'fas fa-ban', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 供应商状态标签
            PROVIDER_STATUS_LABELS: {
                'pending': '待审核',
                'approved': '已认证',
                'rejected': '已拒绝',
                'disabled': '已禁用',
                'suspended': '已暂停'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchApprove() {
            return this.selectedRows.some(row => row.status === 'pending')
        },
        canBatchDisable() {
            return this.selectedRows.some(row => ['approved', 'pending'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟供应商数据（敏感信息脱敏）
                const mockData = [
                    {
                        id: 1,
                        provider_code: 'PRV202401150001',
                        company_name: '深圳***科技有限公司', // 公司名脱敏
                        contact_person: '张经理',
                        contact_phone: '138****5678', // 手机号脱敏
                        contact_email: 'zha***@example.com', // 邮箱脱敏
                        type: 'manufacturer',
                        status: 'approved',
                        region: 'south',
                        address: '深圳市南山区***路***号', // 地址脱敏
                        business_license: '9144030***********', // 营业执照脱敏
                        created_at: '2024-01-15 10:30:00'
                    }
                ]

                this.dataList = mockData
                this.total = 345
                this.approvedCount = 267
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 45
            this.tabOptions[2].badge = 267
            this.tabOptions[3].badge = 23
            this.tabOptions[4].badge = 10
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                region: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('供应商数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchApprove() {
            const approvableItems = this.selectedRows.filter(row =>
                row.status === 'pending'
            )

            if (approvableItems.length === 0) {
                this.$message.warning('没有可审核的供应商')
                return
            }

            this.$confirm(`确定要审核通过选中的 ${approvableItems.length} 个供应商吗？`, '确认批量审核', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功审核 ${approvableItems.length} 个供应商`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchDisable() {
            const disableableItems = this.selectedRows.filter(row =>
                ['approved', 'pending'].includes(row.status)
            )

            if (disableableItems.length === 0) {
                this.$message.warning('没有可禁用的供应商')
                return
            }

            this.$confirm(`确定要禁用选中的 ${disableableItems.length} 个供应商吗？`, '确认批量禁用', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                this.$message.success(`成功禁用 ${disableableItems.length} 个供应商`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看供应商详情: ${row.company_name}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑供应商: ${row.company_name}`)
        },

        handleDelete(row) {
            this.$message.info(`删除供应商: ${row.company_name}`)
        },

        handleStatusChange(row, newStatus) {
            this.$message.info(`供应商状态已更改为: ${this.PROVIDER_STATUS_LABELS[newStatus]}`)
            this.loadData()
        }
    }
}
</script>
