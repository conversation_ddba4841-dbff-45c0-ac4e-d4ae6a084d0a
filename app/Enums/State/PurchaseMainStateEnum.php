<?php

namespace App\Enums\State;


use App\Enums\BaseEnum;

/**
 * Class PurchaseStateEnum
 * @package App\Enums\State\Purchase
 * @method static self default() 默认
 * @method static self WaitPurchase() 交易创建，等待商家
 * @method static self PurchaseInProgress() 采购中
 * @method static self Finished() 交易结束，不可采购
 * @method static self TimeoutClosed() 未采购交易超时关闭
 * @method static self Cancelled() 取消
 */
final class PurchaseMainStateEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'WaitPurchase' => 1,
            'PurchaseInProgress' => 50,
            'Finished' => 100,
            'TimeoutClosed' => 150,
            'Cancelled' => 200,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'WaitPurchase' => '交易创建，等待商家',
            'PurchaseInProgress' => '采购中',
            'Finished' => '交易结束，不可采购',
            'TimeoutClosed' => '未采购交易超时关闭',
            'Cancelled' => '取消',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
        ];
    }

}
