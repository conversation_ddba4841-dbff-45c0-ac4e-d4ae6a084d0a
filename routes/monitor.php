<?php

use App\Http\Middleware\RouteResponseTypeMiddleware;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 监控模块路由 - Monitor Module Routes
|--------------------------------------------------------------------------
|
| 这里定义了监控模块的所有路由，包括：
| - 商店监控
| - Pin 监控
| - Rice 监控
|
| 所有路由都需要认证和特定的响应类型处理
|
*/

// ============================================================================
// 监控模块路由组 - Monitor Module Routes Group
// ============================================================================

Route::middleware([RouteResponseTypeMiddleware::class, 'auth'])->prefix('monitor')->name('monitor.')->group(function () {
    //
    Route::resource('dashboard', App\Http\Controllers\Monitor\DashboardController::class);
    // ========================================================================
    // 商店监控 - Shop Monitoring
    // ========================================================================
    Route::resource('shop', App\Http\Controllers\Monitor\ShopController::class);

    // ========================================================================
    // Pin 监控 - Pin Monitoring
    // ========================================================================
    Route::resource('pin', App\Http\Controllers\Monitor\PinController::class);

    // ========================================================================
    // Rice 监控 - Rice Monitoring
    // ========================================================================
    Route::resource('rice', App\Http\Controllers\Monitor\RiceController::class);
});
