<?php

namespace App\Http\Resources\Advocate\Keyword;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KeywordDetailResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'word_no' => $this->word_no ?? null,
            'word' => $this->word ?? null,
            'rice_count' => isset($this->rices) ? $this->rices->count() : 0,
            'rices' => isset($this->rices) ? $this->rices()->paginate(20, ['uuid', 'rice_preview', 'rice_category_name', 'rice_number', 'rice_title', 'min_price', 'max_price']) : null,
        ]);
    }
}
