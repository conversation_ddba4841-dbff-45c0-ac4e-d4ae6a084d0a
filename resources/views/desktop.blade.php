@extends('layouts.app')
{{-- 页面特定脚本 --}}
@push('scripts')
    {{-- 管理后台Vue应用 --}}
    @vite(['resources/js/desktop/desktop.js'])
@endpush
{{-- 企业级网站首页 - 基于app.blade.php布局模板 --}}
@section('content')
    {{-- 设置页面变量 --}}
    @php
        $title = '首页';
        $description = '现代化的企业工作协作平台，提供高效的团队协作、项目管理、文档管理和业务流程优化解决方案，助力企业数字化转型';
        $keywords = '企业工作平台,团队协作,项目管理,文档管理,业务流程,数字化转型,企业办公,工作效率';
        $pageTitle = 'WorkHub - 现代化企业工作协作平台';
        $pageDescription = '专为现代企业打造的一体化工作协作平台';
    @endphp
    {{-- 企业级主容器 - 完整响应式布局系统 --}}
    <div id="app" v-cloak class="min-h-screen flex flex-col responsive-container enterprise-container">
        {{-- 企业级顶部导航栏 - 黑色主题 --}}
        @include('layouts.partials.top-navigation')
        {{-- 企业级主要内容区域 - 完整响应式容器系统 --}}
        <main class="flex-1 page-content responsive-main enterprise-main" id="main-content" role="main"
              aria-label="主要内容区域">
            {{-- 企业级页面内容 - 响应式间距和布局 --}}
            <div class="space-y-6 responsive-content enterprise-content">
                {{-- Vue管理后台应用挂载点 --}}
                <DesktopFacade data-route-active="{{ $menuActive ?? '' }}">
                    {{-- Vue Admin组件将在这里挂载 --}}
                </DesktopFacade>
            </div>
        </main>
        {{-- 企业级底部版权栏 - 与顶部导航栏风格一致 --}}
        @include('layouts.partials.bottom-copyright')
    </div>
@endsection
