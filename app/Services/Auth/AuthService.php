<?php

namespace App\Services\Auth;

use App\Models\UserCenter\User;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

/**
 * 认证服务类
 *
 * 提供用户认证相关的核心业务逻辑，包括：
 * - 用户登录验证
 * - 用户注册处理
 * - 会话管理
 * - 安全检查
 * - 登录历史记录
 *
 * @package App\Services\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class AuthService
{
    /**
     * 执行用户注册
     *
     * 处理用户注册的完整流程，包括数据验证、用户创建、自动登录
     *
     * @param array $userData 用户注册数据
     * @param Request $request HTTP请求对象
     * @return array 返回注册结果数组
     * @throws \Exception 当注册失败时抛出异常
     */
    public function register(array $userData, Request $request = null): array
    {
        // 获取请求对象
        $request = $request ?? request();

        try {
            // 准备用户数据
            $preparedData = $this->prepareUserData($userData);

            // 创建用户
            $user = User::create($preparedData);

            if (!$user) {
                throw new \Exception('用户创建失败');
            }

            // 触发注册事件
            event(new Registered($user));

            // 自动登录新用户
            Auth::login($user);

            // 重新生成会话ID
            $request->session()->regenerate();

            // 设置会话数据
            Session::put('registration_success', true);
            Session::put('registration_time', now());

            // 记录注册成功日志
            Log::info('用户注册成功', [
                'user_id' => $user->id,
                'email' => $user->email,
                'name' => $user->name,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'registration_time' => now()->toDateTimeString()
            ]);

            return [
                'success' => true,
                'user' => $user,
                'message' => '注册成功，欢迎加入我们的平台！'
            ];

        } catch (\Exception $e) {
            // 记录注册失败日志
            Log::error('用户注册失败', [
                'email' => $userData['email'] ?? 'unknown',
                'name' => $userData['name'] ?? 'unknown',
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
                'attempt_time' => now()->toDateTimeString()
            ]);

            throw $e;
        }
    }

    /**
     * 准备用户数据
     *
     * 处理和清理用户注册数据，确保数据格式正确
     *
     * @param array $userData 原始用户数据
     * @return array 返回处理后的用户数据
     */
    private function prepareUserData(array $userData): array
    {
        return [
            'name' => trim($userData['name']),
            'email' => strtolower(trim($userData['email'])),
            'password' => Hash::make($userData['password']),
            'email_verified_at' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * 执行用户登录
     *
     * 处理用户登录的完整流程，包括凭据验证、会话创建、日志记录
     *
     * @param array $credentials 用户凭据（邮箱和密码）
     * @param bool $remember 是否记住用户
     * @param Request $request HTTP请求对象
     * @return array 返回登录结果数组
     * @throws ValidationException 当登录失败时抛出异常
     */
    public function login(array $credentials, bool $remember = false, Request $request = null): array
    {
        // 获取请求对象
        $request = $request ?? request();

        // 尝试认证用户
        if (!Auth::attempt($credentials, $remember)) {
            // 记录登录失败日志
            Log::warning('用户登录失败', [
                'email' => $credentials['email'] ?? 'unknown',
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'attempt_time' => now()->toDateTimeString()
            ]);

            throw ValidationException::withMessages([
                'email' => '登录凭据不正确，请检查您的邮箱和密码。'
            ]);
        }

        // 获取认证用户
        $user = Auth::user();

        // 重新生成会话ID
        $request->session()->regenerate();

        // 设置会话数据
        Session::put('login_success', true);
        Session::put('last_login_time', now());
        Session::put('login_ip', $request->ip());

        // 触发登录事件
        event(new Login('web', $user, $remember));

        // 记录登录成功日志
        Log::info('用户登录成功', [
            'user_id' => $user->id,
            'email' => $user->email ?? $user->mobile ?? 'unknown',
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'remember' => $remember,
            'login_time' => now()->toDateTimeString()
        ]);

        return [
            'success' => true,
            'user' => $user,
            'message' => '登录成功，欢迎回来！'
        ];
    }

    /**
     * 执行用户登出
     *
     * 处理用户登出的完整流程，包括会话清理、日志记录
     *
     * @param Request $request HTTP请求对象
     * @return array 返回登出结果数组
     */
    public function logout(Request $request = null): array
    {
        // 获取请求对象
        $request = $request ?? request();

        // 获取当前用户信息
        $user = Auth::user();
        $userId = $user ? $user->id : null;
        $userEmail = $user ? ($user->email ?? $user->mobile ?? 'unknown') : 'unknown';

        try {
            // 触发登出事件
            if ($user) {
                event(new Logout('web', $user));
            }

            // 登出用户
            Auth::guard('web')->logout();

            // 清理会话
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            Session::flush();

            // 记录登出成功日志
            Log::info('用户登出成功', [
                'user_id' => $userId,
                'email' => $userEmail,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'logout_time' => now()->toDateTimeString()
            ]);

            return [
                'success' => true,
                'message' => '您已安全登出，感谢使用！'
            ];

        } catch (\Exception $e) {
            // 记录登出失败日志
            Log::error('用户登出失败', [
                'user_id' => $userId,
                'email' => $userEmail,
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
                'logout_attempt_time' => now()->toDateTimeString()
            ]);

            // 即使出错也要尝试清理会话
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return [
                'success' => false,
                'message' => '登出过程中发生错误，但您已被安全登出。'
            ];
        }
    }

    /**
     * 检查用户是否已认证
     *
     * @return bool 返回用户认证状态
     */
    public function isAuthenticated(): bool
    {
        return Auth::check();
    }

    /**
     * 获取当前认证用户
     *
     * @return User|null 返回当前用户或null
     */
    public function getCurrentUser(): ?User
    {
        return Auth::user();
    }

    /**
     * 检查用户密码是否正确
     *
     * @param User $user 用户模型
     * @param string $password 明文密码
     * @return bool 返回密码验证结果
     */
    public function checkPassword(User $user, string $password): bool
    {
        return Hash::check($password, $user->password);
    }

    /**
     * 更新用户密码
     *
     * @param User $user 用户模型
     * @param string $newPassword 新密码
     * @return bool 返回更新结果
     */
    public function updatePassword(User $user, string $newPassword): bool
    {
        try {
            $user->update([
                'password' => Hash::make($newPassword),
                'updated_at' => now()
            ]);

            Log::info('用户密码更新成功', [
                'user_id' => $user->id,
                'email' => $user->email,
                'update_time' => now()->toDateTimeString()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('用户密码更新失败', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
