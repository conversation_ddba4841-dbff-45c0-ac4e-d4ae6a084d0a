<?php

namespace App\Http\Resources\Advocate\Rice;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RiceSeedToSpareLineResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        if ($this->seed_name === 'p-404' || $this->seed_name === 'p-3216') {
            //
            $seed_text = '【' . $this->seed_text . '】';
            $attr_type = 'spec';
            $seed_name = 'p-404';
        }
        //
        if ($this->seed_name === 'p-100018377' || $this->seed_name === 'p-3151') {
            //
            $seed_text = '【' . $this->seed_text . '】';
            $attr_type = 'suit';
            $seed_name = 'p-100018377';
        }
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'category_no' => $this->category_no ?? null,
            //
            'attr_type' => $attr_type ?? null,
            //
            'seed_name' => $seed_name ?? null,
            'seed_value' => $this->seed_value ?? null,
            'seed_text' => $seed_text ?? null,
            'seed_custom' => $this->seed_custom ?? null,
            'seed_image_url' => $this->seed_image_url ?? null,
        ]);
    }
}
