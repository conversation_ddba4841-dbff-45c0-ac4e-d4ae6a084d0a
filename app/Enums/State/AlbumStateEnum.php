<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * 相册创建状态枚举
 * @package App\Enums\State\AlbumStateEnum
 * @method static self pending()    // 相册创建尚未开始
 * @method static self progress()   // 相册创建进行中
 * @method static self completed()  // 相册创建已完成
 * @method static self failed()     // 相册创建失败
 * @method static self cancelled()  // 相册创建已取消
 * @method static self archived()   // 相册已归档
 */
final class AlbumStateEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    /**
     * 定义相册创建状态的值
     */
    protected static function values(): array
    {
        //
        return [
            'pending' => 0,  // 相册创建尚未开始
            'progress' => 1,  // 相册创建进行中
            'completed' => 2,  // 相册创建已完成
            'failed' => 3,  // 相册创建失败
            'cancelled' => 4,  // 相册创建已取消
            'archived' => 5,  // 相册已归档
        ];
    }

    /**
     * 定义每个相册创建状态的中文标签
     */
    protected static function labels(): array
    {
        //
        return [
            'pending' => '相册创建尚未开始', // 相册创建尚未开始
            'progress' => '相册创建进行中',  // 相册创建进行中
            'completed' => '相册创建已完成',  // 相册创建已完成
            'failed' => '相册创建失败',    // 相册创建失败
            'cancelled' => '相册创建已取消',  // 相册创建已取消
            'archived' => '相册已归档',      // 相册已归档
        ];
    }
}
