<?php

namespace App\Helpers\Auth;

use App\Models\UserCenter\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

/**
 * 认证助手类
 *
 * 提供认证相关的常用功能和工具方法，包括：
 * - 用户状态检查
 * - 密码验证和生成
 * - 会话管理
 * - 安全检查
 * - 用户信息获取
 *
 * @package App\Helpers\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class AuthHelper
{
    /**
     * 检查当前用户是否为访客
     *
     * @return bool 如果用户为访客返回true
     */
    public static function isGuest(): bool
    {
        return Auth::guest();
    }

    /**
     * 获取当前用户ID
     *
     * @return int|null 返回当前用户ID或null
     */
    public static function getCurrentUserId(): ?int
    {
        return Auth::id();
    }

    /**
     * 获取当前用户名称
     *
     * @return string|null 返回当前用户名称或null
     */
    public static function getCurrentUserName(): ?string
    {
        $user = self::getCurrentUser();
        return $user ? ($user->name ?? $user->user_name ?? null) : null;
    }

    /**
     * 获取当前认证用户
     *
     * @return User|null 返回当前用户或null
     */
    public static function getCurrentUser(): ?User
    {
        return Auth::user();
    }

    /**
     * 检查用户是否具有指定角色
     *
     * @param string|array $roles 角色名称或角色数组
     * @return bool 如果用户具有指定角色返回true
     */
    public static function hasRole($roles): bool
    {
        $user = self::getCurrentUser();

        if (!$user || !method_exists($user, 'hasRole')) {
            return false;
        }

        return $user->hasRole($roles);
    }

    /**
     * 检查用户是否具有指定权限
     *
     * @param string|array $permissions 权限名称或权限数组
     * @return bool 如果用户具有指定权限返回true
     */
    public static function hasPermission($permissions): bool
    {
        $user = self::getCurrentUser();

        if (!$user || !method_exists($user, 'hasPermission')) {
            return false;
        }

        return $user->hasPermission($permissions);
    }

    /**
     * 验证密码强度
     *
     * 检查密码是否符合安全要求
     *
     * @param string $password 待验证的密码
     * @return array 返回验证结果数组
     */
    public static function validatePasswordStrength(string $password): array
    {
        $result = [
            'valid' => true,
            'score' => 0,
            'messages' => []
        ];

        // 检查密码长度
        if (strlen($password) < 8) {
            $result['valid'] = false;
            $result['messages'][] = '密码长度至少需要8个字符';
        } else {
            $result['score'] += 1;
        }

        // 检查是否包含小写字母
        if (!preg_match('/[a-z]/', $password)) {
            $result['messages'][] = '密码应包含小写字母';
        } else {
            $result['score'] += 1;
        }

        // 检查是否包含大写字母
        if (!preg_match('/[A-Z]/', $password)) {
            $result['messages'][] = '密码应包含大写字母';
        } else {
            $result['score'] += 1;
        }

        // 检查是否包含数字
        if (!preg_match('/[0-9]/', $password)) {
            $result['messages'][] = '密码应包含数字';
        } else {
            $result['score'] += 1;
        }

        // 检查是否包含特殊字符
        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            $result['messages'][] = '密码应包含特殊字符';
        } else {
            $result['score'] += 1;
        }

        // 检查是否包含常见弱密码
        $weakPasswords = ['password', '123456', 'qwerty', 'abc123', 'password123'];
        if (in_array(strtolower($password), $weakPasswords)) {
            $result['valid'] = false;
            $result['messages'][] = '密码过于简单，请使用更复杂的密码';
        }

        return $result;
    }

    /**
     * 生成安全的随机密码
     *
     * @param int $length 密码长度，默认12位
     * @param bool $includeSymbols 是否包含特殊符号
     * @return string 返回生成的密码
     */
    public static function generateSecurePassword(int $length = 12, bool $includeSymbols = true): string
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        $characters = $lowercase . $uppercase . $numbers;

        if ($includeSymbols) {
            $characters .= $symbols;
        }

        $password = '';

        // 确保密码包含各种类型的字符
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];

        if ($includeSymbols) {
            $password .= $symbols[random_int(0, strlen($symbols) - 1)];
        }

        // 填充剩余长度
        for ($i = strlen($password); $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        // 打乱密码字符顺序
        return str_shuffle($password);
    }

    /**
     * 检查密码是否正确
     *
     * @param string $password 明文密码
     * @param string $hashedPassword 哈希密码
     * @return bool 如果密码正确返回true
     */
    public static function checkPassword(string $password, string $hashedPassword): bool
    {
        return Hash::check($password, $hashedPassword);
    }

    /**
     * 哈希密码
     *
     * @param string $password 明文密码
     * @return string 返回哈希后的密码
     */
    public static function hashPassword(string $password): string
    {
        return Hash::make($password);
    }

    /**
     * 获取用户登录状态信息
     *
     * @return array 返回登录状态信息数组
     */
    public static function getLoginStatus(): array
    {
        if (!self::isAuthenticated()) {
            return [
                'authenticated' => false,
                'user' => null,
                'login_time' => null,
                'last_activity' => null
            ];
        }

        return [
            'authenticated' => true,
            'user' => self::getCurrentUser(),
            'login_time' => Session::get('login_time'),
            'last_activity' => Session::get('last_activity_time'),
            'login_ip' => Session::get('login_ip'),
            'session_id' => Session::getId()
        ];
    }

    /**
     * 检查当前用户是否已认证
     *
     * @return bool 如果用户已认证返回true
     */
    public static function isAuthenticated(): bool
    {
        return Auth::check();
    }

    /**
     * 检查会话是否即将过期
     *
     * @param int $warningMinutes 警告时间（分钟）
     * @return bool 如果会话即将过期返回true
     */
    public static function isSessionNearExpiry(int $warningMinutes = 10): bool
    {
        $lastActivity = Session::get('last_activity_time');

        if (!$lastActivity) {
            return false;
        }

        $sessionLifetime = config('session.lifetime', 120); // 默认120分钟
        $warningTime = $sessionLifetime - $warningMinutes;

        $minutesSinceLastActivity = now()->diffInMinutes($lastActivity);

        return $minutesSinceLastActivity >= $warningTime;
    }

    /**
     * 获取会话剩余时间（分钟）
     *
     * @return int 返回会话剩余时间
     */
    public static function getSessionRemainingMinutes(): int
    {
        $lastActivity = Session::get('last_activity_time');

        if (!$lastActivity) {
            return 0;
        }

        $sessionLifetime = config('session.lifetime', 120);
        $minutesSinceLastActivity = now()->diffInMinutes($lastActivity);

        return max(0, $sessionLifetime - $minutesSinceLastActivity);
    }

    /**
     * 生成安全的记住我令牌
     *
     * @return string 返回记住我令牌
     */
    public static function generateRememberToken(): string
    {
        return Str::random(60);
    }

    /**
     * 清理用户会话数据
     *
     * 清理指定用户的所有会话相关数据
     */
    public static function clearUserSession(): void
    {
        Session::forget([
            'login_success',
            'login_time',
            'login_ip',
            'login_user_agent',
            'last_activity_time',
            'failed_login_attempts',
            'last_failed_attempt',
            'registration_success',
            'registration_time',
            'email_verified',
            'email_verified_time'
        ]);
    }

    /**
     * 检查IP地址是否可信
     *
     * @param string $ip IP地址
     * @return bool 如果IP地址可信返回true
     */
    public static function isTrustedIp(string $ip): bool
    {
        // 获取可信IP列表（可以从配置文件读取）
        $trustedIps = config('auth.trusted_ips', []);

        return in_array($ip, $trustedIps);
    }

    /**
     * 记录用户活动
     *
     * @param string $activity 活动描述
     * @param array $data 额外数据
     */
    public static function logUserActivity(string $activity, array $data = []): void
    {
        $user = self::getCurrentUser();

        if (!$user) {
            return;
        }

        $logData = array_merge([
            'user_id' => $user->id,
            'email' => self::getCurrentUserEmail(),
            'activity' => $activity,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toDateTimeString()
        ], $data);

        \Log::info('用户活动记录', $logData);
    }

    /**
     * 获取当前用户邮箱
     *
     * @return string|null 返回当前用户邮箱或null
     */
    public static function getCurrentUserEmail(): ?string
    {
        $user = self::getCurrentUser();
        return $user ? ($user->email ?? $user->mobile ?? null) : null;
    }
}
