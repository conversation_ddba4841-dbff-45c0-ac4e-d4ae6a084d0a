<?php

namespace App\Services\Process\RiceCorn;

use App\Models\RiceCenter\Rice;
use App\Models\RiceCenter\RiceSeed;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RiceCornMatchProduceSpecService extends BaseService
{
    /**
     * 处理单条 RiceSeed 规格逻辑
     * 只处理 seed_name 为 'p-404' 或 'p-3216' 的数据
     * @param RiceSeed $riceSeed 需要处理的RiceSeed实例
     * @param Rice $rice 与该RiceSeed关联的Rice主表实例
     */
    public static function processRiceSeedMatchProduceSpec(RiceSeed $riceSeed, Rice $rice): void
    {
        // 只处理seed_name为p-404或p-3216的RiceSeed，其他类型直接跳过
        if (!in_array($riceSeed->seed_name, ['p-404', 'p-3216'])) {
            // 可选：这里可以记录日志，或直接return
            return;
        }
        //
        // 5. 每条RiceSeed处理都包裹在数据库事务中，保证原子性
        DB::beginTransaction();
        //
        try {
            // 1. 更新RiceSeed，将其属性类型标记为'spec'，且标记为已校验
            $riceSeed->update([
                'attr_type' => 'spec',
                'is_check' => 1,
            ]);
            //
            // 2. 校验 Rice 是否存在（一般已在外部判断）
            if ($rice) {
                //
                // 3. 检查该Rice下是否所有RiceSeed的is_check字段都为1
                $allChecked = RiceSeed::where('rice_id', $rice->id)->where('is_check', '!=', 1)->count() === 0;
                //
                // 4. 若全部校验通过，则将Rice主表的is_check字段也标记为1
                if ($allChecked) {
                    //
                    $rice->update(['is_check' => 1]);
                }
                //
                // 5. 如果RiceSeed的seed_image_url为空，尝试自动补齐
                if (empty($riceSeed->seed_image_url)) {
                    // rice_images 可能为数组或 JSON 字符串，需确保格式正确
                    $images = is_array($rice->rice_images) ? $rice->rice_images : (is_string($rice->rice_images) ? json_decode($rice->rice_images, true) : []);
                    //
                    // 取第5张图片（数组下标为4），如果存在
                    $seedImage = isset($images[4]) ? $images[4] : null;
                    //
                    // 若存在第5张图片则更新RiceSeed的seed_image_url字段
                    if ($seedImage) {
                        //
                        $riceSeed->update(['seed_image_url' => $seedImage]);
                    }
                }
            }
            //
            // 5.6 只要本条数据所有步骤无异常，则提交事务
            DB::commit();
        } catch (Exception $e) {
            // 5.7 任意异常立即回滚本条事务，并计入失败
            DB::rollBack();
            //
            Log::error("RiceSeed规格处理失败，ID: {$riceSeed->id}，原因: {$e->getMessage()}");
        }
    }
}
