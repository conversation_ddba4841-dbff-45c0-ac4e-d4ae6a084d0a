import {Chart, registerables} from 'chart.js'

// 注册所有Chart.js组件
Chart.register(...registerables)

// 设置全局默认配置以避免clipArea错误
Chart.defaults.animation = false
Chart.defaults.plugins.filler = false
Chart.defaults.elements.line.fill = false
Chart.defaults.datasets.line.fill = false
Chart.defaults.clip = false

// 创建最简化的图表配置
export const createSafeChartConfig = (type, data, customOptions = {}) => {
    // 确保数据结构正确
    const safeData = {
        labels: data.labels || [],
        datasets: (data.datasets || []).map(dataset => ({
            label: dataset.label || '数据',
            data: dataset.data || [],
            borderColor: dataset.borderColor || '$primary-color',
            backgroundColor: dataset.backgroundColor || '$primary-color',
            borderWidth: dataset.borderWidth || 2,
            // 完全禁用fill相关功能
            fill: false,
            tension: dataset.tension || 0,
            ...dataset
        }))
    }

    // 最简化的配置，禁用所有可能导致问题的特性
    const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        animation: false, // 完全禁用动画
        plugins: {
            legend: {
                display: customOptions.showLegend !== false,
                position: 'top'
            },
            tooltip: {
                enabled: true
            },
            // 禁用filler插件
            filler: false
        },
        scales: {},
        elements: {},
        // 禁用交互功能以减少错误
        interaction: {
            intersect: false
        },
        // 禁用数据集绘制期间的clipping
        clip: false
    }

    // 根据图表类型设置scales
    if (type === 'line' || type === 'bar') {
        baseOptions.scales = {
            y: {
                beginAtZero: true
            },
            x: {}
        }
    }

    // 特定图表类型配置
    if (type === 'doughnut') {
        baseOptions.cutout = '70%'
        baseOptions.scales = {}
    }

    if (type === 'pie') {
        baseOptions.scales = {}
    }

    // 简单合并自定义选项
    const finalOptions = {
        ...baseOptions,
        ...customOptions
    }

    return {
        type,
        data: safeData,
        options: finalOptions
    }
}

// 创建安全的图表实例
export const createSafeChart = (canvas, config) => {
    return new Promise((resolve, reject) => {
        try {
            // 严格验证 Canvas 元素
            if (!canvas) {
                reject(new Error('Canvas元素不存在'))
                return
            }

            if (!(canvas instanceof HTMLCanvasElement)) {
                reject(new Error('提供的元素不是Canvas'))
                return
            }

            // 验证Canvas尺寸
            if (canvas.width === 0 || canvas.height === 0) {
                // 设置默认尺寸
                canvas.width = canvas.offsetWidth || 400
                canvas.height = canvas.offsetHeight || 300
            }

            // 获取并验证2D上下文
            const ctx = canvas.getContext('2d')
            if (!ctx) {
                reject(new Error('无法获取Canvas 2D上下文'))
                return
            }

            // 验证上下文方法
            if (typeof ctx.save !== 'function' || typeof ctx.restore !== 'function') {
                reject(new Error('Canvas上下文缺少必要方法'))
                return
            }

            // 清理可能存在的旧图表
            const existingChart = Chart.getChart(canvas)
            if (existingChart) {
                try {
                    existingChart.destroy()
                } catch (destroyError) {
                    console.warn('销毁旧图表失败:', destroyError)
                }
            }

            // 增加延迟确保DOM完全准备好
            setTimeout(() => {
                try {
                    // 再次验证上下文
                    if (!ctx || typeof ctx.save !== 'function') {
                        reject(new Error('Canvas上下文在延迟后变为无效'))
                        return
                    }

                    const chart = new Chart(ctx, config)

                    // 验证图表实例
                    if (!chart || typeof chart.destroy !== 'function') {
                        reject(new Error('创建的图表实例无效'))
                        return
                    }

                    resolve(chart)
                } catch (error) {
                    console.error('创建Chart实例失败:', error)
                    reject(error)
                }
            }, 200) // 增加延迟时间

        } catch (error) {
            console.error('创建图表预处理失败:', error)
            reject(error)
        }
    })
}

// 安全更新图表数据
export const updateChartData = (chart, newData, animationMode = 'none') => {
    try {
        if (!chart || !chart.data) {
            console.warn('图表实例无效或数据结构错误')
            return false
        }

        // 更新数据
        if (newData.labels) {
            chart.data.labels = newData.labels
        }

        if (newData.datasets && Array.isArray(newData.datasets)) {
            newData.datasets.forEach((dataset, index) => {
                if (chart.data.datasets[index]) {
                    Object.assign(chart.data.datasets[index], dataset)
                }
            })
        }

        // 安全更新
        chart.update(animationMode)
        return true
    } catch (error) {
        console.error('更新图表数据失败:', error)
        return false
    }
}

// 安全销毁图表
export const destroyChart = (chart) => {
    try {
        if (chart && typeof chart.destroy === 'function') {
            chart.destroy()
            return true
        }
        return false
    } catch (error) {
        console.error('销毁图表失败:', error)
        return false
    }
}

export {Chart}
export default Chart 