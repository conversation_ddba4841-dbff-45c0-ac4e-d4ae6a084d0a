<?php

namespace App\Http\Resources\Admin\Shipment;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ShipmentPickupResource extends BaseResource
{


    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return [
            'uuid' => $this->uuid ?? null,
            //
            'pickup_name' => $this->pickup_name ?? null,
            'pickup_images_url' => $this->pickup_images_url ?? null,
            'pickup_type' => $this->pickup_type ?? null,
            'shipment_address_uuid' => $this->shipment_address->uuid ?? null,
            'pickup_address' => $this->pickup_address ?? null,
            'pickup_address_detail' => $this->pickup_address_detail ?? null,
            'start_time' => $this->start_time ?? null,
            'end_time' => $this->end_time ?? null,
            'is_book_time' => $this->is_book_time ?? null,
            'book_time_count' => $this->book_time_count ?? null,
            'is_same' => $this->is_same ?? null,
            'remarks' => $this->remarks ?? null,
        ];
    }
}
