<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class ProductWeightTypeEnum
 * @package App\Enums\Type\Product
 * @method static self NonWeight() 非称重品
 * @method static self Weight() 称重品
 * @method static self Countable() 计数品
 */
final class WeightTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'NonWeight' => 1,
            'Weight' => 2,
            'Countable' => 3,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'NonWeight' => '非称重品',
            'Weight' => '称重品',
            'Countable' => '计数品',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        //
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
        ];
    }
}
