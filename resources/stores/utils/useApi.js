import {defineStore} from 'pinia'
import {useAuthStore} from '../user/useAuthStore'

/**
 * API请求封装与管理
 * 用于封装与后端接口的交互，简化API请求和错误处理逻辑
 */
export const useApi = defineStore('api', {
    state: () => ({
        // API配置
        config: {
            baseURL: import.meta.env.VITE_APP_API_BASE_URL || '/api',
            timeout: 10000,
            retryAttempts: 3,
            retryDelay: 1000
        },

        // 请求状态
        loading: false,
        loadingRequests: new Set(),

        // 请求缓存
        cache: new Map(),
        cacheConfig: {
            enabled: true,
            maxAge: 5 * 60 * 1000, // 5分钟
            maxSize: 100
        },

        // 错误信息
        errors: [],

        // 请求拦截器
        interceptors: {
            request: [],
            response: []
        }
    }),

    getters: {
        // 检查是否有加载中的请求
        isLoading: (state) => state.loading || state.loadingRequests.size > 0,

        // 获取特定请求的加载状态
        isRequestLoading: (state) => (requestKey) => {
            return state.loadingRequests.has(requestKey)
        },

        // 获取最新错误
        getLatestError: (state) => {
            return state.errors.length > 0 ? state.errors[state.errors.length - 1] : null
        },

        // 获取错误数量
        getErrorCount: (state) => state.errors.length
    },

    actions: {
        /**
         * 通用请求方法
         */
        async request(options) {
            const {
                url,
                method = 'GET',
                data = null,
                params = {},
                headers = {},
                useAuth = true,
                useCache = true,
                cacheKey = null,
                timeout = this.config.timeout,
                retries = this.config.retryAttempts
            } = options

            // 生成请求键
            const requestKey = this.generateRequestKey(method, url, params, data)

            // 检查缓存
            if (useCache && method === 'GET') {
                const cached = this.getFromCache(cacheKey || requestKey)
                if (cached) {
                    return cached
                }
            }

            // 开始加载
            this.setLoading(requestKey, true)

            try {
                // 构建完整URL
                const fullUrl = this.buildUrl(url, params)

                // 构建请求头
                const requestHeaders = this.buildHeaders(headers, useAuth)

                // 应用请求拦截器
                const interceptedRequest = await this.applyRequestInterceptors({
                    url: fullUrl,
                    method,
                    headers: requestHeaders,
                    data
                })

                // 发送请求
                const response = await this.sendRequest(interceptedRequest, timeout)

                // 应用响应拦截器
                const interceptedResponse = await this.applyResponseInterceptors(response)

                // 缓存响应
                if (useCache && method === 'GET') {
                    this.setCache(cacheKey || requestKey, interceptedResponse.data)
                }

                return interceptedResponse.data
            } catch (error) {
                // 重试逻辑
                if (retries > 0 && this.shouldRetry(error)) {
                    await this.delay(this.config.retryDelay)
                    return this.request({...options, retries: retries - 1})
                }

                // 记录错误
                this.addError(error, requestKey)
                throw error
            } finally {
                this.setLoading(requestKey, false)
            }
        },

        /**
         * GET请求
         */
        async get(url, params = {}, options = {}) {
            return this.request({
                url,
                method: 'GET',
                params,
                ...options
            })
        },

        /**
         * POST请求
         */
        async post(url, data = {}, options = {}) {
            return this.request({
                url,
                method: 'POST',
                data,
                ...options
            })
        },

        /**
         * PUT请求
         */
        async put(url, data = {}, options = {}) {
            return this.request({
                url,
                method: 'PUT',
                data,
                ...options
            })
        },

        /**
         * DELETE请求
         */
        async delete(url, options = {}) {
            return this.request({
                url,
                method: 'DELETE',
                ...options
            })
        },

        /**
         * PATCH请求
         */
        async patch(url, data = {}, options = {}) {
            return this.request({
                url,
                method: 'PATCH',
                data,
                ...options
            })
        },

        /**
         * 文件上传
         */
        async upload(url, file, options = {}) {
            const formData = new FormData()
            formData.append('file', file)

            if (options.data) {
                Object.keys(options.data).forEach(key => {
                    formData.append(key, options.data[key])
                })
            }

            return this.request({
                url,
                method: 'POST',
                data: formData,
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                useCache: false,
                ...options
            })
        },

        /**
         * 批量请求
         */
        async batch(requests) {
            const promises = requests.map(request => this.request(request))
            return Promise.allSettled(promises)
        },

        /**
         * 发送实际HTTP请求
         */
        async sendRequest(request, timeout) {
            const controller = new AbortController()
            const timeoutId = setTimeout(() => controller.abort(), timeout)

            try {
                const response = await fetch(request.url, {
                    method: request.method,
                    headers: request.headers,
                    body: request.method !== 'GET' ? JSON.stringify(request.data) : undefined,
                    signal: controller.signal
                })

                clearTimeout(timeoutId)

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }

                const data = await response.json()
                return {data, status: response.status, headers: response.headers}
            } catch (error) {
                clearTimeout(timeoutId)
                throw error
            }
        },

        /**
         * 构建完整URL
         */
        buildUrl(url, params) {
            const baseUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`

            if (Object.keys(params).length === 0) {
                return baseUrl
            }

            const urlObj = new URL(baseUrl, window.location.origin)
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== undefined) {
                    urlObj.searchParams.append(key, params[key])
                }
            })

            return urlObj.toString()
        },

        /**
         * 构建请求头
         */
        buildHeaders(customHeaders, useAuth) {
            const headers = {
                'Content-Type': 'application/json',
                ...customHeaders
            }

            if (useAuth) {
                const authStore = useAuthStore()
                const authHeader = authStore.getAuthHeader
                if (authHeader) {
                    headers.Authorization = authHeader
                }
            }

            return headers
        },

        /**
         * 应用请求拦截器
         */
        async applyRequestInterceptors(request) {
            let modifiedRequest = {...request}

            for (const interceptor of this.interceptors.request) {
                modifiedRequest = await interceptor(modifiedRequest)
            }

            return modifiedRequest
        },

        /**
         * 应用响应拦截器
         */
        async applyResponseInterceptors(response) {
            let modifiedResponse = {...response}

            for (const interceptor of this.interceptors.response) {
                modifiedResponse = await interceptor(modifiedResponse)
            }

            return modifiedResponse
        },

        /**
         * 添加请求拦截器
         */
        addRequestInterceptor(interceptor) {
            this.interceptors.request.push(interceptor)
        },

        /**
         * 添加响应拦截器
         */
        addResponseInterceptor(interceptor) {
            this.interceptors.response.push(interceptor)
        },

        /**
         * 生成请求键
         */
        generateRequestKey(method, url, params, data) {
            const key = `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
            return btoa(key).replace(/[^a-zA-Z0-9]/g, '')
        },

        /**
         * 设置加载状态
         */
        setLoading(requestKey, loading) {
            if (loading) {
                this.loadingRequests.add(requestKey)
            } else {
                this.loadingRequests.delete(requestKey)
            }
            this.loading = this.loadingRequests.size > 0
        },

        /**
         * 缓存相关方法
         */
        getFromCache(key) {
            if (!this.cacheConfig.enabled) return null

            const cached = this.cache.get(key)
            if (!cached) return null

            const now = Date.now()
            if (now - cached.timestamp > this.cacheConfig.maxAge) {
                this.cache.delete(key)
                return null
            }

            return cached.data
        },

        setCache(key, data) {
            if (!this.cacheConfig.enabled) return

            // 检查缓存大小限制
            if (this.cache.size >= this.cacheConfig.maxSize) {
                const firstKey = this.cache.keys().next().value
                this.cache.delete(firstKey)
            }

            this.cache.set(key, {
                data,
                timestamp: Date.now()
            })
        },

        clearCache() {
            this.cache.clear()
        },

        /**
         * 错误处理
         */
        addError(error, requestKey) {
            const errorInfo = {
                id: Date.now(),
                message: error.message,
                requestKey,
                timestamp: new Date().toISOString(),
                stack: error.stack
            }

            this.errors.push(errorInfo)

            // 保持错误列表在合理范围内
            if (this.errors.length > 50) {
                this.errors.shift()
            }
        },

        clearErrors() {
            this.errors = []
        },

        /**
         * 判断是否应该重试
         */
        shouldRetry(error) {
            // 网络错误或5xx服务器错误才重试
            return error.name === 'TypeError' ||
                (error.message.includes('HTTP 5'))
        },

        /**
         * 延迟函数
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms))
        },

        /**
         * 更新配置
         */
        updateConfig(config) {
            this.config = {...this.config, ...config}
        },

        /**
         * 取消所有请求
         */
        cancelAllRequests() {
            this.loadingRequests.clear()
            this.loading = false
        }
    }
})
