<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * @method static self default()
 * @method static self published()
 * @method static self online()
 * @method static self offline()
 * @method static self unlisted()
 * @method static self out()
 * @method static self controlled()
 * @method static self member_expired()
 * @method static self auto_expired()
 * @method static self expired()
 * @method static self member_deleted()
 * @method static self modified()
 * @method static self new()
 * @method static self deleted()
 * @method static self tbd()
 * @method static self approved()
 * @method static self auditing()
 * @method static self untread()
 */
final class OfferStateEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    protected static function values(): array
    {
        //
        return [
            'default' => 0,  // 默认状态
            'published' => 1,  // 销售中
            'online' => 2,  // 已上架
            'offline' => 5,  // 待上架
            'unlisted' => 7,  // 已下架
            'out' => 9,  // 售罄
            'controlled' => 11,  // 管制中
            'member_expired' => 20,  // 会员撤销
            'auto_expired' => 30,  // 自然过期
            'expired' => 40,  // 过期(包含手动过期与自动过期)
            'member_deleted' => 50,  // 会员删除
            'modified' => 60,  // 修改
            'new' => 70,  // 新发
            'deleted' => 80,  // 删除
            'tbd' => 90,  // 待删除 (to be delete)
            'approved' => 100, // 审批通过
            'auditing' => 110, // 审核中
            'untread' => 120, // 审核不通过
        ];
    }

    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
            'published' => '销售中',
            'online' => '已上架',
            'offline' => '待上架',
            'unlisted' => '已下架',
            'out' => '售罄',
            'controlled' => '管制中',
            'member_expired' => '会员撤销',
            'auto_expired' => '自然过期',
            'expired' => '过期(包含手动过期与自动过期)',
            'member_deleted' => '会员删除',
            'modified' => '修改',
            'new' => '新发',
            'deleted' => '删除',
            'tbd' => '待删除',
            'approved' => '审批通过',
            'auditing' => '审核中',
            'untread' => '审核不通过',
        ];
    }
}
