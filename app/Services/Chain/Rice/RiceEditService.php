<?php

namespace App\Services\Chain\Rice;

use App\Models\RiceCenter\Rice;
use App\Models\RiceCenter\RiceCorn;
use App\Models\RiceCenter\RiceSeed;
use App\Services\Admin\Keyword\KeywordService;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\DB;

class RiceEditService extends BaseService
{
    /**
     * 批量编辑稻米记录
     * @param array $rices 稻米数据
     * @return array 返回更新后的稻米对象数组
     * @throws Exception 如果发生异常，抛出
     */
    public static function batchEditRice(array $rices)
    {
        //
        try {
            // 开始数据库事务
            DB::beginTransaction();
            //
            $riceLists = [];
            // 遍历每个稻米记录
            foreach ($rices as $riceData) {
                //
                // 更新 Rice 记录
                $Rice = Rice::where('platform_id', config('app.platform_id'))->whereUuid($riceData['rice_uuid'])->first();
                $Rice->rice_title = $riceData['rice_title'] ?? $Rice->rice_title;
                $Rice->rice_images = $riceData['rice_images'] ?? $Rice->rice_images;
                $Rice->rice_descries = $riceData['rice_descries'] ?? $Rice->rice_descries;
                $Rice->save();
                // 更新 RiceCorn 记录
                $rice_corns = $riceData['rice_corns'];
                foreach ($rice_corns as $rice_cornData) {
                    //
                    $RiceCorn = RiceCorn::where('platform_id', config('app.platform_id'))->whereUuid($rice_cornData['corn_uuid'])->first();
                    $RiceCorn->corn_sn = $rice_cornData['corn_sn'] ?? $RiceCorn->corn_sn;
                    $RiceCorn->corn_stock = $rice_cornData['corn_stock'] ?? $RiceCorn->corn_stock;
                    $RiceCorn->corn_price = $rice_cornData['corn_price'] ?? $RiceCorn->corn_price;
                    $RiceCorn->save();
                    // 更新 RiceSeed 记录
                    foreach ($rice_cornData['rice_seeds'] as $rice_seedData) {
                        //
                        $seed_text = $rice_seedData['attr_type'] == 'suit' ? $rice_cornData['suit_attr'] : ($rice_seedData['attr_type'] == 'spec' ? $rice_cornData['spec_attr'] : null);
                        $RiceSeed = RiceSeed::where('platform_id', config('app.platform_id'))->whereUuid($rice_seedData['seed_uuid'])->first();
                        $RiceSeed->seed_text = $seed_text;
                        $RiceSeed->save();
                    }
                }
                // 同步关键词
                $keyword_ids = KeywordService::SyncKeywordDataToIds($riceData['keywords']);
                $Rice->keywords()->sync($keyword_ids);
                //
                $riceLists[] = $Rice;
            }
            // 提交事务
            DB::commit();
            //
            return $riceLists;
        } catch (Exception $e) {
            // 回滚事务，记录异常日志并抛出
            DB::rollBack();
            // 如果发生异常，直接抛出
            throw new Exception('批量操作失败: ' . $e->getMessage());
        }
    }
}
