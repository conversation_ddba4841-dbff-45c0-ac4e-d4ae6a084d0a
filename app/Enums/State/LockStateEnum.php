<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * LockStateEnum 用于管理锁定状态，例如用户账户或订单的锁定状态。
 * @package App\Enums\Type\User
 * @method static self default() 默认状态
 * @method static self locked() 锁定状态
 * @method static self unlocked() 解锁状态
 * @method static self pending() 待解锁状态
 */
final class LockStateEnum extends BaseEnum
{
    /**
     * 定义锁定状态的值
     * 返回一个数组，其中键是状态名称，值是该状态对应的数字。
     * 数字值可以用于存储和数据库操作。
     * @return array 状态值数组
     */
    protected static function values(): array
    {
        return [
            'default' => 0,  // 默认状态
            'locked' => 1,  // 锁定状态
            'unlocked' => 2,  // 解锁状态
            'pending' => 3,  // 待解锁状态
        ];
    }

    /**
     * 定义状态标签
     * 返回一个数组，其中键是状态名称，值是该状态的中文描述。
     * 标签用于界面显示，帮助用户理解每个状态的含义。
     * @return array 状态标签数组
     */
    protected static function labels(): array
    {
        return [
            'default' => '默认状态',  // 默认为未设置状态
            'locked' => '已锁定状态', // 锁定状态，表示该账户或项已被锁定
            'unlocked' => '已解锁状态', // 解锁状态，表示该账户或项已解锁，可以进行操作
            'pending' => '待解锁状态', // 待解锁状态，表示该账户或项正在等待解锁
        ];
    }
}
