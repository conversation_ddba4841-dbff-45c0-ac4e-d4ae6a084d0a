<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class TypeEnum
 * @package App\Enums\Type\
 * @method static self default() 默认
 * @method static self Flagship() 旗舰店
 * @method static self Specialty() 专卖店
 * @method static self Exclusive() 专营店
 * @method static self MallFlagship() 卖场旗舰店
 * @method static self Enterprise() 普通企业店
 * @method static self Personal() 个人普通店
 * @method static self Individual() 个体工商户
 * @method static self Overseas() 海外跨境店铺
 * @method static self OtherType() 其他类型
 */
final class ShopTypeEnum extends BaseEnum
{


    //
    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'Flagship' => 1,
            'Specialty' => 2,
            'Exclusive' => 3,
            'MallFlagship' => 4,
            'Enterprise' => 5,
            'Personal' => 6,
            'Individual' => 7,
            'Overseas' => 8,
            'OtherType' => 9,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'Flagship' => '旗舰店',
            'Specialty' => '专卖店',
            'Exclusive' => '专营店',
            'MallFlagship' => '卖场旗舰店',
            'Enterprise' => '普通企业店',
            'Personal' => '个人普通店',
            'Individual' => '个体工商户',
            'Overseas' => '海外跨境店铺',
            'OtherType' => '其他类型',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        //
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
            6 => 'blue',
            7 => 'indigo',
            8 => 'cyan',
            9 => 'teal',
        ];
    }
}
