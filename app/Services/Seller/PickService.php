<?php

namespace App\Services\Seller;

use App\Enums\State\OrderStateEnum;
use App\Http\Resources\Seller\Pick\PickResource;
use App\Http\Resources\Seller\Pick\PickRowResource;
use App\Http\Resources\Seller\Product\ProductSkuResource;
use App\Models\OrderCenter\OrderItem;
use App\Models\PickCenter\Pick;
use App\Models\PickCenter\PickRow;
use App\Models\ProductCenter\ProductSku;
use App\Services\BaseService;

class PickService extends BaseService
{
    /**
     * @param $User
     * @return mixed
     */
    public static function getStoreByPickCountData($Store)
    {
        //
        $pick_counts = [
            'pick_work_count' => PickRow::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', '=', 1)->orderBy('updated_at', 'desc')->where('main_state', '=', 40)->count() ?? 0,
            'pick_valid_count' => Pick::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', '=', 1)->orderBy('updated_at', 'desc')->where('main_state', '=', 50)->count() ?? 0,
            'pick_done_count' => Pick::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', '=', 1)->where('main_state', '=', 100)->orderBy('updated_at', 'desc')->count() ?? 0,
        ];
        //
        return $pick_counts;
    }

    /**
     * @param $Store
     * @return array
     */
    public static function getStoreByPickWaitList($Store)
    {
        //
        $OrderItem = OrderItem::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->orderBy('created_at', 'desc')->where('is_check', '=', 1)->where('main_state', '=', OrderStateEnum::OrderPicking())->get();
        $collection = collect($OrderItem);
        //
        $pick_rows = [];
        // 先根据skuID进行分组
        $groups = $collection->groupBy('sku_id');
        // 再统计每个skuID的代发货总数
        $groups->each(function ($item, $key) use (&$pick_rows) {
            // 分组后的 $key 就是skuID
            $pick_rows[] = ['sku_id' => $key, 'sku_image_url' => $item->max('sku_image_url'), 'sku_title' => $item->max('name'), 'product_id' => $item->max('product_id'), 'price' => $item->max('price'), 'rice_number' => $item->max('product_rice_number'), 'sku_attrs' => $item->max('sku_attrs'), 'sku_spec' => $item->max('sku_spec'), 'quantity' => $item->sum('quantity'), 'jit_quantity' => $item->sum('quantity'), 'total_quantity' => $item->sum('quantity')];
        });
        //
        foreach ($pick_rows as $index => $pick_item) {
            //
            $ProductSku = ProductSku::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('id', '=', $pick_item['sku_id'])->first();
            //
            $pick_rows[$index]['sku'] = new ProductSkuResource($ProductSku);
            $pick_rows[$index]['sku_title'] = $ProductSku->product->product_title ?? null;
            $pick_rows[$index]['product_no'] = $ProductSku->product_no ?? null;
            $pick_rows[$index]['sku_no'] = $ProductSku->sku_no ?? null;
            $pick_rows[$index]['shelve_code'] = $ProductSku->position->shelve_code ?? null;
            $pick_rows[$index]['layer_code'] = $ProductSku->position->layer_code ?? null;
            $pick_rows[$index]['box_code'] = $ProductSku->position->box_code ?? null;
            $pick_rows[$index]['label'] = isset($ProductSku->position->box_code) ? $ProductSku->position->shelve_name . '-' . $ProductSku->position->layer_name . '-' . $ProductSku->position->box_name : null;
        }
        //
        return $pick_rows;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByPickWorkList($Store)
    {
        //
        $PickRow = PickRow::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', '=', 1)->orderBy('updated_at', 'desc')->where('main_state', '=', 40)->get();
        $pick_item = PickRowResource::collection($PickRow);
        //
        return $pick_item;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByPickValidList($Store)
    {
        //
        $Pick = Pick::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', '=', 1)->orderBy('updated_at', 'desc')->where('main_state', '=', 50)->paginate(50);
        $pick = PickResource::collection($Pick)->resource;
        //
        return $pick;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByPickDoneList($Store)
    {
        //
        $Pick = Pick::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', '=', 1)->where('main_state', '=', 100)->orderBy('updated_at', 'desc')->paginate(50);
        $pick = PickResource::collection($Pick)->resource;
        //
        return $pick;
    }

    /**
     * @param        $Store
     * @param string $picks_uuid
     * @return PickResource
     */
    public static function GetPrintPickValidItemList($Store, string $picks_uuid)
    {
        //
        $Pick = Pick::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->whereUuid($picks_uuid)->orderBy('updated_at', 'desc')->first();
        $pick = new PickResource($Pick);
        //
        return $pick;
    }
}
