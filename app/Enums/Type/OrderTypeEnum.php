<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class OrderTypeEnum
 * @package App\Enums\Type\Order
 * @method static self default() 默认
 * @method static self ShipperOrder() 代发单shipper
 * @method static self SpreadOrder() 本地采购单spread
 * @method static self IntermediaryDistributionOrder() 同业分销单
 * @method static self ChannelGroupPurchaseOrder() 渠道集采单
 * @method static self StoreServiceOrder() 门店服务单门店当面交易，如点餐
 * @method static self PreSaleOrder() 前置销售单（社区团购）
 * @method static self AppointmentOrder() 前置预约单（预约服务单，如酒店）
 * @method static self VerificationOrder() 核销单write
 * @method static self RechargeOrder() 充值单recharge
 * @method static self CollectionOrder() 收款Collection
 * @method static self ElectronicWaybillRechargeOrder() 电子面单充值
 * @method static self ExpressDeliveryOrder() 快递发货单
 * @method static self LocalDeliveryOrder() 同城配送单
 * @method static self LocalFreightOrder() 同城货运单
 * @method static self CrossCityFreightOrder() 跨城货运单
 * @method static self CrossCityLogisticsOrder() 跨城物流单
 * @method static self WarehouseEntryOrder() 仓库入库单
 * @method static self WarehousePerformanceOrder() 仓库履约单
 * @method static self WarehouseLeaseOrder() 仓库租期单
 * @method static self PlatformServiceOrder() 平台服务单
 * @method static self PlatformMerchantCooperationOrder() 平台商家合作单
 * @method static self PlatformServiceProviderCooperationOrder() 平台服务商合作单
 * @method static self PlatformLogisticsProviderCooperationOrder() 平台物流商合作单
 * @method static self PlatformValueAddedOrder() 平台增值单
 * @method static self CrossBorderHostingDomesticWarehouseOrder() 跨境托管（国内仓）单
 * @method static self OverseasHostingFulfillmentBySeaOrder() 海外托管（Fulfillment by sea）单
 * @method static self OverseasDistributionOrder() 海外分销单
 * @method static self OverseasGroupPurchaseOrder() 海外集采单
 */
final class OrderTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'ShipperOrder' => 1,
            'SpreadOrder' => 2,
            'IntermediaryDistributionOrder' => 3,
            'ChannelGroupPurchaseOrder' => 4,
            'StoreServiceOrder' => 5,
            'PreSaleOrder' => 6,
            'AppointmentOrder' => 7,
            'VerificationOrder' => 8,
            'RechargeOrder' => 9,
            'CollectionOrder' => 10,
            'ElectronicWaybillRechargeOrder' => 11,
            'ExpressDeliveryOrder' => 12,
            'LocalDeliveryOrder' => 13,
            'LocalFreightOrder' => 14,
            'CrossCityFreightOrder' => 15,
            'CrossCityLogisticsOrder' => 16,
            'WarehouseEntryOrder' => 17,
            'WarehousePerformanceOrder' => 18,
            'WarehouseLeaseOrder' => 19,
            'PlatformServiceOrder' => 20,
            'PlatformMerchantCooperationOrder' => 21,
            'PlatformServiceProviderCooperationOrder' => 22,
            'PlatformLogisticsProviderCooperationOrder' => 23,
            'PlatformValueAddedOrder' => 24,
            'CrossBorderHostingDomesticWarehouseOrder' => 25,
            'OverseasHostingFulfillmentBySeaOrder' => 26,
            'OverseasDistributionOrder' => 27,
            'OverseasGroupPurchaseOrder' => 28,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'ShipperOrder' => 'shipper',
            'SpreadOrder' => 'spread',
            'IntermediaryDistributionOrder' => '同业分销单',
            'ChannelGroupPurchaseOrder' => '渠道集采单',
            'StoreServiceOrder' => '门店服务单(门店当面交易，如点餐)',
            'PreSaleOrder' => '前置销售单（社区团购）',
            'AppointmentOrder' => '前置预约单（预约服务单，如酒店）',
            'VerificationOrder' => '核销单write',
            'RechargeOrder' => '充值单recharge',
            'CollectionOrder' => '收款Collection',
            'ElectronicWaybillRechargeOrder' => '电子面单充值',
            'ExpressDeliveryOrder' => '快递发货单',
            'LocalDeliveryOrder' => '同城配送单',
            'LocalFreightOrder' => '同城货运单',
            'CrossCityFreightOrder' => '跨城货运单',
            'CrossCityLogisticsOrder' => '跨城物流单',
            'WarehouseEntryOrder' => '仓库入库单',
            'WarehousePerformanceOrder' => '仓库履约单',
            'WarehouseLeaseOrder' => '仓库租期单',
            'PlatformServiceOrder' => '平台服务单',
            'PlatformMerchantCooperationOrder' => '平台商家合作单',
            'PlatformServiceProviderCooperationOrder' => '平台服务商合作单',
            'PlatformLogisticsProviderCooperationOrder' => '平台物流商合作单',
            'PlatformValueAddedOrder' => '平台增值单',
            'CrossBorderHostingDomesticWarehouseOrder' => '跨境托管（国内仓）单',
            'OverseasHostingFulfillmentBySeaOrder' => '海外托管（Fulfillment by sea）单',
            'OverseasDistributionOrder' => '海外分销单',
            'OverseasGroupPurchaseOrder' => '海外集采单',
        ];
    }


}
