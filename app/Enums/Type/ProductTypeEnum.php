<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class BusinessTypeEnum
 * @package App\Enums\Type\Business
 * @method static self default() 默认
 * @method static self regular() 普通产品
 * @method static self Book() 图书
 * @method static self Electronic() 带电产品
 * @method static self Fresh() 生鲜
 * @method static self LargeAppliance() 大件电器
 * @method static self Medicine() 药品
 * @method static self Travel() 旅游
 * @method static self Hotel() 酒店
 * @method static self Virtual() 虚拟
 * @method static self Imported() 进口
 * @method static self OverseasShopping() 国外海淘
 * @method static self DirectMail() 直邮
 * @method static self CrossBorder() 流量
 * @method static self MobileTraffic() 话费
 * @method static self MobileRechargeQQ() QQ充值
 * @method static self FuelCard() 加油卡
 * @method static self CCPostal() CC行邮
 */
final class ProductTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'regular' => 1,
            'Book' => 2,
            'Electronic' => 3,
            'Fresh' => 4,
            'LargeAppliance' => 5,
            'Medicine' => 6,
            'Travel' => 7,
            'Hotel' => 8,
            'Virtual' => 9,
            'Imported' => 12,
            'OverseasShopping' => 13,
            'DirectMail' => 14,
            'CrossBorder' => 15,
            'MobileTraffic' => 16,
            'MobileRechargeQQ' => 18,
            'FuelCard' => 19,
            'CCPostal' => 20,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'regular' => '普通产品',
            'Book' => '图书',
            'Electronic' => '带电产品',
            'Fresh' => '生鲜',
            'LargeAppliance' => '大件电器',
            'Medicine' => '药品',
            'Travel' => '旅游',
            'Hotel' => '酒店',
            'Virtual' => '虚拟',
            'Imported' => '进口',
            'OverseasShopping' => '国外海淘',
            'DirectMail' => '直邮',
            'CrossBorder' => '流量',
            'MobileTraffic' => '话费',
            'MobileRechargeQQ' => 'QQ充值',
            'FuelCard' => '加油卡',
            'CCPostal' => 'CC行邮',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
            6 => 'blue',
            7 => 'indigo',
            8 => 'violet',
        ];
    }
}
