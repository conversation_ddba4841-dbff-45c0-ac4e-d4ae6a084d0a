<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * Class ProductConfirmStateEnum
 * @package App\Enums\State\Product
 * @method static self WaitConfirmed() 待确认状态
 * @method static self AllConfirmed() 全部已确认状态
 * @method static self PartialConfirmed() 部分确认状态
 * @method static self UnConfirmed() 已下架停产/匹配失败状态
 */
final class IsConfirmEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'WaitConfirmed' => 0,
            'AllConfirmed' => 1,
            'PartialConfirmed' => 2,
            'UnConfirmed' => 3,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'WaitConfirmed' => '待确认',
            'AllConfirmed' => '全部已确认',
            'PartialConfirmed' => '部分确认',
            'UnConfirmed' => '已下架停产/匹配失败',
        ];
    }
}
