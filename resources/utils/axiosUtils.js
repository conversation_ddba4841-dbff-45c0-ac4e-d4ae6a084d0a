/**
 * 我们将加载 Axios HTTP 库，它允许我们轻松地向 Laravel 后端发出请求。
 * 这个库会基于 "XSRF" 令牌 cookie 的值自动处理发送 CSRF 令牌作为头信息。
 */

import axios from 'axios';
import md5 from 'js-md5';

// 将 axios 挂载到全局窗口对象上，以便在整个应用中使用
window.axios = axios;

// 设置默认的请求头，标识这是一个 XMLHttpRequest 请求
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * Echo 提供了一个富有表现力的 API，用于订阅频道和监听由 Laravel 广播的事件。
 * Echo 和事件广播允许您的团队轻松构建强大的实时 Web 应用程序。
 * 这里的代码被注释掉了，如果需要实时功能可以根据需要取消注释并配置。
 */

// import Echo from 'laravel-echo';
// import Pusher from 'pusher-js';
// window.Pusher = Pusher;

// window.Echo = new Echo({
//     broadcaster: 'pusher',
//     key: import.meta.env.VITE_PUSHER_APP_KEY,
//     cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER ?? 'mt1',
//     wsHost: import.meta.env.VITE_PUSHER_HOST ? import.meta.env.VITE_PUSHER_HOST : `ws-${import.meta.env.VITE_PUSHER_APP_CLUSTER}.pusher.com`,
//     wsPort: import.meta.env.VITE_PUSHER_PORT ?? 80,
//     wssPort: import.meta.env.VITE_PUSHER_PORT ?? 443,
//     forceTLS: (import.meta.env.VITE_PUSHER_SCHEME ?? 'https') === 'https',
//     enabledTransports: ['ws', 'wss'],
// });

/**
 * Axios 插件，用于在 Vue 应用中全局配置 Axios。
 * 该插件包括请求和响应的拦截器，以及 CSRF 令牌的配置。
 */
const AxiosUtils = {
    install(app) {
        /**
         * 获取 CSRF token
         * 从 HTML 的 meta 标签中获取 CSRF 令牌的值
         */
        const csrfTokenElement = document.head.querySelector('meta[name="csrf-token"]');
        if (csrfTokenElement) {
            // 将 CSRF 令牌设置为 Axios 的默认请求头
            axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfTokenElement.content;
        } else {
            // 如果未找到 CSRF 令牌，输出错误信息
            console.error('未在 meta 标签中找到 CSRF 令牌。请确保 Blade 模板中包含：<meta name="csrf-token" content="{{ csrf_token() }}">');
        }

        /**
         * 全局配置 Axios
         */
        axios.defaults.baseURL = '/'; // 根据需要替换为您的 API 基础地址
        axios.defaults.headers.common['Content-Type'] = 'application/json'; // 设置默认的 Content-Type
        axios.defaults.withCredentials = true; // 允许 Axios 发送带有凭证的请求（如 cookies）
        // 获取当前页面的URL参数
        const getUrlParams = () => {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                tab: urlParams.get('tab'), // 获取tab参数
                pageParam: urlParams.get('page'), // 获取page参数
                keyword: urlParams.get('keyword') // 获取keyword参数
            };
        };
        /**
         * 请求拦截器
         * 在每个请求发送之前执行一些操作
         */
        axios.interceptors.request.use(
            async (config) => {
                /**
                 * 从请求配置中提取 'page' 参数
                 * 这里尝试从不同的位置（config、config.params、config.data）获取 'page' 参数
                 */
                const {tab, pageParam, keyword} = getUrlParams();
                //
                console.log(keyword, 'keyword');
                //let  = page || 0;
                let page = (config && config.page) || (config && config.params && config.params.page) || (config && config.data && config.data.page) || pageParam || 0;

                // 如果存在 'page' 参数，则进行加密处理并添加额外参数
                if (page) {
                    config.params = {
                        ...config.params,
                        page: page, // 原始的 page 参数
                        keyword: keyword,
                        pg: md5(page + '2333.'), // 加密后的 pv 参数，使用 md5 算法
                        state: tab, // 添加tab参数
                        type: 'json', // 固定的 type 参数
                        s: 'pc_index', // 固定的 s 参数
                    };
                } else {
                    // 如果不存在 'page' 参数，仅添加固定的 type 和 s 参数
                    config.params = {
                        ...config.params,
                        page: page,
                        keyword: keyword,
                        pg: md5(page + '2333.'),
                        state: tab, // 添加tab参数
                        type: 'json',
                        s: 'pc_index',
                    };
                }
                //
                return config; // 返回修改后的配置
            },
            (error) => {
                // 如果请求错误，直接返回错误
                return Promise.reject(error);
            }
        );

        /**
         * 响应拦截器
         * 在每个响应返回之后执行一些操作
         */
        axios.interceptors.response.use(
            (response) => {
                // 如果响应状态码是 200，返回响应数据
                if (response.status === 200) {
                    return response.data;
                } else {
                    // 否则，返回错误的响应
                    return Promise.reject(response);
                }
            },
            (error) => {
                if (error.response) {
                    // 如果服务器有响应但状态码不是 2xx
                    if (error.response.status === 404) {
                        // 处理 404 错误
                        console.error('404 Not Found');
                    } else if (error.response.status === 419) {
                        // 处理 CSRF 令牌不匹配错误（状态码 419）
                        console.error('CSRF 令牌不匹配。请刷新页面。');
                        // 可选：自动刷新页面以获取新的 CSRF 令牌
                        window.location.reload();
                    } else {
                        // 处理其他类型的错误
                        console.error('错误状态码：', error.response.status);
                    }
                } else if (error.request) {
                    // 如果请求已发送但未收到响应
                    console.error('未收到响应。请求信息：', error.request);
                } else {
                    // 处理其他类型的错误
                    console.error('请求错误：', error.message);
                }
                // 返回被拒绝的 Promise 以便在调用处处理
                return Promise.reject(error);
            }
        );

        /**
         * 将 Axios 实例添加到 Vue 全局属性中
         * 这样在 Vue 组件中可以通过 this.$axios 访问 Axios
         */
        app.config.globalProperties.$axios = axios;
    },
};

export default AxiosUtils;
