import {defineStore} from 'pinia'

/**
 * 应用系统核心Store
 * 专注于应用核心状态管理、加载状态、错误上报、基础统计
 * 用户体验优化导向的轻量级状态管理
 */
export const useAppStoreSystem = defineStore('appSystem', {
    state: () => ({
        // === 应用核心状态 ===
        isInitialized: false,
        appVersion: '1.0.0',
        startTime: Date.now(),

        // === 系统配置 ===
        systemConfig: {
            debug: import.meta.env.DEV,
            environment: import.meta.env.MODE,
            apiBaseUrl: import.meta.env.VITE_APP_API_BASE_URL || '',
            autoSave: true,
            maxRetries: 3,
            animationDuration: 3000
        },

        // === 加载状态管理 ===
        loading: false,
        isMenuLoading: false, // 页面内切换切换时的过渡加载
        transitionLoading: false, // 前后台切换时的过渡加载
        isInitialLoad: true,
        isAppInitialized: false, // 应用是否已完成首次初始化
        loadingProgress: 0,
        loadingMessage: '',
        loadingQueue: [], // 加载队列管理

        // === 用户活动跟踪 ===
        lastActivity: Date.now(),
        isUserActive: true,

        // === 用户体验配置 ===
        userExperience: {
            autoSaveInterval: 30000, // 30秒自动保存
            showLoadingMessages: true,
            enableSmoothTransitions: true,
            enableAnimations: true,
            loadingMinDuration: 5000 // 最小加载显示时间，避免闪烁
        },

        // === 错误统计和上报 ===
        errorStats: {
            totalErrors: 0,
            errorsByType: {},
            lastError: null,
            errorHistory: [], // 保存最近10个错误
            maxErrorHistory: 10
        },

        // === 操作统计 ===
        operationStats: {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            averageResponseTime: 0,
            operationTimes: []
        },

        // === 应用性能简单统计 ===
        performanceStats: {
            pageLoadTime: 0,
            renderTime: 0,
            initializedAt: null
        }
    }),

    getters: {
        // === 基础状态获取器 ===
        getInitializeStatus: (state) => state.isInitialized,
        getSystemConfig: (state) => state.systemConfig,
        getAppUptime: (state) => Date.now() - state.startTime,

        // === 加载状态获取器 ===
        isLoading: (state) => state.loading,
        isTransitionLoading: (state) => state.transitionLoading,
        getLoadingProgress: (state) => state.loadingProgress,
        getLoadingMessage: (state) => state.loadingMessage,
        hasLoadingQueue: (state) => state.loadingQueue.length > 0,
        getAppInitialized: (state) => state.isAppInitialized, // 避免与state属性名冲突

        // === 显示全局加载的条件 ===
        shouldShowGlobalLoading: (state) => state.loading && state.isInitialLoad && !state.isAppInitialized,

        // === 用户活动获取器 ===
        getUserActive: (state) => state.isUserActive,
        getIdleTime: (state) => Date.now() - state.lastActivity,

        // === 错误统计获取器 ===
        getErrorRate: (state) => {
            const total = state.operationStats.totalOperations
            return total > 0 ? ((state.errorStats.totalErrors / total) * 100).toFixed(2) : 0
        },
        getLastError: (state) => state.errorStats.lastError,
        getErrorHistory: (state) => state.errorStats.errorHistory,

        // === 成功率获取器 ===
        getSuccessRate: (state) => {
            const total = state.operationStats.totalOperations
            return total > 0 ? ((state.operationStats.successfulOperations / total) * 100).toFixed(1) : 100
        },

        // === 应用健康状态 ===
        getAppHealth: (state) => {
            const errorRate = state.operationStats.totalOperations > 0 ?
                (state.errorStats.totalErrors / state.operationStats.totalOperations) * 100 : 0

            if (errorRate > 10) return 'poor'
            if (errorRate > 5) return 'fair'
            return 'good'
        }
    },

    actions: {
        /**
         * 初始化应用
         */
        async initializeApp() {
            const startTime = performance.now()

            try {
                console.log('🔧 应用系统初始化开始...')

                this.loading = true
                this.isInitialLoad = true
                this.loadingProgress = 0
                this.updateLoadingMessage('正在初始化系统...')

                // 模拟初始化步骤
                await this.performInitializationSteps()

                // 记录初始化完成时间
                this.performanceStats.initializedAt = Date.now()
                this.performanceStats.pageLoadTime = performance.now() - startTime

                this.isInitialized = true
                this.isAppInitialized = true // 标记应用已完成首次初始化
                this.incrementSuccessfulOperations()

                console.log('✅ 应用系统初始化完成')

            } catch (error) {
                this.recordError(error, 'app_initialization')
                console.error('❌ 应用初始化失败:', error)
                throw error
            } finally {
                // 确保加载状态至少显示最小时间，避免闪烁
                const elapsed = performance.now() - startTime
                const minDelay = Math.max(0, this.userExperience.loadingMinDuration - elapsed)

                setTimeout(() => {
                    this.loading = false
                    this.isInitialLoad = false // 首次加载完成
                    this.loadingProgress = 100
                    this.updateLoadingMessage('初始化完成')
                }, minDelay)
            }
        },

        /**
         * 执行初始化步骤
         */
        async performInitializationSteps() {
            const steps = [
                {progress: 20, message: '加载核心模块...', delay: 150},
                {progress: 40, message: '初始化数据存储...', delay: 200},
                {progress: 60, message: '准备用户界面...', delay: 150},
                {progress: 80, message: '建立连接...', delay: 100},
                {progress: 95, message: '完成初始化...', delay: 100}
            ]

            for (const step of steps) {
                await new Promise(resolve => setTimeout(resolve, step.delay))
                this.loadingProgress = step.progress
                this.updateLoadingMessage(step.message)
            }
        },

        // === 加载状态管理 ===
        setLoading(status, message = '') {
            const loadingId = Date.now() + Math.random()

            if (status) {
                // 添加到加载队列
                this.loadingQueue.push({id: loadingId, message, startTime: Date.now()})
                this.loading = true
            } else {
                // 从队列中移除
                this.loadingQueue = this.loadingQueue.filter(item => item.id !== loadingId)
                this.loading = this.loadingQueue.length > 0
            }

            if (message) {
                this.updateLoadingMessage(message)
            }

            if (!status) {
                this.updateLastActivity()
            }

            return loadingId
        },

        setTransitionLoading(status, message = '') {
            this.transitionLoading = status
            if (message) {
                this.updateLoadingMessage(message)
            }
        },
        setMenuLoading(status, message = '') {
            this.isMenuLoading = status
            if (message) {
                this.updateLoadingMessage(message)
            }
        },
        //
        updateLoadingMessage(message) {
            if (this.userExperience.showLoadingMessages) {
                this.loadingMessage = message
            }
        },

        setLoadingProgress(progress) {
            this.loadingProgress = Math.max(0, Math.min(100, progress))
        },

        // === 用户活动管理 ===
        updateLastActivity() {
            this.lastActivity = Date.now()
            this.isUserActive = true
        },

        setUserInactive() {
            this.isUserActive = false
        },

        // === 错误处理和上报 ===
        recordError(error, context = '') {
            const errorInfo = {
                id: Date.now() + Math.random(),
                message: error.message || error.toString(),
                context,
                timestamp: Date.now(),
                stack: error.stack,
                url: window.location.href,
                userAgent: navigator.userAgent
            }

            // 更新错误统计
            this.errorStats.totalErrors++
            this.errorStats.lastError = errorInfo

            // 按类型统计错误
            const errorType = context || 'unknown'
            this.errorStats.errorsByType[errorType] = (this.errorStats.errorsByType[errorType] || 0) + 1

            // 添加到错误历史
            this.errorStats.errorHistory.unshift(errorInfo)
            if (this.errorStats.errorHistory.length > this.errorStats.maxErrorHistory) {
                this.errorStats.errorHistory.pop()
            }

            // 更新操作统计
            this.incrementFailedOperations()

            // 在开发环境下详细输出错误信息
            if (this.systemConfig.debug) {
                console.group('🔍 错误详情')
                console.error('错误信息:', errorInfo)
                console.error('应用状态:', {
                    isInitialized: this.isInitialized,
                    loading: this.loading,
                    totalErrors: this.errorStats.totalErrors
                })
                console.groupEnd()
            }
        },

        reportError(errorInfo) {
            // 简化的错误上报，只记录不弹窗
            this.recordError(errorInfo, errorInfo.context)
        },

        clearErrorHistory() {
            this.errorStats.errorHistory = []
            this.errorStats.errorsByType = {}
        },

        // === 操作统计管理 ===
        recordOperationStart(operationName) {
            return {
                name: operationName,
                startTime: performance.now(),
                id: Date.now() + Math.random()
            }
        },

        recordOperationEnd(operationContext, success = true) {
            const endTime = performance.now()
            const duration = endTime - operationContext.startTime

            // 记录操作时间
            this.operationStats.operationTimes.push(duration)
            if (this.operationStats.operationTimes.length > 100) {
                this.operationStats.operationTimes.shift() // 只保留最近100次操作时间
            }

            // 更新平均响应时间
            this.operationStats.averageResponseTime =
                this.operationStats.operationTimes.reduce((a, b) => a + b, 0) /
                this.operationStats.operationTimes.length

            this.operationStats.totalOperations++

            if (success) {
                this.incrementSuccessfulOperations()
            } else {
                this.incrementFailedOperations()
            }
        },

        incrementSuccessfulOperations() {
            this.operationStats.successfulOperations++
        },

        incrementFailedOperations() {
            this.operationStats.failedOperations++
        },

        // === 自动保存功能 ===
        startAutoSave() {
            if (this.userExperience.autoSave) {
                setInterval(() => {
                    this.performAutoSave()
                }, this.userExperience.autoSaveInterval)
            }
        },

        performAutoSave() {
            try {
                // 触发自动保存事件
                const event = new CustomEvent('app:auto-save', {
                    detail: {timestamp: Date.now()}
                })
                window.dispatchEvent(event)

                this.incrementSuccessfulOperations()

            } catch (error) {
                console.warn('自动保存失败:', error)
                this.recordError(error, 'auto_save')
            }
        },

        // === 系统配置管理 ===
        updateSystemConfig(config) {
            this.systemConfig = {...this.systemConfig, ...config}
        },

        updateUserExperience(settings) {
            this.userExperience = {...this.userExperience, ...settings}
        },

        // === 便捷操作方法 ===
        async performOperation(operationName, operation) {
            const context = this.recordOperationStart(operationName)

            try {
                const result = await operation()
                this.recordOperationEnd(context, true)
                return result
            } catch (error) {
                this.recordOperationEnd(context, false)
                this.recordError(error, operationName)
                throw error
            }
        },

        // === 应用状态重置 ===
        resetAppState() {
            this.isInitialized = false
            this.loading = false
            this.transitionLoading = false
            this.isInitialLoad = true
            this.isAppInitialized = false // 重置应用初始化状态
            this.loadingProgress = 0
            this.loadingMessage = ''
            this.loadingQueue = []
        },

        // === 优雅关闭 ===
        async gracefulShutdown() {
            try {
                console.log('🔄 应用正在优雅关闭...')

                // 执行最后一次自动保存
                this.performAutoSave()

                // 触发关闭事件
                const event = new CustomEvent('app:shutdown', {
                    detail: {
                        timestamp: Date.now(),
                        uptime: this.getAppUptime
                    }
                })
                window.dispatchEvent(event)

                console.log('✅ 应用已优雅关闭')

            } catch (error) {
                console.error('❌ 优雅关闭失败:', error)
                this.recordError(error, 'graceful_shutdown')
            }
        },

        // === 获取应用统计摘要 ===
        getAppSummary() {
            return {
                uptime: this.getAppUptime,
                totalOperations: this.operationStats.totalOperations,
                successRate: this.getSuccessRate,
                errorRate: this.getErrorRate,
                averageResponseTime: this.operationStats.averageResponseTime.toFixed(2),
                health: this.getAppHealth,
                lastActivity: new Date(this.lastActivity).toLocaleString()
            }
        }
    },

    // 数据持久化配置
    persist: {
        key: 'app-system-state',
        storage: localStorage,
        paths: ['userExperience', 'systemConfig', 'performanceStats']
    }
})
