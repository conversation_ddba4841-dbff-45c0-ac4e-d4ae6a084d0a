<!--
/**
 * 产品管理页面
 *
 * 功能特性：
 * - 企业级产品管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 产品状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/product/product
 * 页面标题：产品列表
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="product-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索产品名称、SKU编号或品牌名称'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出产品
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchOffline"
                    type="warning"
                    @click="handleBatchOffline"
                >
                    <i class="fas fa-eye-slash"></i>
                    批量下架 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchOnline"
                    type="info"
                    @click="handleBatchOnline"
                >
                    <i class="fas fa-eye"></i>
                    批量上架 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="产品状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in PRODUCT_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类">
                            <el-select v-model="filters.category" clearable placeholder="选择分类">
                                <el-option label="全部分类" value=""></el-option>
                                <el-option label="电子产品" value="electronics"></el-option>
                                <el-option label="服装配饰" value="clothing"></el-option>
                                <el-option label="家居用品" value="home"></el-option>
                                <el-option label="运动户外" value="sports"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item label="价格范围">
                            <el-input-number
                                v-model="filters.min_price"
                                :min="0"
                                :precision="2"
                                placeholder="最低价格"
                                style="width: 120px; margin-right: 8px;"
                            />
                            <span style="margin: 0 8px;">-</span>
                            <el-input-number
                                v-model="filters.max_price"
                                :min="0"
                                :precision="2"
                                placeholder="最高价格"
                                style="width: 120px;"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 产品列表 -->
            <template #default>
                <ProductListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总产品: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						在售: <strong>{{ onlineCount }}</strong>
					</span>
                    <span class="stat-item">
						总价值: <strong>¥{{ formatAmount(totalValue) }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import ProductListView from './components/ProductListView.vue'

export default {
    name: 'AdminProductProductIndexPage',
    components: {
        BackendPageListLayout,
        ProductListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                category: '',
                start_date: '',
                end_date: '',
                min_price: null,
                max_price: null
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            onlineCount: 0,
            totalValue: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部产品', icon: 'fas fa-list', badge: 0},
                {name: 'online', label: '在售中', icon: 'fas fa-eye', badge: 0},
                {name: 'offline', label: '已下架', icon: 'fas fa-eye-slash', badge: 0},
                {name: 'draft', label: '草稿', icon: 'fas fa-edit', badge: 0},
                {name: 'out_of_stock', label: '缺货', icon: 'fas fa-exclamation-triangle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 产品状态标签
            PRODUCT_STATUS_LABELS: {
                'online': '在售中',
                'offline': '已下架',
                'draft': '草稿',
                'out_of_stock': '缺货'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchOffline() {
            return this.selectedRows.some(row => row.status === 'online')
        },
        canBatchOnline() {
            return this.selectedRows.some(row => ['offline', 'draft'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟产品数据
                const mockData = [
                    {
                        id: 1,
                        sku: 'PRD202401150001',
                        product_name: 'iPhone 15 Pro Max 256GB',
                        brand: 'Apple',
                        category: 'electronics',
                        price: 9999.00,
                        stock: 156,
                        sales: 2340,
                        status: 'online',
                        image_url: '/images/products/iphone15.jpg',
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        sku: 'PRD202401150002',
                        product_name: '小米14 Ultra 512GB',
                        brand: '小米',
                        category: 'electronics',
                        price: 6999.00,
                        stock: 0,
                        sales: 1890,
                        status: 'out_of_stock',
                        image_url: '/images/products/mi14.jpg',
                        created_at: '2024-01-15 11:20:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 3,
                        sku: 'PRD202401150003',
                        product_name: 'Nike Air Max 270',
                        brand: 'Nike',
                        category: 'sports',
                        price: 899.00,
                        stock: 89,
                        sales: 567,
                        status: 'offline',
                        image_url: '/images/products/nike-air.jpg',
                        created_at: '2024-01-15 12:45:00',
                        updated_at: '2024-01-15 12:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 3456
                this.onlineCount = 2134
                this.totalValue = 12567890.50
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount || 0).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 2134
            this.tabOptions[2].badge = 856
            this.tabOptions[3].badge = 234
            this.tabOptions[4].badge = 232
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                category: '',
                start_date: '',
                end_date: '',
                min_price: null,
                max_price: null
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true

                // 模拟导出
                await new Promise(resolve => setTimeout(resolve, 2000))

                this.$message.success('产品数据导出成功')

            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchOffline() {
            const offlineableProducts = this.selectedRows.filter(row =>
                row.status === 'online'
            )

            if (offlineableProducts.length === 0) {
                this.$message.warning('没有可下架的产品')
                return
            }

            this.$confirm(`确定要下架选中的 ${offlineableProducts.length} 个产品吗？`, '确认批量下架', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功下架 ${offlineableProducts.length} 个产品`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchOnline() {
            const onlineableProducts = this.selectedRows.filter(row =>
                ['offline', 'draft'].includes(row.status)
            )

            if (onlineableProducts.length === 0) {
                this.$message.warning('没有可上架的产品')
                return
            }

            this.$confirm(`确定要上架选中的 ${onlineableProducts.length} 个产品吗？`, '确认批量上架', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功上架 ${onlineableProducts.length} 个产品`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看产品详情: ${row.product_name}`)
            // TODO: 跳转到产品详情页面
            // this.$router.push(`/admin/product/product/detail/${row.id}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑产品: ${row.product_name}`)
            // TODO: 跳转到产品编辑页面
            // this.$router.push(`/admin/product/product/edit/${row.id}`)
        },

        handleStatusChange(row, status) {
            this.$confirm(`确定要将产品 "${row.product_name}" 状态更改为 "${this.PRODUCT_STATUS_LABELS[status]}" 吗？`, '确认状态变更', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`产品状态已更新为: ${this.PRODUCT_STATUS_LABELS[status]}`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消状态变更')
            })
        },

        handleDelete(row) {
            this.$confirm(`确定要删除产品 "${row.product_name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('产品删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.product-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;

        .el-form-item {
            margin-bottom: 16px;
        }
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .product-index-page {
        .advanced-search-form {
            padding: 12px;

            .el-form-item {
                margin-bottom: 12px;
            }
        }

        .footer-stats {
            flex-direction: column;
            gap: 8px;
        }
    }
}
</style>
