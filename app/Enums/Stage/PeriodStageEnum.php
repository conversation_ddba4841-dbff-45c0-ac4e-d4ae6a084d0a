<?php

namespace App\Enums\Stage;

use App\Enums\BaseEnum;


/**
 * Class PeriodStageEnum
 * @package App\Enums\State\Period
 * @method static self default() 默认状态
 * @method static self UserCreated() 用户创建订单状态
 * @method static self MerchantPicking() 商家配货拣货状态
 * @method static self ProviderService() 服务商服务状态
 * @method static self LogisticsTransport() 物流商运输状态
 * @method static self UserFulfillment() 用户履约状态
 */
final class PeriodStageEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'UserCreated' => 1,
            'MerchantPicking' => 2,
            'ProviderService' => 3,
            'LogisticsTransport' => 4,
            'UserFulfillment' => 5,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'UserCreated' => '用户创建订单',
            'MerchantPicking' => '商家配货拣货',
            'ProviderService' => '服务商服务',
            'LogisticsTransport' => '物流商运输',
            'UserFulfillment' => '用户履约',
        ];
    }
}
