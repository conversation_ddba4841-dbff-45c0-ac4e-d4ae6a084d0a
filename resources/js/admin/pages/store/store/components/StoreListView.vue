<!--
/**
 * 商店列表视图组件
 *
 * 功能特性：
 * - 商店数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 * - 敏感信息脱敏处理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="store-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 商店信息 -->
            <el-table-column
                label="商店信息"
                min-width="200"
            >
                <template #default="{ row }">
                    <div class="store-info">
                        <div class="store-name">
                            <i class="fas fa-store store-icon"></i>
                            <span class="name">{{ row.store_name }}</span>
                        </div>
                        <div class="store-code">
                            <i class="fas fa-barcode code-icon"></i>
                            <span class="code">{{ row.store_code }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 店主信息 -->
            <el-table-column
                label="店主信息"
                min-width="160"
            >
                <template #default="{ row }">
                    <div class="owner-info">
                        <div class="owner-name">
                            <i class="fas fa-user owner-icon"></i>
                            <span>{{ row.owner_name }}</span>
                        </div>
                        <div class="owner-phone">
                            <i class="fas fa-phone phone-icon"></i>
                            <span class="sensitive-data">{{ row.owner_phone }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 商店分类 -->
            <el-table-column
                align="center"
                label="商店分类"
                prop="category"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getCategoryType(row.category)"
                        size="small"
                    >
                        <i :class="getCategoryIcon(row.category)" class="category-icon"></i>
                        {{ getCategoryName(row.category) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 月销售额 -->
            <el-table-column
                align="right"
                label="月销售额"
                prop="monthly_sales"
                sortable
                width="140"
            >
                <template #default="{ row }">
                    <div class="sales-cell">
                        <span class="amount">¥{{ formatAmount(row.monthly_sales) }}</span>
                        <div class="product-count">{{ row.product_count }}件商品</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 商店状态 -->
            <el-table-column
                align="center"
                label="商店状态"
                prop="status"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        <i :class="getStatusIcon(row.status)" class="status-icon"></i>
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 开店时间 -->
            <el-table-column
                label="开店时间"
                prop="created_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="datetime-cell">
                        <div class="date">{{ formatDate(row.created_at) }}</div>
                        <div class="time">{{ formatTime(row.created_at) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleView(row)"
                    >
                        查看
                    </el-button>
                    <el-button
                        v-if="canEdit(row)"
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        编辑
                    </el-button>
                    <el-dropdown trigger="click">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    v-if="row.status === 'active'"
                                    @click="handleStatusChange(row, 'suspended')"
                                >
                                    <i class="fas fa-pause"></i>
                                    暂停商店
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="['suspended', 'pending'].includes(row.status)"
                                    @click="handleStatusChange(row, 'active')"
                                >
                                    <i class="fas fa-play"></i>
                                    激活商店
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'pending'"
                                    @click="handleStatusChange(row, 'reviewing')"
                                >
                                    <i class="fas fa-search"></i>
                                    开始审核
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="['active', 'suspended'].includes(row.status)"
                                    @click="handleStatusChange(row, 'closed')"
                                >
                                    <i class="fas fa-times"></i>
                                    关闭商店
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="handleDelete(row)">
                                    <i class="fas fa-trash"></i>
                                    删除商店
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'StoreListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'status-change',
        'delete',
        'data-change'
    ],
    data() {
        return {
            statusMap: {
                'active': '活跃',
                'pending': '待审核',
                'suspended': '已暂停',
                'closed': '已关闭',
                'reviewing': '审核中'
            },
            categoryMap: {
                'clothing': '服装类',
                'digital': '数码类',
                'food': '食品类',
                'home': '家居类'
            }
        }
    },
    methods: {
        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleView(row) {
            this.$emit('view-detail', row)
        },

        // 编辑商店
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 状态变化
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 删除商店
        handleDelete(row) {
            this.$emit('delete', row)
            // 触发数据变化事件
            this.$emit('data-change')
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount).toFixed(2)
        },

        // 格式化日期
        formatDate(datetime) {
            return datetime.split(' ')[0]
        },

        // 格式化时间
        formatTime(datetime) {
            return datetime.split(' ')[1]
        },

        // 获取分类名称
        getCategoryName(category) {
            return this.categoryMap[category] || category
        },

        // 获取分类图标
        getCategoryIcon(category) {
            const iconMap = {
                'clothing': 'fas fa-tshirt',
                'digital': 'fas fa-laptop',
                'food': 'fas fa-utensils',
                'home': 'fas fa-home'
            }
            return iconMap[category] || 'fas fa-tag'
        },

        // 获取分类类型
        getCategoryType(category) {
            const typeMap = {
                'clothing': 'primary',
                'digital': 'success',
                'food': 'warning',
                'home': 'info'
            }
            return typeMap[category] || 'info'
        },

        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态图标
        getStatusIcon(status) {
            const iconMap = {
                'active': 'fas fa-check-circle',
                'pending': 'fas fa-clock',
                'suspended': 'fas fa-pause-circle',
                'closed': 'fas fa-times-circle',
                'reviewing': 'fas fa-search'
            }
            return iconMap[status] || 'fas fa-question-circle'
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'active': 'success',
                'pending': 'warning',
                'suspended': 'info',
                'closed': 'danger',
                'reviewing': 'primary'
            }
            return typeMap[status] || 'info'
        },

        // 判断是否可以编辑
        canEdit(row) {
            return ['active', 'pending', 'suspended'].includes(row.status)
        }
    }
}
</script>

<style lang="scss" scoped>
.store-list-view {
    .admin-table {
        // 商店信息单元格
        .store-info {
            .store-name {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 4px;

                .store-icon {
                    color: #409eff;
                    font-size: 14px;
                }

                .name {
                    font-weight: 500;
                    color: #303133;
                }
            }

            .store-code {
                display: flex;
                align-items: center;
                gap: 6px;

                .code-icon {
                    color: #909399;
                    font-size: 12px;
                }

                .code {
                    font-size: 12px;
                    color: #606266;
                    font-family: 'Courier New', monospace;
                }
            }
        }

        // 店主信息单元格
        .owner-info {
            .owner-name {
                display: flex;
                align-items: center;
                gap: 6px;
                margin-bottom: 4px;

                .owner-icon {
                    color: #606266;
                    font-size: 12px;
                }

                span {
                    font-weight: 500;
                    color: #303133;
                }
            }

            .owner-phone {
                display: flex;
                align-items: center;
                gap: 6px;

                .phone-icon {
                    color: #909399;
                    font-size: 12px;
                }

                .sensitive-data {
                    font-size: 12px;
                    color: #606266;
                    font-family: 'Courier New', monospace;
                }
            }
        }

        // 销售额单元格
        .sales-cell {
            text-align: right;

            .amount {
                font-weight: 600;
                color: #e6a23c;
                font-size: 14px;
            }

            .product-count {
                font-size: 12px;
                color: #909399;
                margin-top: 2px;
            }
        }

        // 时间单元格
        .datetime-cell {
            .date {
                font-weight: 500;
                color: #303133;
                margin-bottom: 2px;
            }

            .time {
                font-size: 12px;
                color: #909399;
            }
        }

        // 图标样式
        .category-icon,
        .status-icon {
            margin-right: 4px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .store-list-view {
        .admin-table {
            font-size: 12px;

            .store-info,
            .owner-info {
                .store-name,
                .store-code,
                .owner-name,
                .owner-phone {
                    font-size: 11px;
                }
            }

            .sales-cell {
                .amount {
                    font-size: 13px;
                }

                .product-count {
                    font-size: 11px;
                }
            }

            .datetime-cell {
                .date {
                    font-size: 12px;
                }

                .time {
                    font-size: 11px;
                }
            }
        }
    }
}
</style>
