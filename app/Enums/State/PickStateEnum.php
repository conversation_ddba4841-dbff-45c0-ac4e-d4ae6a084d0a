<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;


/**
 * Class PickStateEnum
 * @package App\Enums\State\Pick
 * @method static self default() 默认状态
 * @method static self wait() 待配货
 * @method static self inventory() 未匹配到库存
 * @method static self out() 缺货处理中（待采购）
 * @method static self replenish() 库存补货中（采购中）
 * @method static self matched() 已匹配到库存
 * @method static self work() 拣货中
 * @method static self valid() 拣货复核
 * @method static self pack() 拣货打包
 * @method static self partial() 部分完成
 * @method static self done() 拣货完成
 * @method static self freeze() 拣货冻结（拣货异常）
 * @method static self cancelled() 拣货取消
 */
final class PickStateEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            //
            'wait' => 10,
            'inventory' => 20,
            'out' => 30,
            'replenish' => 40,
            'matched' => 50,
            'work' => 60,
            'valid' => 70,
            'pack' => 80,
            'partial' => 90,
            'done' => 100,
            'freeze' => 150,
            'cancelled' => 200,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            //
            'wait' => '待配货',
            'inventory' => '未匹配到库存',
            'out' => '缺货处理中（待采购）',
            'replenish' => '库存补货中（采购中）',
            'matched' => '已匹配到库存',
            'work' => '拣货中',
            'valid' => '拣货复核',
            'pack' => '拣货打包',
            'partial' => '部分完成',
            'done' => '拣货完成',
            'freeze' => '拣货冻结（拣货异常）',
            'cancelled' => '拣货取消',
        ];
    }
}
