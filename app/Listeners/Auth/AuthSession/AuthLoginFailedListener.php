<?php

namespace App\Listeners\Auth\AuthSession;

use Illuminate\Auth\Events\Failed;
use Illuminate\Http\Request;
use Rappasoft\LaravelAuthenticationLog\Notifications\FailedLogin;
use Rappasoft\LaravelAuthenticationLog\Traits\AuthenticationLoggable;

class AuthLoginFailedListener
{
    // 定义一个Request属性，用于存储当前的HTTP请求实例
    public Request $request;

    // 构造函数，初始化Request属性
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    // 事件处理方法，接收一个事件实例
    public function handle($event): void
    {
        // 获取配置文件中的登录失败事件类，如果没有设置，则默认为Illuminate\Auth\Events\Failed
        $listener = config('authentication-log.events.failed', Failed::class);

        // 检查事件实例是否为登录失败事件的实例，如果不是则直接返回
        if (!$event instanceof $listener) {
            return;
        }

        // 如果事件中有用户对象
        if ($event->user) {
            // 检查用户类是否使用了AuthenticationLoggable trait，如果没有使用则直接返回
            if (!in_array(AuthenticationLoggable::class, class_uses_recursive(get_class($event->user)))) {
                return;
            }

            // 获取用户的IP地址，如果配置文件中设置了behind_cdn则从特定的HTTP头字段中获取IP地址
            if (config('authentication-log.behind_cdn')) {
                $ip = $this->request->server(config('authentication-log.behind_cdn.http_header_field'));
            } else {
                $ip = $this->request->ip();
            }

            // 创建新的登录失败日志
            $log = $event->user->authentications()->create([
                'ip_address' => $ip,
                'user_agent' => $this->request->userAgent(),
                'login_at' => now(),
                'login_successful' => false,
                // 如果配置文件中启用了地理位置记录，则获取IP地址对应的地理位置
                'location' => config('authentication-log.notifications.new-device.location') ? optional(geoip()->getLocation($ip))->toArray() : null,
            ]);

            // 如果启用了登录失败通知，则发送登录失败通知
            if (config('authentication-log.notifications.failed-login.enabled')) {
                // 获取登录失败通知模板类，如果没有设置则默认为FailedLogin类
                $failedLogin = config('authentication-log.notifications.failed-login.template') ?? FailedLogin::class;
                // 发送登录失败通知
                $event->user->notify(new $failedLogin($log));
            }
        }
    }
}
