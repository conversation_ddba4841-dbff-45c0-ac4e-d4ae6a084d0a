<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Enum representing show types.
 * @package App\Enums\Type
 * @method static self default()
 * @method static self Hidden()
 * @method static self Visible()
 * @method static self Optional()
 */
final class ViewTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'Hidden' => 1,
            'Visible' => 2,
            'Optional' => 3,
        ];
    }

    protected static function labels(): array
    {
        return [
            'default' => '默认状态',
            'Hidden' => '不显示',
            'Visible' => '显示',
            'Optional' => '可选',
        ];
    }

    protected static function colors(): array
    {
        return [
            0 => 'red',
            1 => 'green',
        ];
    }
}
