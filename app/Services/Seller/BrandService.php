<?php

namespace App\Services\Seller;

use App\Http\Resources\Advocate\Brand\BrandResource;
use App\Models\BrandBrace\Brand;
use App\Services\BaseService;

class BrandService extends BaseService
{
    /**
     * @param $brand_uuid
     * @return BrandResource|null
     */
    public static function getBrandUuidToBrand($brand_uuid = null)
    {
        //
        $Brand = Brand::where('platform_id', '=', config('app.platform_id'))->whereUuid($brand_uuid)->first();
        $brand = new BrandResource($Brand);
        //
        return $brand ?? null;
    }
}
