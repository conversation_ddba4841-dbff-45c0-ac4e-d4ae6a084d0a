<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * Class PeriodStateEnum
 * 用于表示周期状态的枚举类。包括订单创建、商家配货、物流运输等多个状态。
 * 该枚举类包含与订单和配送周期相关的不同阶段，例如：
 * - 用户创建订单（'user'）
 * - 商家配货拣货（'merchant'）
 * - 服务商服务（'provider'）
 * - 物流商运输（'transport'）
 * - 用户履约（'fulfillment'）
 * @package App\Enums\State\Period
 * @method static self default() 默认状态
 * @method static self user() 用户创建订单状态
 * @method static self merchant() 商家配货拣货状态
 * @method static self provider() 服务商服务状态
 * @method static self transport() 物流商运输状态
 * @method static self fulfillment() 用户履约状态
 */
final class PeriodStateEnum extends BaseEnum
{
    /**
     * 定义周期状态的数值
     * 该方法为不同的周期状态分配了唯一的数字值。这些数字值将存储在数据库中，便于状态的查询、存储与更新。
     * 返回一个关联数组，其中键是状态名称（如 'user'、'merchant'），
     * 值是该状态对应的数字（如 1、2、3 等）。
     * @return array 状态值数组
     */
    protected static function values(): array
    {
        //
        return [
            'default' => 0,  // 默认状态：初始状态，未设置任何周期状态时使用此值
            'user' => 1,  // 用户创建订单状态：用户创建订单后，系统进入此状态
            'merchant' => 2,  // 商家配货拣货状态：商家准备订单并进行配货拣货时进入此状态
            'provider' => 3,  // 服务商服务状态：服务商开始处理订单并提供服务时进入此状态
            'transport' => 4,  // 物流商运输状态：订单进入运输环节，物流商开始配送
            'fulfillment' => 5,  // 用户履约状态：用户完成付款或接受服务时，订单进入履约状态
        ];
    }

    /**
     * 定义周期状态的标签
     * 该方法返回每个周期状态的中文标签，便于展示给用户或系统管理员。
     * 标签将帮助用户更清晰地理解每个阶段的意义。返回一个关联数组，其中键是状态名称（如 'user'、'merchant'），
     * 值是该状态的中文标签（如 '用户创建订单'、'商家配货拣货' 等）。
     * @return array 状态标签数组
     */
    protected static function labels(): array
    {
        return [
            'default' => '默认',             // 默认状态：表示周期未开始或无特定状态
            'user' => '用户创建订单',       // 用户创建订单：用户提交订单，等待后续处理
            'merchant' => '商家配货拣货',       // 商家配货拣货：商家根据订单进行物品配货和拣货
            'provider' => '服务商服务',         // 服务商服务：服务商处理订单并提供相关服务
            'transport' => '物流商运输',         // 物流商运输：物流商负责订单的运输和配送
            'fulfillment' => '用户履约',           // 用户履约：用户完成付款、收货或相关履约操作
        ];
    }

    /**
     * 获取状态值的名称
     * 该方法返回当前状态的名称，名称是一个字符串值。例如，'user'、'merchant' 等。
     * 可以在程序中使用此名称进行状态的比较和引用。
     * @return string 当前状态的名称
     */
    public function name(): string
    {
        return static::getKey();
    }

    /**
     * 获取状态值对应的数字
     * 该方法返回当前状态的数字值，例如 `user` 状态的值为 `1`，`merchant` 状态的值为 `2`。
     * 数字值通常用于数据库存储或逻辑操作中，便于数据处理和存储。
     * @return int 当前状态的数字值
     */
    public function value(): int
    {
        return static::getValue();
    }

    /**
     * 获取状态标签
     * 该方法返回当前状态的中文标签，用于界面展示。例如，'用户创建订单'、'商家配货拣货' 等。
     * 标签可以帮助用户理解当前周期的具体状态。
     * @return string 当前状态的标签
     */
    public function label(): string
    {
        return static::getLabel();
    }
}
