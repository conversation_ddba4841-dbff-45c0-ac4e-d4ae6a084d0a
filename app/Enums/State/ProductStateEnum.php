<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * Class ProductStateEnum
 * @package App\Enums\State\Product
 * @method static self draft() 草稿
 * @method static self online() 上架中
 * @method static self review() 待审核
 * @method static self release() 待发布
 * @method static self pre() 预售中
 * @method static self restock() 待补货
 * @method static self resale() 转售中
 * @method static self sold() 已售罄
 * @method static self unavailable() 已下架
 * @method static self discontinued() 已停售
 * @method static self hidden() 已隐藏
 * @method static self archived() 已归档
 * @method static self frozen() 冻结
 * @method static self hold() 暂停销售
 */
final class ProductStateEnum extends BaseEnum
{
    protected static function values(): array
    {
        //
        return [
            'draft' => 0,  // 草稿
            'online' => 1, // 上架中
            'review' => 10, // 待审核
            'release' => 20, // 待发布
            'pre' => 30, // 预售中
            'restock' => 40, // 待补货
            'resale' => 50, // 转售中
            'sold' => 60, // 已售罄
            'unavailable' => 70, // 已下架
            'hidden' => 80, // 已隐藏
            'discontinued' => 90, // 已停售
            //
            'frozen' => 100, // 冻结
            'hold' => 150, // 冻结
            'archived' => 200, // 暂停销售
        ];
    }

    protected static function labels(): array
    {
        //
        return [
            'draft' => '草稿',
            'online' => '上架中',
            'review' => '待审核',
            'release' => '待发布',
            'pre' => '预售中',
            'restock' => '待补货',
            'resale' => '转售中',
            'sold' => '已售罄',
            'unavailable' => '已下架',
            'hidden' => '已隐藏',
            'discontinued' => '已停售',
            //
            'frozen' => '冻结',
            'hold' => '暂停销售',
            'archived' => '已归档',
        ];
    }
}
