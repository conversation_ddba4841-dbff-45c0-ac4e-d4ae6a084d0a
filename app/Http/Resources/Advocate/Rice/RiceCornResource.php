<?php

namespace App\Http\Resources\Advocate\Rice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * RiceCornResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class RiceCornResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            //
            'suit_attr' => $suit_attr ?? null,
            'spec_attr' => $spec_attr ?? null,
            //
            'corn_no' => $this->corn_no ?? null,
            'corn_sn' => $this->corn_sn ?? rand(1000, 9999),
            'corn_stock' => $this->corn_stock > 0 ? $this->corn_stock : rand(500, 999),
            'corn_price' => isset($this->corn_price) ? $this->corn_price : 0,
            'corn_status' => $this->corn_status ? 'online' : 'offline',
            //
            'corn_length' => $this->corn_length > 0 ? $this->corn_length : 12.0,
            'corn_width' => $this->corn_width > 0 ? $this->corn_width : 10.0,
            'corn_height' => $this->corn_height > 0 ? $this->corn_height : 3.5,
            'corn_weight' => $this->corn_weight > 0 ? $this->corn_weight : 40.0,
            'corn_volume' => $this->corn_volume > 0 ? $this->corn_volume : 420.0,
            'corn_key' => $this->corn_key ?? null,
            'corn_values' => $this->corn_values ?? null,
            //
            'sku_props' => isset($this->rice_corn_seeds) ? RiceSeedToSpareLineResource::collection($this->rice_corn_seeds()->orderBy('id', 'desc')->get()) : null,
        ]);
    }
}
