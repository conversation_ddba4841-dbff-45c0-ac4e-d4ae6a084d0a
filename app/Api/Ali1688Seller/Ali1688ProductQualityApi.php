<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688ProductQualityApi 类
 * 此类用于处理与阿里巴巴1688平台商品质量相关的API调用，包括根据商品ID获取质量星级建议、
 * 批量获取质量星级优化建议以及获取商品质量星级相关信息等功能。
 */
class Ali1688ProductQualityApi extends Ali1688BaseApi
{
    /**
     * 根据商品ID获取质量星级建议
     * （修改商品场景）根据商品ID获取质量星级建议。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:com.alibaba.cbu.offer.quality.advice.query-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:com.alibaba.cbu.offer.quality.advice.query-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.cbu.offer/com.alibaba.cbu.offer.quality.advice.query-1/7587553
     */
    public static function queryQualityStarAdvice(array $Ali1688AccessToken, int $productId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.cbu.offer/com.alibaba.cbu.offer.quality.advice.query/';

        // 参数验证
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'productID' => $productId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 根据商品ID列表获取质量星级优化建议
     * （修改商品场景）根据商品ID列表获取质量星级优化建议。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:com.alibaba.cbu.offer.quality.advice.queryList-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $productIds 商品ID列表，最多查询100个
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:com.alibaba.cbu.offer.quality.advice.queryList-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.cbu.offer/com.alibaba.cbu.offer.quality.advice.queryList-1/7587553
     */
    public static function queryQualityStarAdviceList(array $Ali1688AccessToken, array $productIds)
    {
        // 定义API路径
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.cbu.offer/com.alibaba.cbu.offer.quality.advice.queryList-1/7587553';

        // 参数验证
        if (empty($productIds)) {
            throw new InvalidArgumentException('productIds 是必填参数，且不能为空。');
        }

        if (count($productIds) > 100) {
            throw new InvalidArgumentException('productIds 数量不能超过100。');
        }

        foreach ($productIds as $id) {
            if (!is_int($id) || $id <= 0) {
                throw new InvalidArgumentException('productIds 中的每个商品ID必须为正整数。');
            }
        }

        // 初始化请求参数
        $params = [
            'productIDs' => implode(',', $productIds),
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取商品质量星级相关信息
     * 获取商品质量星级相关信息，包括质量星级和质量优化建议等。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:product.quality.getInfo-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID
     * @param array $additionalParams 可选参数，包括 catId, dataBody, offerId, scene, bizParam
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:product.quality.getInfo-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.quality.getInfo-1/7587553
     */
    public static function getProductQualityInfo(array $Ali1688AccessToken, int $productId, array $additionalParams = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.quality.getInfo-1/7587553';

        // 参数验证
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'offerId' => $productId,
        ];

        // 处理可选参数
        if (!empty($additionalParams)) {
            if (isset($additionalParams['catId']) && !is_string($additionalParams['catId'])) {
                throw new InvalidArgumentException('catId 必须为字符串。');
            }

            if (isset($additionalParams['dataBody']) && !is_string($additionalParams['dataBody'])) {
                throw new InvalidArgumentException('dataBody 必须为字符串。');
            }

            if (isset($additionalParams['offerId']) && (!is_int($additionalParams['offerId']) || $additionalParams['offerId'] <= 0)) {
                throw new InvalidArgumentException('offerId 必须为正整数。');
            }

            if (isset($additionalParams['scene']) && !is_string($additionalParams['scene'])) {
                throw new InvalidArgumentException('scene 必须为字符串。');
            }

            if (isset($additionalParams['bizParam']) && !is_string($additionalParams['bizParam'])) {
                throw new InvalidArgumentException('bizParam 必须为字符串。');
            }

            $params = array_merge($params, $additionalParams);
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
