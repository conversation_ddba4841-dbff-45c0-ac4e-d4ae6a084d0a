<?php

namespace App\Services\Seller;

use App\Http\Resources\Seller\Order\OrderResource;
use App\Http\Resources\Seller\Order\OrderSpreadResource;
use App\Models\OrderCenter\Order;
use App\Models\OrderCenter\OrderSpread;
use App\Models\ShipmentCenter\ShipmentAddress;
use App\Models\UserCenter\UserPickup;
use App\Services\BaseService;
use App\Services\Waybill\WaybillBirdService;

class FetchService extends BaseService
{
    /**
     * @param $User
     * @return mixed
     */
    public static function getStoreByFetchCountData($Store)
    {
        //
        $fetch_counts = [
            //
            'fetch_work_count' => Order::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', 1)->where('order_type', 2)->where('main_state', '>=', 19)->where('main_state', '<', 21)->where('is_lock', 0)->count() ?? 0,
            'fetch_pack_count' => Order::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', 1)->where('order_type', 2)->where('main_state', '>=', 21)->where('main_state', '<', 26)->where('is_lock', 0)->count() ?? 0,
            'fetch_take_count' => Order::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', 1)->where('order_type', 2)->where('main_state', '>=', 26)->where('main_state', '<', 29)->where('is_lock', 0)->count() ?? 0,
            'fetch_done_count' => Order::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', 1)->where('order_type', 2)->where('main_state', '>=', 29)->where('is_lock', 0)->count() ?? 0,
        ];
        //
        return $fetch_counts;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByFetchWorkList($Store)
    {
        //
        $Order = Order::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', 1)->where('order_type', 2)->where('main_state', '>=', 19)->where('main_state', '<', 21)->where('is_lock', 0)->orderBy('create_time', 'desc')->paginate(50);
        $collection = OrderResource::collection($Order)->resource;
        $orders = $collection->groupBy('user_pickup_id');
        //
        $fetches = [];
        foreach ($orders as $user_pickup_id => $order) {
            //
            $UserPickup = UserPickup::where('id', '=', $user_pickup_id)->first();
            //
            $fetch = [
                'user_pickup_uuid' => $UserPickup->uuid,
                'contact_name' => $UserPickup->contact_name,
                'contact_phone' => $UserPickup->contact_phone,
                'orders' => $order,
            ];
            //
            array_push($fetches, $fetch);
        }
        //
        return $fetches;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByFetchPackList($Store)
    {
        //
        $OrderSpread = OrderSpread::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('main_state', 1)->paginate(50);
        $order_spreads = OrderSpreadResource::collection($OrderSpread)->resource;
        //->where('is_check', 1)->where('order_type', 2)->where('main_state', '>', 20)->where('main_state', '<=', 21)->where('is_lock', 0)->orderBy('create_time', 'desc')
        return $order_spreads;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByFetchTakeList($Store)
    {
        //
        $OrderSpread = OrderSpread::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->paginate(50);
        $fetch_prints = OrderSpreadResource::collection($OrderSpread)->resource;
        //		//->where('main_state', 10)
        //		Paginator::useBootstrap();
        //		$Order        = Order::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', 1)->where('order_type', 2)->where('main_state', '>', 21)->where('main_state', '<=', 26)->where('is_lock', 0)->orderBy('create_time', 'desc')->paginate(50);
        //		$fetch_prints = OrderResource::collection($Order)->resource;
        //
        return $fetch_prints;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByFetchDoneList($Store)
    {
        //
        $OrderSpread = OrderSpread::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('main_state', 100)->paginate(50);
        $fetch_dones = OrderSpreadResource::collection($OrderSpread)->resource;
        //		//
        //		Paginator::useBootstrap();
        //		$Order       = Order::where('platform_id', '=', config('app.platform_id'))->where('store_id', '=', $Store->id)->where('is_check', 1)->where('order_type', 2)->where('main_state', '>=', 26)->where('is_lock', 0)->orderBy('create_time', 'desc')->paginate(50);
        //		$fetch_dones = OrderResource::collection($Order)->resource;
        //
        return $fetch_dones;
    }    // 获取用户信息

    public static function GetOrderFetchCaiNiaoCloudPrint($waybill_uuid, $order_uuids, $shipment_address_uuid)
    {
        //
        $Orders = Order::where('platform_id', '=', config('app.platform_id'))->whereUuid($order_uuids)->get();
        $print_orders = PrintResource::collection($Orders);
        //		dd($shipment_address_uuid);
        //
        $ShipmentAddress = ShipmentAddress::where('platform_id', '=', config('app.platform_id'))->whereUuid($shipment_address_uuid)->first();
        //		dd($ShipmentAddress);
        $waybill_send_address = [
            'province' => $ShipmentAddress->province_name,
            'city' => $ShipmentAddress->city_name,
            'district' => $ShipmentAddress->area_name,
            'town' => $ShipmentAddress->town_name,
            'detail' => $ShipmentAddress->detail_info,
            //
            'mobile' => $ShipmentAddress->mobile,
            'name' => $ShipmentAddress->contact_name,
            'phone' => $ShipmentAddress->phone,
        ];
        //        dd($Orders->logisty);
        //
        $print_orders_ens = json_encode($print_orders);
        $print_orders_infos = json_decode($print_orders_ens, true);
        //
        $waybill_print_response = WaybillBirdService::GetWaybillBirdCloudPrintApply($waybill_uuid, $print_orders_infos, $waybill_send_address);
        //
        $waybill_response = $waybill_print_response['waybill_response'] ?? null;
        $waybill = $waybill_print_response['waybill'] ?? null;
        //
        if (empty($waybill_response->code)) {
            //
            $print_responses = $waybill_response->modules->waybill_cloud_print_response;
            //
            foreach ($print_responses as $print_response) {
                //
                $Order = Order::where('platform_id', '=', config('app.platform_id'))->whereUuid($print_response->object_id)->first();
                //				$Waybill = Waybill::where('platform_id', '=', config('app.platform_id'))->whereUuid($waybill_uuid)->first();
                //
                $OrderFetch = $Order->fetch;
                $OrderFetch->cp_code = $print_response->cp_code ?? null;
                //
                $OrderFetch->sheet_type = $waybill->sheet_type ?? 1;
                $OrderFetch->waybill_id = $waybill->id ?? null;
                $OrderFetch->express_corp_id = $waybill->express_corp_id ?? null;
                //
                $OrderFetch->cp_name = $waybill->cp_name ?? null;
                $OrderFetch->real_cp_code = $print_response->real_cp_code ?? null;
                $OrderFetch->master_express_no = $print_response->waybill_code ?? null;
                $OrderFetch->print_data = json_decode($print_response->print_data, true) ?? null;
                $OrderFetch->update();
                //
                $Order->is_waybill = 1;
                $Order->main_state = 30;
                $Order->client_ip = request()->ip();
                $Order->update();
                //记录数据 $OrderBeforeStatus   = $Order->main_state;
                //OrderNoteService::NoteOrderWorkToHandleFetchGetWaybill($Order, $OrderBeforeStatus);
            }
        }
        //
        return $waybill_response;
    }

    /**
     * @param string $retail_ali1688_retail_id
     * @return mixed
     */
    public static function GetOrderPrintCustomTemplate($Store, $Order, $print_content, $print_describe, $OrderItems, $count_items, $sum_items)
    {
        //
        $title_state = in_array('title_state', $print_content);
        $retail_state = in_array('retail_state', $print_content);
        $shop_state = in_array('shop_state', $print_content);
        $time_state = in_array('time_state', $print_content);
        $no_state = in_array('no_state', $print_content);
        $buyer_message_state = in_array('message_state', $print_content);
        $seller_memo_state = in_array('memo_state', $print_content);
        $pay_state = in_array('pay_state', $print_content);
        $describe_state = in_array('describe_state', $print_content);
        $goods_state = in_array('goods_state', $print_content);
        $count_state = in_array('count_state', $print_content);
        //
        $retail_content = $retail_state ? '|1688' : null;
        $shop_content = $shop_state ? '|' . $Store->loginId : null;
        $time_content = $time_state ? $Order->create_time : null;
        $no_content = $no_state ? $Order->order_no : null;
        $buyer_message_content = $buyer_message_state ? $Order->buyerFeedback : null;
        $seller_memo_content = $seller_memo_state ? $Order->sellerMemo : null;
        $pay_content = $pay_state ? $Order->pay_time : null;
        $count_content = $count_state ? ($count_items ? $count_items . '/' : null) . ($sum_items ?? null) : null;
        //
        $goods_info_content = '';
        foreach ($OrderItems as $name => $quantity) {
            //
            $goods_info_content = $goods_info_content . '【' . $name . '】' . '*' . $quantity . '    ';
        }
        //
        $custom_templates = [
            'data' => array_filter([
                'waybill_info' => $retail_state || $shop_state ? "自" . ($retail_content ?? '') . ($shop_content ?? '') : null,
                'trade_info' => (($title_state && ($time_state || $no_state)) ? "交易信息：" : '') . ($time_state ? $time_content . '    ' : '') . ($no_state ? $no_content : ''),
                'buyer_message' => ($title_state && $buyer_message_state ? "用户留言：" : '') . ($buyer_message_state ? $buyer_message_content . '    ' : ''),
                'seller_memo' => ($title_state && $seller_memo_state ? "商家备注：" : '') . ($seller_memo_state ? $seller_memo_content . '    ' : ''),
                'pay_info' => ($title_state && $pay_state ? "付款时间：" : '') . ($pay_state ? $pay_content . '    ' : ''),
                'goods_info' => ($title_state && $goods_state ? "款式数量：" : '') . ($goods_state ? $goods_info_content . '    ' : ''),
                'pack_describe' => ($title_state && $describe_state ? "包裹描述：" : '') . $print_describe,
                'pack_count' => ($title_state && $count_state ? "包裹统计：" : '') . ($count_state ? $count_content . '    ' : ''),
                'consumer_info' => "   ****************************************************",
            ]),
            //
            'templateURL' => "https://cloudprint.cainiao.com/template/standard/616424/30",
        ];
        //
        return $custom_templates;
    }
}
