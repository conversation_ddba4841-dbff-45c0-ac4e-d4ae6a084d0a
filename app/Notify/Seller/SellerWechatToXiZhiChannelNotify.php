<?php

namespace App\Notify\Seller;

use Guanguans\Notify\XiZhi\Authenticator;
use Guanguans\Notify\XiZhi\Client;
use Guanguans\Notify\XiZhi\Messages\ChannelMessage;
use Guanguans\Notify\XiZhi\Messages\SingleMessage;
use Illuminate\Notifications\Notification;

class SellerWechatToXiZhiChannelNotify
{
    protected $client;

    /**
     * 构造函数，初始化 XiZhi 客户端
     */
    public function __construct()
    {
        // 从配置文件中获取认证令牌
        $authToken = env('EXCEPTION_NOTIFY_XIZHI_TOKEN', 'XZ901760bb8c7940eda1beaa838a2b415f');
        // 创建认证器实例
        $authenticator = new Authenticator($authToken);
        // 使用认证器创建 XiZhi 客户端实例
        $this->client = new Client($authenticator);
    }

    /**
     * 发送给定的通知
     */
    public function send($notifiable, Notification $notification): void
    {
        // 获取通知的 XiZhi 表示形式
        $data = $notification->toXiZhi($notifiable);
        // 判断消息类型并创建相应的消息实例
        if ($data['type'] === 'single') {
            // 创建单条消息实例
            $message = SingleMessage::make($data['message']);
        } else {
            // 创建频道消息实例
            $message = ChannelMessage::make($data['message']);
        }
        // 发送消息
        $this->client->send($message);
    }
}
