<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * ViewTypeEnum 枚举类
 * 这是一个纯枚举类，不包含底层值。
 * 纯枚举用于表示一组固定的、互斥的可能值。
 * 使用示例:
 * - 定义枚举值: case EXAMPLE
 * - 获取枚举值: ViewTypeEnum::EXAMPLE
 * - 获取所有情况: ViewTypeEnum::cases()
 * - 比较枚举值: $value === ViewTypeEnum::EXAMPLE
 */
final class ViewTypeEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    /**
     * 获取枚举值的显示名称
     * @return string 枚举值的人类可读名称
     */
    protected static function values(): array
    {
        return [
            'Default' => 0,
        ];
    }

    /**
     * 获取所有枚举值的状态标签
     * @return array 包含所有枚举值及其标签的数组
     */
    protected static function labels(): array
    {
        //
        return [
            'Default' => '默认状态',
        ];
    }
}
