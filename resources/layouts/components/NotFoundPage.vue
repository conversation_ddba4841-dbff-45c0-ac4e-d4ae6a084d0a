<template>
    <div class="error-page not-found">
        <div class="error-container">
            <div class="error-content">
                <div class="error-illustration">
                    <i class="fas fa-search"></i>
                    <div class="error-code">404</div>
                </div>

                <div class="error-info">
                    <h1 class="error-title">页面未找到</h1>
                    <p class="error-description">
                        抱歉，您访问的页面不存在或已被移除。
                        请检查URL是否正确，或返回首页继续浏览。
                    </p>

                    <div class="error-actions">
                        <el-button type="primary" @click="goHome">
                            <i class="fas fa-home"></i>
                            返回首页
                        </el-button>
                        <el-button @click="goBack">
                            <i class="fas fa-arrow-left"></i>
                            返回上页
                        </el-button>
                    </div>
                </div>
            </div>

            <div class="error-suggestions">
                <h3>您可能需要：</h3>
                <ul>
                    <li>
                        <router-link to="/">
                            <i class="fas fa-home"></i>
                            访问首页
                        </router-link>
                    </li>
                    <li>
                        <router-link to="/login">
                            <i class="fas fa-sign-in-alt"></i>
                            登录系统
                        </router-link>
                    </li>
                    <li>
                        <router-link to="/admin/dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            管理后台
                        </router-link>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'NotFound'
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

.error-page {
    min-height: 100vh;
    background: linear-gradient(
            135deg,
            color.scale($primary-color, $lightness: 100%) 0%,
            color.scale($primary-color, $lightness: 61.0778443114%) 100%
    );
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.error-container {
    max-width: 800px;
    width: 100%;
    background-color: white;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-lg;
    overflow: hidden;
}

.error-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 400px;
}

.error-illustration {
    background: linear-gradient(135deg, $primary-color 0%, color.scale($primary-color, $lightness: -14.8688046647%) 100%);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    position: relative;

    i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.3;
    }

    .error-code {
        font-size: 6rem;
        font-weight: $font-weight-bold;
        line-height: 1;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
}

.error-info {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .error-title {
        font-size: 2rem;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin: 0 0 1rem 0;
    }

    .error-description {
        color: $text-secondary;
        line-height: 1.6;
        margin-bottom: 2rem;
        font-size: 1rem;
    }

    .error-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        .el-button {
            i {
                margin-right: 0.5rem;
            }
        }
    }
}

.error-suggestions {
    padding: 2rem 3rem 3rem;
    border-top: 1px solid $border-light;
    background-color: $light-color;

    h3 {
        font-size: 1.125rem;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin: 0 0 1rem 0;
    }

    ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
            margin-bottom: 0.75rem;

            &:last-child {
                margin-bottom: 0;
            }

            a {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: $text-secondary;
                text-decoration: none;
                transition: color 0.3s ease;

                &:hover {
                    color: $primary-color;
                }

                i {
                    width: 1rem;
                    text-align: center;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .error-content {
        grid-template-columns: 1fr;
    }

    .error-illustration {
        padding: 2rem;

        i {
            font-size: 3rem;
        }

        .error-code {
            font-size: 4rem;
        }
    }

    .error-info {
        padding: 2rem;

        .error-title {
            font-size: 1.5rem;
        }

        .error-actions {
            flex-direction: column;

            .el-button {
                width: 100%;
                justify-content: center;
            }
        }
    }

    .error-suggestions {
        padding: 1.5rem 2rem 2rem;
    }
}

@media (max-width: 480px) {
    .error-page {
        padding: 1rem;
    }

    .error-illustration {
        padding: 1.5rem;
    }

    .error-info {
        padding: 1.5rem;
    }

    .error-suggestions {
        padding: 1rem 1.5rem 1.5rem;
    }
}
</style>
