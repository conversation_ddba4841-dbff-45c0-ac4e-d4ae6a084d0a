# Auth系统增强 - 手机号短信验证码和微信扫码登录注册

**版本**: V2.0  
**日期**: 2025-01-22  
**作者**: WorkHub Core Team  

## 概述

本文档记录了对WorkHub Core项目认证(Auth)系统的进一步增强，在V1.0基础上新增了中国手机号短信验证码登录注册和微信公众号扫码登录注册功能。此次增强旨在提供更符合中国用户习惯的认证方式，提升用户体验和系统的本土化程度。

## 新增功能总览

### 1. 手机号短信验证码认证系统

#### 1.1 核心功能
- **多场景支持**：登录、注册、绑定手机号、密码重置
- **多渠道支持**：阿里云、腾讯云、华为云短信服务
- **安全防护**：频率限制、验证码过期、防暴力破解
- **智能验证**：中国大陆手机号格式验证、运营商号段验证

#### 1.2 主要组件
- **SmsAuthController.php** - 短信认证控制器
- **SmsVerificationRequest.php** - 短信验证请求类
- **SmsService.php** - 短信发送服务
- **SmsCodeService.php** - 验证码管理服务

### 2. 微信公众号扫码认证系统

#### 2.1 核心功能
- **二维码生成**：临时和永久二维码支持
- **扫码状态轮询**：实时查询扫码状态
- **多场景支持**：登录、注册、账号绑定
- **用户信息同步**：自动获取微信用户信息

#### 2.2 主要组件
- **WechatAuthController.php** - 微信认证控制器
- **WechatService.php** - 微信API服务
- **WechatQrCodeService.php** - 二维码管理服务

## 技术架构

### 数据库设计

#### 用户表增强字段
```sql
-- 手机号相关
mobile VARCHAR(20) UNIQUE COMMENT '手机号'
mobile_verified_at TIMESTAMP COMMENT '手机号验证时间'

-- 微信相关
mp_openid VARCHAR(100) UNIQUE COMMENT '微信公众号OpenID'
mini_openid VARCHAR(100) UNIQUE COMMENT '微信小程序OpenID'
unionid VARCHAR(100) UNIQUE COMMENT '微信UnionID'
nickname VARCHAR(100) COMMENT '微信昵称'
head_img_url TEXT COMMENT '微信头像URL'

-- 登录信息
last_login_at TIMESTAMP COMMENT '最后登录时间'
last_login_ip VARCHAR(45) COMMENT '最后登录IP'
status ENUM('active', 'inactive', 'suspended', 'banned') COMMENT '账户状态'
```

#### 新增数据表
1. **sms_codes** - 短信验证码记录表
2. **wechat_users** - 微信用户信息表

### 服务架构

#### 短信服务架构
```
SmsAuthController
    ├── SmsService (短信发送)
    │   ├── 阿里云短信
    │   ├── 腾讯云短信
    │   ├── 华为云短信
    │   └── 模拟短信
    └── SmsCodeService (验证码管理)
        ├── 验证码生成
        ├── 验证码验证
        ├── 频率限制
        └── 缓存管理
```

#### 微信服务架构
```
WechatAuthController
    ├── WechatService (微信API)
    │   ├── 签名验证
    │   ├── 消息解析
    │   ├── 用户信息获取
    │   └── 消息发送
    └── WechatQrCodeService (二维码管理)
        ├── 临时二维码
        ├── 永久二维码
        ├── 图片下载
        └── 状态管理
```

## 安全特性

### 短信安全
1. **频率限制**：每小时最多10次，每分钟最多5次验证
2. **IP限制**：基于IP和手机号的组合限制
3. **验证码安全**：6位数字，5分钟过期，最多5次验证
4. **手机号验证**：严格的中国大陆手机号格式验证

### 微信安全
1. **签名验证**：验证微信服务器请求签名
2. **会话管理**：二维码场景值唯一性和过期管理
3. **状态跟踪**：完整的扫码状态跟踪和日志记录
4. **防重放攻击**：基于时间戳的防重放机制

## 配置说明

### 短信配置 (config/sms.php)
```php
// 默认短信渠道
'default' => env('SMS_DEFAULT_CHANNEL', 'aliyun'),

// 验证码配置
'code_length' => 6,
'code_expires_minutes' => 5,
'resend_interval_seconds' => 60,

// 频率限制
'max_attempts_per_hour' => 10,
'max_code_attempts_per_minute' => 5,
```

### 微信配置 (config/wechat.php)
```php
// 公众号配置
'official_account' => [
    'app_id' => env('WECHAT_OFFICIAL_ACCOUNT_APPID'),
    'app_secret' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET'),
    'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN'),
],

// 二维码配置
'qrcode' => [
    'temp_expire_seconds' => 604800, // 7天
    'auto_download' => false,
],
```

## 环境变量配置

### 短信服务环境变量
```env
# 短信服务配置
SMS_DEFAULT_CHANNEL=aliyun
SMS_CODE_LENGTH=6
SMS_CODE_EXPIRES_MINUTES=5

# 阿里云短信
ALIYUN_SMS_ACCESS_KEY_ID=your_access_key_id
ALIYUN_SMS_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_SMS_SIGN_NAME=WorkHub

# 腾讯云短信
TENCENT_SMS_SECRET_ID=your_secret_id
TENCENT_SMS_SECRET_KEY=your_secret_key
TENCENT_SMS_SDK_APP_ID=your_sdk_app_id
```

### 微信服务环境变量
```env
# 微信公众号配置
WECHAT_OFFICIAL_ACCOUNT_APPID=your_app_id
WECHAT_OFFICIAL_ACCOUNT_SECRET=your_app_secret
WECHAT_OFFICIAL_ACCOUNT_TOKEN=your_token
WECHAT_OFFICIAL_ACCOUNT_AES_KEY=your_aes_key
```

## API接口说明

### 短信验证码接口

#### 发送验证码
```http
POST /sms/send-code
Content-Type: application/json

{
    "mobile": "***********",
    "type": "login"
}
```

#### 验证码登录
```http
POST /sms-login
Content-Type: application/json

{
    "mobile": "***********",
    "code": "123456",
    "remember": false
}
```

### 微信扫码接口

#### 生成二维码
```http
POST /wechat/generate-qrcode
Content-Type: application/json

{
    "type": "login",
    "redirect_url": "/dashboard"
}
```

#### 查询扫码状态
```http
POST /wechat/check-qrcode-status
Content-Type: application/json

{
    "scene_str": "login_1642857600_abc12345"
}
```

## 路由配置

### 新增路由列表
```php
// 手机号认证路由
Route::get('sms-login', [SmsAuthController::class, 'showLoginForm']);
Route::post('sms/send-code', [SmsAuthController::class, 'sendSmsCode']);
Route::post('sms-login', [SmsAuthController::class, 'login']);
Route::post('sms-register', [SmsAuthController::class, 'register']);

// 微信认证路由
Route::get('wechat-login', [WechatAuthController::class, 'showLoginForm']);
Route::post('wechat/generate-qrcode', [WechatAuthController::class, 'generateQrCode']);
Route::post('wechat/check-qrcode-status', [WechatAuthController::class, 'checkQrCodeStatus']);
Route::any('wechat/callback', [WechatAuthController::class, 'handleCallback']);
```

## 部署指南

### 1. 数据库迁移
```bash
php artisan migrate
```

### 2. 配置文件发布
```bash
php artisan config:publish
```

### 3. 缓存清理
```bash
php artisan config:clear
php artisan cache:clear
```

### 4. 队列配置（可选）
```bash
php artisan queue:work
```

## 使用示例

### 短信验证码登录流程
1. 用户输入手机号
2. 点击发送验证码
3. 系统发送6位数字验证码
4. 用户输入验证码
5. 系统验证并登录用户

### 微信扫码登录流程
1. 用户访问微信登录页面
2. 系统生成二维码
3. 用户微信扫码
4. 系统获取用户信息
5. 自动登录或注册用户

## 监控和日志

### 日志记录
- 短信发送成功/失败日志
- 验证码验证日志
- 微信API调用日志
- 用户登录/注册日志

### 监控指标
- 短信发送成功率
- 验证码验证成功率
- 微信扫码成功率
- 用户转化率

## 故障排除

### 常见问题
1. **短信发送失败**：检查短信服务商配置和余额
2. **验证码验证失败**：检查验证码是否过期或已使用
3. **微信回调失败**：检查服务器URL配置和签名验证
4. **二维码过期**：检查二维码生成时间和过期设置

## 版本历史

- **V2.0** (2025-01-22): 新增手机号短信验证码和微信扫码登录注册功能
- **V1.0** (2025-01-22): 基础Auth系统优化和增强

---

**注意**: 本文档记录了Auth系统的主要增强内容。如需了解具体实现细节，请参考相应的源代码文件。
