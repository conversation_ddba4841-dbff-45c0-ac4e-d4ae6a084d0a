<?php

namespace App\Models\PropertyCenter;

use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Database\Factories\PropertyCenter\PropertyFactory;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Property 模型
 * @package App\Models\PropertyCenter
 * @property int $uuid       主键ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @method static Builder|Property query() 开始查询
 * @method static Builder|Property where($column, $operator = null, $value = null) 条件查询
 */
class Property extends Model
{
    /** @use HasFactory<PropertyFactory> */
    use HasFactory;
    use GeneratesUuid;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = '';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array<int, string>
     */
    protected $fillable = [
        //
    ];

    /**
     * 隐藏的属性（不会在数组/JSON中显示）
     * @var array<int, string>
     */
    protected $hidden = [
        //
    ];

    /**
     * 应该被转换成原生类型的属性
     * @var array<string, string>
     */
    protected $casts = [
        // 'options' => 'array',
        // 'is_active' => 'boolean',
        // 'price' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'rices.rice_number' => 10,
            'rices.rice_subject' => 10,
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $riceStatuses = [
            'new_count' => RiceStateEnum::new()->value,
            'category_count' => RiceStateEnum::category()->value,
            'schema_count' => RiceStateEnum::schema()->value,
            'store_count' => RiceStateEnum::store()->value,
            'product_count' => RiceStateEnum::product()->value,
            'word_count' => RiceStateEnum::word()->value,
            'image_count' => RiceStateEnum::image()->value,
            'ready_count' => RiceStateEnum::ready()->value,
            'change_count' => RiceStateEnum::change()->value,
            'progress_count' => RiceStateEnum::progress()->value,
            'spare_count' => RiceStateEnum::spare()->value,
        ];
        //
        //dd($riceStatuses);
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('rice_state', $riceStatuses);
    }

    /**
     * 应该被追加到数组输出的访问器
     * @var array<int, string>
     */
    // protected $appends = ['full_name'];

    /**
     * 日期类型的字段
     * @var array<int, string>
     */
    // protected $dates = [
    //     'published_at',
    // ];

    /**
     * 模型关联方法示例
     */

    /**
     * 一对多关联示例
     * @return HasMany
     */
    // public function items()
    // {
    //     return $this->hasMany(Item::class);
    // }

    /**
     * 一对一关联示例
     * @return HasOne
     */
    // public function profile()
    // {
    //     return $this->hasOne(Profile::class);
    // }

    /**
     * 多对多关联示例
     * @return BelongsToMany
     */
    // public function roles()
    // {
    //     return $this->belongsToMany(Role::class)->withTimestamps()->withPivot('extra_column');
    // }

    /**
     * 反向一对一关联示例
     * @return BelongsTo
     */
    // public function user()
    // {
    //     return $this->belongsTo(User::class);
    // }

    /**
     * 远程一对多关联示例
     * @return HasManyThrough
     */
    // public function comments()
    // {
    //     return $this->hasManyThrough(Comment::class, Post::class);
    // }

    /**
     * 多态一对多关联示例
     * @return MorphMany
     */
    // public function comments()
    // {
    //     return $this->morphMany(Comment::class, 'commentable');
    // }

    /**
     * 访问器和修改器示例
     */

    /**
     * 获取格式化的名称
     * @return Attribute
     */
    // protected function fullName(): Attribute
    // {
    //     return Attribute::make(
    //         get: fn () => $this->first_name . ' ' . $this->last_name,
    //         set: fn (string $value) => [
    //             'first_name' => explode(' ', $value)[0] ?? '',
    //             'last_name' => explode(' ', $value)[1] ?? '',
    //         ],
    //     );
    // }

    /**
     * 本地作用域示例
     * @param Builder $query
     * @return Builder
     */
    // public function scopeActive($query)
    // {
    //     return $query->where('is_active', true);
    // }

    /**
     * 按创建日期排序的本地作用域
     * @param Builder $query
     * @param string $direction
     * @return Builder
     */
    // public function scopeOrderByCreated($query, $direction = 'desc')
    // {
    //     return $query->orderBy('created_at', $direction);
    // }

    /**
     * 模型事件钩子
     */
    // protected static function booted()
    // {
    //     static::creating(function ($model) {
    //         // 创建记录前的操作
    //     });
    //
    //     static::created(function ($model) {
    //         // 创建记录后的操作
    //     });
    //
    //     static::updating(function ($model) {
    //         // 更新记录前的操作
    //     });
    //
    //     static::updated(function ($model) {
    //         // 更新记录后的操作
    //     });
    //
    //     static::deleting(function ($model) {
    //         // 删除记录前的操作
    //     });
    //
    //     static::deleted(function ($model) {
    //         // 删除记录后的操作
    //     });
    // }

    /**
     * 自定义查询方法示例
     * @param mixed $value
     * @return Model|null
     */
    // public static function findBySlug($value)
    // {
    //     return static::where('slug', $value)->first();
    // }


    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
