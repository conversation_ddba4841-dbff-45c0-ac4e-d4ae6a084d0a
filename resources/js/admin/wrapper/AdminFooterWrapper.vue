<template>
    <!-- Admin 底部包装组件 -->
    <BackendFooter :footer-config="adminFooterConfig"/>
</template>

<script>
// 导入全局底部组件
import BackendFooter from '@layouts/BackendLayout/components/BackendFooter.vue'

/**
 * Admin 底部包装组件
 *
 * 功能说明：
 * - 为 Admin 系统提供专用的底部配置
 * - 管理 Admin 特定的版权信息、链接和版本信息
 * - 处理 Admin 特有的底部交互逻辑
 * - 可以添加 Admin 特定的底部功能（如系统状态、快速链接等）
 *
 * 设计原则：
 * - 封装 Admin 特定的底部逻辑
 * - 保持与全局 BackendFooter 的兼容性
 * - 提供灵活的配置和扩展能力
 */
export default {
    name: 'AdminFooterWrapper',
    components: {
        BackendFooter
    },

    /**
     * 组件属性
     */
    props: {
        /**
         * 外部传入的底部配置
         */
        footerConfig: {
            type: Object,
            default: null
        }
    },

    /**
     * 组件数据
     */
    data() {
        return {
            // Admin 系统信息
            adminSystemInfo: {
                name: 'Admin 管理系统',
                version: '2.0.0',
                buildDate: '2024-01-01',
                environment: process.env.NODE_ENV || 'production',
                apiVersion: 'v2.0'
            },

            // Admin 默认底部配置
            defaultAdminFooterConfig: {
                // 版权信息
                copyright: {
                    year: new Date().getFullYear(),
                    company: 'Admin 管理系统',
                    text: '保留所有权利'
                },

                // 版本信息
                version: {
                    current: '2.0.0',
                    showBuildInfo: process.env.NODE_ENV === 'development',
                    buildDate: '2024-01-01'
                },

                // 链接配置
                links: [
                    {
                        text: '帮助文档',
                        url: '/admin/help',
                        external: false,
                        icon: 'fas fa-question-circle'
                    },
                    {
                        text: '技术支持',
                        url: '/admin/support',
                        external: false,
                        icon: 'fas fa-life-ring'
                    },
                    {
                        text: '系统状态',
                        url: '/admin/system/status',
                        external: false,
                        icon: 'fas fa-heartbeat'
                    },
                    {
                        text: '更新日志',
                        url: '/admin/changelog',
                        external: false,
                        icon: 'fas fa-history'
                    }
                ],

                // 显示配置
                showLinks: true,
                showVersion: true,
                showCopyright: true,
                showSystemInfo: process.env.NODE_ENV === 'development'
            }
        }
    },

    /**
     * 计算属性
     */
    computed: {
        /**
         * Admin 底部配置
         * 合并默认配置、系统信息和外部传入的配置
         */
        adminFooterConfig() {
            const baseConfig = {
                ...this.defaultAdminFooterConfig,
                ...this.footerConfig
            }

            // 动态更新系统信息
            baseConfig.copyright.year = new Date().getFullYear()
            baseConfig.copyright.company = this.adminSystemInfo.name
            baseConfig.version.current = this.adminSystemInfo.version
            baseConfig.version.buildDate = this.adminSystemInfo.buildDate
            baseConfig.version.showBuildInfo = this.adminSystemInfo.environment === 'development'

            return baseConfig
        },

        /**
         * 系统运行时间（示例）
         */
        systemUptime() {
            // 这里可以从 API 获取真实的系统运行时间
            const startTime = new Date('2024-01-01')
            const now = new Date()
            const diffTime = Math.abs(now - startTime)
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
            return `${diffDays} 天`
        },

        /**
         * 当前用户信息（示例）
         */
        currentAdminUser() {
            // 这里可以从用户 store 获取真实的用户信息
            return {
                name: 'Admin',
                role: '超级管理员',
                loginTime: new Date().toLocaleString('zh-CN')
            }
        }
    },

    /**
     * 组件方法
     */
    methods: {
        /**
         * 处理 Admin 底部链接点击
         */
        handleAdminFooterLinkClick(link) {
            try {
                // Admin 特定的链接点击处理
                this.logAdminFooterAction(link)

                // 触发链接点击统计
                this.trackAdminFooterUsage(link)

                // 向父组件传递事件
                this.$emit('footer-link-click', link)

            } catch (error) {
                console.error('Admin 底部链接点击处理失败:', error)
            }
        },

        /**
         * 记录 Admin 底部操作日志
         */
        logAdminFooterAction(link) {
            if (this.adminSystemInfo.environment === 'development') {
                console.log('Admin 底部链接点击:', {
                    link: link,
                    user: this.currentAdminUser.name,
                    timestamp: new Date().toISOString(),
                    page: this.$route.path
                })
            }
        },

        /**
         * 跟踪 Admin 底部使用情况
         */
        trackAdminFooterUsage(link) {
            // 这里可以添加底部链接使用统计逻辑
            if (window.AdminAnalytics) {
                window.AdminAnalytics.track('footer_link_click', {
                    link_text: link.text,
                    link_url: link.url,
                    is_external: link.external,
                    current_page: this.$route.path,
                    timestamp: Date.now()
                })
            }
        },

        /**
         * 获取系统状态信息
         */
        async getSystemStatus() {
            try {
                // 这里可以调用 API 获取真实的系统状态
                return {
                    status: 'healthy',
                    uptime: this.systemUptime,
                    version: this.adminSystemInfo.version,
                    environment: this.adminSystemInfo.environment,
                    lastUpdate: this.adminSystemInfo.buildDate
                }
            } catch (error) {
                console.error('获取系统状态失败:', error)
                return {
                    status: 'unknown',
                    uptime: '未知',
                    version: this.adminSystemInfo.version,
                    environment: this.adminSystemInfo.environment
                }
            }
        },

        /**
         * 显示系统信息弹窗
         */
        async showSystemInfo() {
            try {
                const systemStatus = await this.getSystemStatus()

                this.$msgbox({
                    title: 'Admin 系统信息',
                    message: this.createSystemInfoMessage(systemStatus),
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: '确定',
                    type: 'info'
                })
            } catch (error) {
                console.error('显示系统信息失败:', error)
                this.$message.error('获取系统信息失败')
            }
        },

        /**
         * 创建系统信息消息内容
         */
        createSystemInfoMessage(systemStatus) {
            return `
                <div style="text-align: left;">
                    <p><strong>系统名称：</strong>${this.adminSystemInfo.name}</p>
                    <p><strong>系统版本：</strong>${systemStatus.version}</p>
                    <p><strong>运行环境：</strong>${systemStatus.environment}</p>
                    <p><strong>运行时间：</strong>${systemStatus.uptime}</p>
                    <p><strong>系统状态：</strong><span style="color: ${systemStatus.status === 'healthy' ? 'green' : 'red'}">${systemStatus.status === 'healthy' ? '正常' : '异常'}</span></p>
                    <p><strong>构建日期：</strong>${systemStatus.lastUpdate}</p>
                    <p><strong>API 版本：</strong>${this.adminSystemInfo.apiVersion}</p>
                    <p><strong>当前用户：</strong>${this.currentAdminUser.name} (${this.currentAdminUser.role})</p>
                    <p><strong>登录时间：</strong>${this.currentAdminUser.loginTime}</p>
                </div>
            `
        },

        /**
         * 初始化 Admin 底部特有功能
         */
        initAdminFooterFeatures() {
            // 设置 Admin 特有的快捷键
            this.setupAdminFooterShortcuts()

            // 如果是开发环境，添加调试功能
            if (this.adminSystemInfo.environment === 'development') {
                this.initDebugFeatures()
            }
        },

        /**
         * 设置 Admin 底部快捷键
         */
        setupAdminFooterShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl+Shift+I 显示系统信息
                if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                    e.preventDefault()
                    this.showSystemInfo()
                }
            })
        },

        /**
         * 初始化调试功能
         */
        initDebugFeatures() {
            // 在开发环境下添加调试相关的功能
            console.log('Admin 底部调试功能已启用')

            // 可以添加性能监控、错误收集等调试功能
        }
    },

    /**
     * 组件挂载后执行
     */
    mounted() {
        this.initAdminFooterFeatures()
    }
}
</script>

<style lang="scss" scoped>
/**
 * Admin 底部包装组件样式
 *
 * 注意：
 * - 主要样式由 BackendFooter 提供
 * - 这里只添加 Admin 特定的样式扩展
 * - 保持样式的简洁和一致性
 */

/* Admin 特定的底部样式可以在这里添加 */
/* 例如：Admin 链接样式、系统状态指示器样式等 */
</style>
