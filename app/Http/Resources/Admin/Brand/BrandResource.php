<?php

namespace App\Http\Resources\Admin\Brand;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class BrandResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'brand_name' => $this->brand_name ?? null,
            'brand_english_name' => $this->brand_english_name ?? null,
            'brand_description' => $this->brand_description ?? null,
            'value' => $this->brand_name ?? null,
            'sort' => $this->sort ?? null,
        ]);
    }
}
