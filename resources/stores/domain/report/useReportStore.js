import {defineStore} from 'pinia'

/**
 * 报告状态管理
 * 用于管理报告生成与展示的数据状态
 */
export const useReportStore = defineStore('report', {
    state: () => ({
        // 报告列表
        reports: [],

        // 当前报告
        currentReport: null,

        // 报告模板
        templates: [
            {id: 1, name: '销售报告', type: 'sales', description: '包含销售数据和趋势分析'},
            {id: 2, name: '用户报告', type: 'users', description: '用户增长和活跃度分析'},
            {id: 3, name: '财务报告', type: 'finance', description: '收入支出和利润分析'},
            {id: 4, name: '运营报告', type: 'operation', description: '运营指标和效率分析'}
        ],

        // 报告状态
        status: {
            loading: false,
            generating: false,
            error: null
        },

        // 筛选条件
        filters: {
            dateRange: null,
            reportType: 'all',
            status: 'all'
        },

        // 导出状态
        exportStatus: {
            exporting: false,
            progress: 0,
            error: null
        }
    }),

    getters: {
        // 获取已完成的报告
        getCompletedReports: (state) => {
            return state.reports.filter(report => report.status === 'completed')
        },

        // 获取正在生成的报告
        getGeneratingReports: (state) => {
            return state.reports.filter(report => report.status === 'generating')
        },

        // 按类型分组的报告
        getReportsByType: (state) => {
            const grouped = {}
            state.reports.forEach(report => {
                if (!grouped[report.type]) {
                    grouped[report.type] = []
                }
                grouped[report.type].push(report)
            })
            return grouped
        },

        // 获取最近的报告
        getRecentReports: (state) => (limit = 5) => {
            return state.reports.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, limit)
        }
    },

    actions: {
        /**
         * 加载报告列表
         */
        async loadReports() {
            try {
                this.status.loading = true
                this.status.error = null

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟数据
                this.reports = [
                    {
                        id: 1,
                        name: '2024年1月销售报告',
                        type: 'sales',
                        status: 'completed',
                        createdAt: '2024-01-31T23:59:59Z',
                        size: '2.5MB',
                        author: '管理员'
                    },
                    {
                        id: 2,
                        name: '用户增长分析报告',
                        type: 'users',
                        status: 'generating',
                        createdAt: '2024-02-01T10:30:00Z',
                        progress: 75,
                        author: '数据分析师'
                    }
                ]
            } catch (error) {
                this.status.error = error.message
                console.error('加载报告列表失败:', error)
            } finally {
                this.status.loading = false
            }
        },

        /**
         * 生成报告
         */
        async generateReport(reportConfig) {
            try {
                this.status.generating = true
                this.status.error = null

                const newReport = {
                    id: Date.now(),
                    name: reportConfig.name,
                    type: reportConfig.type,
                    status: 'generating',
                    createdAt: new Date().toISOString(),
                    progress: 0,
                    author: '当前用户',
                    config: reportConfig
                }

                this.reports.unshift(newReport)

                // 模拟报告生成过程
                for (let progress = 0; progress <= 100; progress += 10) {
                    await new Promise(resolve => setTimeout(resolve, 200))
                    newReport.progress = progress
                }

                // 完成生成
                newReport.status = 'completed'
                newReport.size = `${(Math.random() * 5 + 1).toFixed(1)}MB`
                newReport.completedAt = new Date().toISOString()

                return newReport
            } catch (error) {
                this.status.error = error.message
                console.error('生成报告失败:', error)
                throw error
            } finally {
                this.status.generating = false
            }
        },

        /**
         * 删除报告
         */
        async deleteReport(reportId) {
            try {
                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 500))

                const index = this.reports.findIndex(report => report.id === reportId)
                if (index > -1) {
                    this.reports.splice(index, 1)
                }
            } catch (error) {
                console.error('删除报告失败:', error)
                throw error
            }
        },

        /**
         * 导出报告
         */
        async exportReport(reportId, format = 'pdf') {
            try {
                this.exportStatus.exporting = true
                this.exportStatus.progress = 0
                this.exportStatus.error = null

                const report = this.reports.find(r => r.id === reportId)
                if (!report) {
                    throw new Error('报告不存在')
                }

                // 模拟导出进度
                for (let progress = 0; progress <= 100; progress += 20) {
                    await new Promise(resolve => setTimeout(resolve, 300))
                    this.exportStatus.progress = progress
                }

                // 模拟下载文件
                const filename = `${report.name}.${format}`
                console.log(`导出完成: ${filename}`)

                return {filename, url: `/downloads/${filename}`}
            } catch (error) {
                this.exportStatus.error = error.message
                console.error('导出报告失败:', error)
                throw error
            } finally {
                this.exportStatus.exporting = false
                this.exportStatus.progress = 0
            }
        },

        /**
         * 预览报告
         */
        async previewReport(reportId) {
            try {
                this.status.loading = true

                const report = this.reports.find(r => r.id === reportId)
                if (!report) {
                    throw new Error('报告不存在')
                }

                // 模拟加载报告内容
                await new Promise(resolve => setTimeout(resolve, 800))

                this.currentReport = {
                    ...report,
                    content: this.generateMockReportContent(report.type)
                }

                return this.currentReport
            } catch (error) {
                console.error('预览报告失败:', error)
                throw error
            } finally {
                this.status.loading = false
            }
        },

        /**
         * 生成模拟报告内容
         */
        generateMockReportContent(type) {
            const contents = {
                sales: {
                    summary: '本月销售额增长15%',
                    charts: ['销售趋势图', '产品销售分布', '区域销售对比'],
                    data: {totalSales: 1000000, growth: 15}
                },
                users: {
                    summary: '用户增长稳定，活跃度提升',
                    charts: ['用户增长图', '活跃用户分析', '用户来源分布'],
                    data: {totalUsers: 50000, activeUsers: 35000}
                },
                finance: {
                    summary: '财务状况良好，利润稳步上升',
                    charts: ['收入支出图', '利润趋势图', '成本分析'],
                    data: {revenue: 2000000, profit: 500000}
                },
                operation: {
                    summary: '运营效率不断优化',
                    charts: ['操作效率图', '响应时间分析', '服务质量评估'],
                    data: {efficiency: 85, responseTime: 200}
                }
            }

            return contents[type] || contents.sales
        },

        /**
         * 设置筛选条件
         */
        setFilters(filters) {
            this.filters = {...this.filters, ...filters}
        },

        /**
         * 清除筛选条件
         */
        clearFilters() {
            this.filters = {
                dateRange: null,
                reportType: 'all',
                status: 'all'
            }
        },

        /**
         * 获取筛选后的报告
         */
        getFilteredReports() {
            let filtered = [...this.reports]

            if (this.filters.reportType !== 'all') {
                filtered = filtered.filter(report => report.type === this.filters.reportType)
            }

            if (this.filters.status !== 'all') {
                filtered = filtered.filter(report => report.status === this.filters.status)
            }

            if (this.filters.dateRange) {
                const {start, end} = this.filters.dateRange
                filtered = filtered.filter(report => {
                    const reportDate = new Date(report.createdAt)
                    return reportDate >= start && reportDate <= end
                })
            }

            return filtered
        },

        /**
         * 清除当前报告
         */
        clearCurrentReport() {
            this.currentReport = null
        },

        /**
         * 重置报告状态
         */
        resetReportState() {
            this.reports = []
            this.currentReport = null
            this.status = {
                loading: false,
                generating: false,
                error: null
            }
            this.exportStatus = {
                exporting: false,
                progress: 0,
                error: null
            }
            this.clearFilters()
        }
    }
}) 