<?php

namespace App\Services\Lesson;

use App\Enums\State\WordStateEnum;
use App\Models\KeywordCenter\Keyword;
use App\Models\RiceCenter\Rice;
use App\Services\BaseService;
use Exception;
use Fukuball\Jieba\Finalseg;
use Fukuball\Jieba\Jieba;
use Fukuball\Jieba\Posseg;
use Illuminate\Support\Facades\Log;
use liliuwei\pscws4\PSCWS4API;

class KeywordService extends BaseService
{
    /**
     * 使用 Jieba 进行分词处理并返回关键词列表
     * @param string $text 输入文本
     * @return array 关键词数组
     */
    public static function SubjectJiebaProcessWord(string $text): array
    {
        //
        try {
            // 设置内存限制为1024M
            ini_set('memory_limit', '1024M');
            //
            Jieba::init();  // 初始化 Jieba
            Finalseg::init();  // 初始化 Finalseg
            Posseg::init();  // 初始化 Posseg
            Jieba::loadUserDict(public_path('dict_word.txt'));  // 加载用户字典
            // 使用 Posseg 进行分词和词性标注
            $words = Posseg::cut($text);
            return self::processJiebaWords($words, 10);  // 返回处理后的关键词
        } catch (Exception $e) {
            // 错误日志记录
            Log::error('Jieba分词处理失败', [
                'text' => $text,
                'error_message' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString(),
            ]);
            return [];
        }
    }

    /**
     * 处理分词结果并创建或获取关键词
     * @param array $words 分词结果
     * @param int $word_frequency 关键词频率
     * @return array 关键词数组
     */
    private static function processJiebaWords(array $words, int $word_frequency): array
    {
        //
        $keywords = [];
        foreach ($words as $word) {
            //
            $wordText = $word['word'] ?: '';
            $wordSpeech = $word['tag'] ?: 'n';
            // 创建或获取关键词
            $keywords[] = self::createOrGetKeyword($wordText, $wordSpeech, $word_frequency);
        }
        //
        return array_filter($keywords);
    }

    /**
     * 创建或获取指定单词的关键词并返回
     * @param string $word 关键词
     * @param string $word_speech 词性
     * @param int $word_frequency 关键词频率
     * @return Keyword|null 关键词对象或 null
     */
    public static function createOrGetKeyword(string $word, string $word_speech = 'n', int $word_frequency = 9): ?Keyword
    {
        //
        // 检查词是否符合条件（排除短词，标点符号，和无效词性）
        if (mb_strlen($word) >= 2 && !self::isInvalidWord($word) && !self::containsInvalidChars($word) && self::isValidWord($word_speech) && !self::containsCustomSymbols($word)) {
            // 查找现有关键词
            $keyword = self::findOrCreateKeyword($word, $word_speech, $word_frequency);
            //
            if ($keyword && in_array($keyword->word_state, [WordStateEnum::default()->value, WordStateEnum::priority()->value])) {
                //  WordStateEnum::priority()->value 状态的关键词可以包含数字，其他状态不能包含数字
                if ($keyword->word_state === WordStateEnum::priority()->value || !self::containsNumber($keyword->word)) {
                    //
                    return $keyword;
                }
            }
        }
        //
        return null;  // 返回 null，表示未找到有效的关键词
    }

    /**
     * 过滤无效的词汇
     * @param string $word 关键词
     * @return bool 是否为无效词汇
     */
    public static function isInvalidWord(string $word): bool
    {
        // 过滤掉单个中文字和两个字母的英文单词
        return self::isSingleChineseCharacter($word) || self::isTwoLetterEnglish($word);
    }

    /**
     * 判断词是否为单个中文字
     * @param string $word 词
     * @return bool 是否为单个中文字符
     */
    public static function isSingleChineseCharacter(string $word): bool
    {
        return preg_match('/[\x{4e00}-\x{9fa5}]/u', $word) && strlen($word) === 1;
    }

    /**
     * 判断词是否为两个字母的英文词
     * @param string $word 词
     * @return bool 是否为两个字母的英文单词
     */
    public static function isTwoLetterEnglish(string $word): bool
    {
        //
        return preg_match('/^[a-zA-Z]{2}$/', $word);
    }

    /**
     * 判断词是否包含标点符号或数字
     * @param string $word 词
     * @return bool 是否包含无效字符（标点符号或数字）
     */
    public static function containsInvalidChars(string $word): bool
    {
        return self::containsPunctuation($word) || self::containsNumber($word);
    }

    /**
     * 判断词是否包含标点符号
     * @param string $word 词
     * @return bool 是否包含标点符号
     */
    public static function containsPunctuation(string $word): bool
    {
        return preg_match('/[[:punct:]]/', $word);
    }

    /**
     * 判断词是否包含数字
     * @param string $word 词
     * @return bool 是否包含数字
     */
    public static function containsNumber(string $word): bool
    {
        return preg_match('/\d/', $word);
    }

    /**
     * 判断词性是否有效
     * @param string $word 关键词
     * @param string $word_speech 词性
     * @return bool 是否有效
     */
    public static function isValidWord(string $word_speech): bool
    {
        //
        $invalid_speeches = ['x', 'u', 'm'];  // 排除一些无效的词性
        return !in_array($word_speech, $invalid_speeches);
    }

    /**
     * 判断词是否包含自定义符号（例如☆和★）
     * @param string $word
     * @return bool 是否包含自定义符号
     */
    public static function containsCustomSymbols(string $word): bool
    {
        return preg_match('/[☆★（]/', $word); // 检查是否包含 ☆ 或 ★
    }

    /**
     * 查找或创建关键词
     * @param string $word 关键词
     * @param string $word_speech 词性
     * @param int $word_frequency 关键词频率
     * @return Keyword|null 关键词对象或 null
     */
    private static function findOrCreateKeyword(string $word, string $word_speech, int $word_frequency): ?Keyword
    {
        // 优化：先精确查找，再使用模糊匹配
        $keyword = Keyword::where('word', $word)->first();
        //
        if (!$keyword) {
            //
            $keyword = Keyword::where('word', 'like', '%' . $word . '%')->first();
        }
        if (!$keyword) {
            // 创建新的关键词
            $keyword = Keyword::firstOrCreate(
                ['word' => $word],
                [
                    'word_no' => BaseService::BaseGetNo(),  // 创建关键词的编号
                    'word_speech' => $word_speech,               // 设置关键词的词性
                    'word_frequency' => $word_frequency,            // 设置关键词的频率
                ]
            );
        }
        //
        //
        return $keyword;
    }

    /**
     * 使用 PSCWS4 进行分词处理并返回关键词列表
     * @param string $text 输入文本
     * @return array 关键词数组
     */
    public static function SubjectPSCWS4APIProcessWord(string $text): array
    {
        try {
            // 处理文本并获取初步的分词结果
            $words = self::splitCompoundWord($text);
            // 进一步处理每个分词结果并返回关键词
            return self::processPSCWS4Words($words, 9);  // 返回处理后的关键词
        } catch (Exception $e) {
            // 错误日志记录
            Log::error('PSCWS4分词处理失败', [
                'text' => $text,
                'error_message' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString(),
            ]);
            return [];
        }
    }

    /**
     * 使用 PSCWS4 API 对复合词进行分词
     * @param string $text
     * @return array
     */
    private static function splitCompoundWord(string $text): array
    {
        //
        try {
            // 初始化 PSCWS4 分词API
            $participle = new PSCWS4API();
            // 使用 PSCWS4 对词语进行分词
            $segmentedWords = $participle->PSCWS4($text);
            // 如果分词失败，返回空数组
            if (empty($segmentedWords)) {
                //
                $segmentedWords = [$text];  // 如果没有分词结果，则返回原始文本作为一个词
            }
            // Assuming $segmentedWords contains the nested arrays
            $flattenedArray = [];
            foreach ($segmentedWords as $segment) {
                if (is_array($segment)) {
                    $flattenedArray = array_merge($flattenedArray, $segment);
                } else {
                    // If the element is a string (not an array), you can just add it directly
                    $flattenedArray[] = $segment;
                }
            }
            // 提取分词后的单词部分
            return $flattenedArray;
        } catch (Exception $e) {
            // 捕获错误并记录
            Log::error('PSCWS4 分词失败', [
                'text' => $text,
                'error_message' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString(),
            ]);
            return [$text];  // 如果发生错误，返回原始词语
        }
    }

    /**
     * 处理分词结果并创建或获取关键词
     * @param array $words 分词结果
     * @param int $word_frequency 关键词频率
     * @return array 关键词数组
     */
    private static function processPSCWS4Words(array $words, int $word_frequency): array
    {
        $keywords = [];
        // 遍历分词结果，进行再次分词处理
        $allWords = [];
        foreach ($words as $word) {
            // 使用 PSCWS4 对每个分词进行再次分割
            $splitWords = self::splitCompoundWord($word);
            $allWords = array_merge($allWords, $splitWords);
        }
        //
        // 去除重复的分词结果（避免重复处理）
        $uniqueWords = array_unique($allWords);
        // 处理每个分词生成关键词
        foreach ($uniqueWords as $segmentedWord) {
            // 创建或获取关键词
            if (!empty($segmentedWord)) {
                //
                $keywords[] = self::createOrGetKeyword($segmentedWord, 'n', $word_frequency);
            }
        }
        //
        return array_filter($keywords);
    }

    /**
     * 批量更新 rice 与 keywords 的关联关系
     * @param       $rice     Rice 对象
     * @param array $keywords 关键词数组
     */
    public static function updateKeywordsForRice($rice, array $keywords): void
    {
        //
        if (count($keywords) > 0) {
            try {
                // 批量更新关键词与 rice 的关联
                $rice->keywords()->syncWithoutDetaching(array_column($keywords, 'id'));
            } catch (Exception $e) {
                // 记录日志，捕获异常
                Log::error('批量更新关键词关联失败', [
                    'rice_id' => $rice->id,
                    'error_message' => $e->getMessage(),
                    'stack_trace' => $e->getTraceAsString()
                ]);
            }
        }
    }

    /**
     * 判断词是否为英文字母或英文单词
     * @param string $word
     * @return bool 是否为英文字母或英文单词
     */
    public static function containsEnglishWord(string $word): bool
    {
        return preg_match('/^[a-zA-Z]+$/', $word); // 检查是否仅由英文字母组成
    }
}
