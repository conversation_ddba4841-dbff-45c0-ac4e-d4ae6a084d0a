<?php

namespace App\Http\Resources\Admin\Waybill;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class WaybillServiceResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
        ]);
    }
}
