<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;

/**
 * Class IsOfflineEnum
 * @package App\Enums\Is
 * @method static self offline() 下线
 * @method static self online() 上线
 * @method static self part() 部分上线
 */
final class IsOfflineEnum extends BaseEnum
{
    protected static function values(): array
    {
        //
        return [
            'offline' => 0,
            'online' => 1,
            'part' => 2,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'offline' => '下线',
            'online' => '上线',
            'part' => '部分上线',
        ];
    }
}
