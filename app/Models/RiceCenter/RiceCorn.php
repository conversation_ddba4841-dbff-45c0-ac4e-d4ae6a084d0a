<?php

namespace App\Models\RiceCenter;

use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Database\Factories\RiceCenter\RiceCornFactory;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * RiceCorn 模型
 * @package App\Models\RiceCenter
 * @property int $uuid       主键ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @method static Builder|RiceCorn query() 开始查询
 * @method static Builder|RiceCorn where($column, $operator = null, $value = null) 条件查询
 */
class RiceCorn extends Model
{
    /** @use HasFactory<RiceCornFactory> */
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'rice_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array<int, string>
     */
    protected $fillable = [
        //
    ];

    /**
     * 隐藏的属性（不会在数组/JSON中显示）
     * @var array<int, string>
     */
    protected $hidden = [
        //
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 应该被转换成原生类型的属性
     * @var array<string, string>
     */
    protected $casts = [
        'corn_values' => 'array',
        'corn_props' => 'array',
        //
        'corn_price' => 'decimal:2',
        'corn_length' => 'decimal:2',
        'corn_width' => 'decimal:2',
        'corn_height' => 'decimal:2',
        'corn_weight' => 'decimal:2',
        'corn_volume' => 'decimal:2',
        // 'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * @return BelongsToMany
     */
    public function rice_corn_seeds()
    {
        //
        return $this->belongsToMany(RiceSeed::class, 'rice_center.rice_corn_seeds'); // 定义多对多关系
    }

    /**
     * 应该被追加到数组输出的访问器
     * @var array<int, string>
     */
    // protected $appends = ['full_name'];

    /**
     * 日期类型的字段
     * @var array<int, string>
     */
    // protected $dates = [
    //     'published_at',
    // ];

    /**
     * 模型关联方法示例
     */

    /**
     * 一对多关联示例
     * @return HasMany
     */
    // public function items()
    // {
    //     return $this->hasMany(Item::class);
    // }

    /**
     * 一对一关联示例
     * @return HasOne
     */
    // public function profile()
    // {
    //     return $this->hasOne(Profile::class);
    // }

    /**
     * 多对多关联示例
     * @return BelongsToMany
     */
    // public function roles()
    // {
    //     return $this->belongsToMany(Role::class)->withTimestamps()->withPivot('extra_column');
    // }

    /**
     * 反向一对一关联示例
     * @return BelongsTo
     */
    // public function user()
    // {
    //     return $this->belongsTo(User::class);
    // }

    /**
     * 远程一对多关联示例
     * @return HasManyThrough
     */
    // public function comments()
    // {
    //     return $this->hasManyThrough(Comment::class, Post::class);
    // }

    /**
     * 多态一对多关联示例
     * @return MorphMany
     */
    // public function comments()
    // {
    //     return $this->morphMany(Comment::class, 'commentable');
    // }

    /**
     * 访问器和修改器示例
     */

    /**
     * 获取格式化的名称
     * @return Attribute
     */
    // protected function fullName(): Attribute
    // {
    //     return Attribute::make(
    //         get: fn () => $this->first_name . ' ' . $this->last_name,
    //         set: fn (string $value) => [
    //             'first_name' => explode(' ', $value)[0] ?? '',
    //             'last_name' => explode(' ', $value)[1] ?? '',
    //         ],
    //     );
    // }

    /**
     * 本地作用域示例
     * @param Builder $query
     * @return Builder
     */
    // public function scopeActive($query)
    // {
    //     return $query->where('is_active', true);
    // }

    /**
     * 按创建日期排序的本地作用域
     * @param Builder $query
     * @param string $direction
     * @return Builder
     */
    // public function scopeOrderByCreated($query, $direction = 'desc')
    // {
    //     return $query->orderBy('created_at', $direction);
    // }

    /**
     * 模型事件钩子
     */
    // protected static function booted()
    // {
    //     static::creating(function ($model) {
    //         // 创建记录前的操作
    //     });
    //
    //     static::created(function ($model) {
    //         // 创建记录后的操作
    //     });
    //
    //     static::updating(function ($model) {
    //         // 更新记录前的操作
    //     });
    //
    //     static::updated(function ($model) {
    //         // 更新记录后的操作
    //     });
    //
    //     static::deleting(function ($model) {
    //         // 删除记录前的操作
    //     });
    //
    //     static::deleted(function ($model) {
    //         // 删除记录后的操作
    //     });
    // }

    /**
     * 自定义查询方法示例
     * @param mixed $value
     * @return Model|null
     */
    // public static function findBySlug($value)
    // {
    //     return static::where('slug', $value)->first();
    // }


    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }

}
