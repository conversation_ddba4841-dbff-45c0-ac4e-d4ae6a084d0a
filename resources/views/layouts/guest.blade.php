<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
      class="{{ $htmlClass ?? '' }}"
      data-theme="{{ $theme ?? 'light' }}"
      data-env="{{ config('app.env') }}">
{{-- 企业级访客页面头部组件 - 完整 SEO、Meta 标签、资源文件优化，增强响应式和无障碍支持 --}}
<head>
    {{-- 基础 Meta 标签 - 企业级移动端支持 --}}
    <meta charset="utf-8">
    <meta name="viewport"
          content="width=device-width, initial-scale=1, minimum-scale=0.5, maximum-scale=5, user-scalable=yes, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=yes, email=yes, address=yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ config('app.name', 'WorkHub') }}">
    <meta name="application-name" content="{{ config('app.name', 'WorkHub') }}">
    <meta name="theme-color" content="#5247ef">
    <meta name="msapplication-TileColor" content="#5247ef">
    <meta name="msapplication-navbutton-color" content="#5247ef">

    {{-- 安全和隐私 Meta 标签 --}}
    <meta name="referrer" content="strict-origin-when-cross-origin">
    @if(app()->environment('production'))
        <meta http-equiv="Content-Security-Policy"
              content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.bunny.net https://cdnjs.cloudflare.com; font-src 'self' https://fonts.bunny.net; img-src 'self' data: https:; connect-src 'self' https:;">
    @else
        {{-- 开发环境：允许 Vite 开发服务器和 CDN 资源 --}}
        <meta http-equiv="Content-Security-Policy"
              content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.tailwindcss.com http://localhost:* http://127.0.0.1:* http://[::1]:*; style-src 'self' 'unsafe-inline' https://fonts.bunny.net https://cdnjs.cloudflare.com https://cdn.tailwindcss.com http://localhost:* http://127.0.0.1:* http://[::1]:*; font-src 'self' https://fonts.bunny.net; img-src 'self' data: https:; connect-src 'self' https: ws://localhost:* ws://127.0.0.1:* ws://[::1]:* http://localhost:* http://127.0.0.1:* http://[::1]:*;">
    @endif

    {{-- CSRF Token 配置 --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- 企业级动态页面标题系统 --}}
    <title>
        @if(isset($title))
            {{ $title }} - {{ config('app.name', 'WorkHub') }}
        @elseif(isset($pageTitle))
            {{ $pageTitle }} - {{ config('app.name', 'WorkHub') }}
        @else
            {{ config('app.name', 'WorkHub') }} - 现代化企业工作协作平台
        @endif
    </title>

    {{-- 企业级 SEO Meta 标签系统 --}}
    <meta name="description"
          content="{{ $description ?? $seoDescription ?? '现代化的企业工作协作平台，提供安全的用户认证和高效的团队协作解决方案' }}">
    <meta name="keywords"
          content="{{ $keywords ?? $seoKeywords ?? '企业登录,用户认证,工作平台,团队协作,安全登录,企业办公' }}">
    <meta name="author" content="{{ $author ?? config('app.company.name', config('app.name', 'WorkHub')) }}">
    <meta name="publisher" content="{{ config('app.company.name', config('app.name', 'WorkHub')) }}">
    <meta name="robots" content="{{ $robots ?? ($noIndex ?? false ? 'noindex,nofollow' : 'index,follow') }}">
    <meta name="googlebot" content="{{ $googlebot ?? 'index,follow,snippet,archive' }}">
    <meta name="bingbot" content="{{ $bingbot ?? 'index,follow,snippet' }}">

    {{-- 企业级 Open Graph 标签系统 (社交媒体分享优化) --}}
    <meta property="og:title" content="{{ $ogTitle ?? ($title ?? config('app.name')) }}">
    <meta property="og:description" content="{{ $ogDescription ?? ($description ?? '现代化的企业工作协作平台') }}">
    <meta property="og:type" content="{{ $ogType ?? 'website' }}">
    <meta property="og:url" content="{{ $ogUrl ?? request()->url() }}">
    <meta property="og:image" content="{{ $ogImage ?? asset('images/og-default.jpg') }}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="{{ $ogImageAlt ?? config('app.name') . ' - 企业工作协作平台' }}">
    <meta property="og:site_name" content="{{ config('app.name', 'WorkHub') }}">
    <meta property="og:locale" content="zh_CN">
    <meta property="og:locale:alternate" content="en_US">

    {{-- 企业级 Twitter Card 标签系统 --}}
    <meta name="twitter:card" content="{{ $twitterCard ?? 'summary_large_image' }}">
    <meta name="twitter:site" content="{{ config('app.social.twitter', '@WorkHub') }}">
    <meta name="twitter:creator" content="{{ $twitterCreator ?? config('app.social.twitter', '@WorkHub') }}">
    <meta name="twitter:title" content="{{ $twitterTitle ?? ($title ?? config('app.name')) }}">
    <meta name="twitter:description"
          content="{{ $twitterDescription ?? ($description ?? '现代化的企业工作协作平台') }}">
    <meta name="twitter:image" content="{{ $twitterImage ?? asset('images/twitter-default.jpg') }}">
    <meta name="twitter:image:alt" content="{{ $twitterImageAlt ?? config('app.name') . ' - 企业工作协作平台' }}">

    {{-- 企业级网站图标系统 --}}
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicon-16x16.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/apple-touch-icon.png') }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ asset('images/apple-touch-icon-152x152.png') }}">
    <link rel="apple-touch-icon" sizes="144x144" href="{{ asset('images/apple-touch-icon-144x144.png') }}">
    <link rel="apple-touch-icon" sizes="120x120" href="{{ asset('images/apple-touch-icon-120x120.png') }}">
    <link rel="apple-touch-icon" sizes="114x114" href="{{ asset('images/apple-touch-icon-114x114.png') }}">
    <link rel="apple-touch-icon" sizes="76x76" href="{{ asset('images/apple-touch-icon-76x76.png') }}">
    <link rel="apple-touch-icon" sizes="72x72" href="{{ asset('images/apple-touch-icon-72x72.png') }}">
    <link rel="apple-touch-icon" sizes="60x60" href="{{ asset('images/apple-touch-icon-60x60.png') }}">
    <link rel="apple-touch-icon" sizes="57x57" href="{{ asset('images/apple-touch-icon-57x57.png') }}">
    <link rel="manifest" href="{{ asset('site.webmanifest') }}">
    <link rel="mask-icon" href="{{ asset('images/safari-pinned-tab.svg') }}" color="#5247ef">

    {{-- 企业级性能优化 - DNS 预解析和资源预加载 --}}
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//{{ parse_url(config('app.url'), PHP_URL_HOST) }}">
    <link rel="preconnect" href="https://fonts.bunny.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    {{-- 企业级字体系统 - 性能优化加载 --}}
    <link rel="preload" href="https://fonts.bunny.net/css?family=figtree:400,500,600,700&display=swap" as="style"
          onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://fonts.bunny.net/css?family=figtree:400,500,600,700&display=swap">
    </noscript>

    {{-- 企业级图标库 - Font Awesome 6.4.0 --}}
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style"
          onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
              integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
              crossorigin="anonymous"
              referrerpolicy="no-referrer">
    </noscript>

    {{-- 企业级样式系统 - 多重加载策略 --}}
    @if(app()->environment('production'))
        {{-- 生产环境：优先使用 Vite 编译的样式，使用全局样式系统 --}}
        @vite(['resources/css/variables.scss','resources/css/base.scss','resources/css/global.scss','resources/css/app.scss','resources/css/layouts.scss','resources/css/client-layout.scss'])
    @else
        {{-- 开发环境：检查 Vite 是否可用，否则使用备用方案 --}}
        @php
            $viteManifestExists = file_exists(public_path('build/manifest.json'));
            $viteDevServerRunning = false;

            // 简单检查 Vite 开发服务器是否可能在运行
            if (!$viteManifestExists && function_exists('fsockopen')) {
                $connection = @fsockopen('127.0.0.1', 5173, $errno, $errstr, 1);
                if ($connection) {
                    $viteDevServerRunning = true;
                    fclose($connection);
                }
            }
        @endphp

        @if($viteManifestExists || $viteDevServerRunning)
            @vite(['resources/css/variables.scss','resources/css/base.scss','resources/css/global.scss','resources/css/app.scss','resources/css/layouts.scss','resources/css/client-layout.scss'])
        @else
            {{-- Vite 开发服务器未运行时的备用方案 --}}
            <script src="https://cdn.tailwindcss.com"></script>
            <script>
                tailwind.config = {
                    theme: {
                        extend: {
                            colors: {
                                primary: {
                                    50: '#f0f0ff',
                                    100: '#e6e3ff',
                                    200: '#d0ccff',
                                    300: '#b3adff',
                                    400: '#9489ff',
                                    500: '#5247ef',
                                    600: '#4a3dd6',
                                    700: '#3d32b8',
                                    800: '#322899',
                                    900: '#2a217a',
                                    950: '#1a1454'
                                },
                                secondary: {
                                    500: '#5247ef'
                                },
                                success: {
                                    500: '#04c717'
                                },
                                warning: {
                                    500: '#FF5000'
                                },
                                danger: {
                                    500: '#FF0036'
                                }
                            },
                            maxWidth: {
                                '1760': '1760px',
                                'container': '1760px'
                            },
                            minWidth: {
                                '640': '640px',
                                'mobile': '640px'
                            },
                            screens: {
                                'xs': '640px',
                                'sm': '768px',
                                'md': '1024px',
                                'lg': '1200px',
                                'xl': '1440px',
                                '2xl': '1760px'
                            }
                        }
                    }
                }
            </script>
            <style>
                /* 备用样式 - 确保基本布局可用 */
                @import url('https://fonts.bunny.net/css?family=figtree:400,500,600,700');

                .container-1760 {
                    max-width: 1760px !important;
                    margin: 0 auto !important;
                    padding-left: 1rem !important;
                    padding-right: 1rem !important;
                }

                .container-mobile {
                    min-width: 640px !important;
                }

                .no-focus-outline:focus,
                .no-focus-outline:active {
                    outline: none !important;
                    box-shadow: none !important;
                }
            </style>
        @endif
    @endif

    {{-- 企业级访客页面基础样式 - 确保基本样式可用 --}}
    <style>
        /* 企业级访客页面基础样式 - 防止样式丢失，使用全局样式变量 */
        :root {
            --color-primary: #5247ef;
            --color-secondary: #1664FF;
            --color-success: #04c717;
            --color-warning: #FF5000;
            --color-danger: #FF0036;
            --color-gray: #97a6ba;
            --font-size-base: 12px;
            --container-max-width: 1760px;
            --container-min-width: 640px;
            --guest-card-max-width: 28rem;
            --guest-card-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --transition-none: none;
        }

        /* 基础重置和企业级字体 */
        * {
            box-sizing: border-box;
        }

        html {
            font-size: var(--font-size-base);
        }

        body {
            font-family: 'Figtree', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: #111827;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-width: var(--container-min-width);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 企业级访客容器 */
        .guest-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 1.5rem 1rem;
            position: relative;
        }

        /* 企业级 Logo 容器 */
        .guest-logo-container {
            margin-bottom: 2rem;
            text-align: center;
        }

        .guest-logo-link {
            display: inline-block;
            transition: var(--transition-none);
            text-decoration: none;
        }

        .guest-logo-link:focus {
            outline: 2px solid var(--color-primary);
            outline-offset: 4px;
            border-radius: 0.5rem;
        }

        /* 企业级卡片样式 */
        .guest-card {
            width: 100%;
            max-width: var(--guest-card-max-width);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            box-shadow: var(--guest-card-shadow);
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .guest-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
        }

        /* 无障碍支持 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .focus\:not-sr-only:focus {
            position: static;
            width: auto;
            height: auto;
            padding: inherit;
            margin: inherit;
            overflow: visible;
            clip: auto;
            white-space: normal;
        }

        /* 企业级无焦点轮廓 */
        .no-focus-outline:focus,
        .no-focus-outline:active {
            outline: none !important;
            box-shadow: none !important;
            border: none !important;
        }

        /* 基础布局工具类 */
        .flex {
            display: flex;
        }

        .flex-col {
            display: flex;
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-center {
            justify-content: center;
        }

        .text-center {
            text-align: center;
        }

        .w-full {
            width: 100%;
        }

        .h-20 {
            height: 5rem;
        }

        .w-20 {
            width: 5rem;
        }

        .min-h-screen {
            min-height: 100vh;
        }

        .max-w-md {
            max-width: 28rem;
        }

        .mt-6 {
            margin-top: 1.5rem;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .mt-2 {
            margin-top: 0.5rem;
        }

        .mt-1 {
            margin-top: 0.25rem;
        }

        .mt-8 {
            margin-top: 2rem;
        }

        .mb-8 {
            margin-bottom: 2rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mr-2 {
            margin-right: 0.5rem;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .py-4 {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .py-3 {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }

        .py-2 {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }

        .p-8 {
            padding: 2rem;
        }

        .bg-white {
            background-color: #ffffff;
        }

        .text-gray-500 {
            color: #6b7280;
        }

        .text-gray-600 {
            color: #4b5563;
        }

        .text-gray-900 {
            color: #111827;
        }

        .text-white {
            color: #ffffff;
        }

        .text-xl {
            font-size: 1.25rem;
            line-height: 1.75rem;
        }

        .text-2xl {
            font-size: 1.5rem;
            line-height: 2rem;
        }

        .text-sm {
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        .font-semibold {
            font-weight: 600;
        }

        .font-bold {
            font-weight: 700;
        }

        .shadow-md {
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        .rounded-lg {
            border-radius: 0.5rem;
        }

        .rounded-md {
            border-radius: 0.375rem;
        }

        .rounded-full {
            border-radius: 9999px;
        }

        .overflow-hidden {
            overflow: hidden;
        }

        .antialiased {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .font-sans {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
        }

        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        .space-x-4 > * + * {
            margin-left: 1rem;
        }

        .border {
            border-width: 1px;
        }

        .border-green-200 {
            border-color: #bbf7d0;
        }

        .border-red-200 {
            border-color: #fecaca;
        }

        .border-yellow-200 {
            border-color: #fde68a;
        }

        .border-blue-200 {
            border-color: #bfdbfe;
        }

        .bg-green-50 {
            background-color: #f0fdf4;
        }

        .bg-red-50 {
            background-color: #fef2f2;
        }

        .bg-yellow-50 {
            background-color: #fffbeb;
        }

        .bg-blue-50 {
            background-color: #eff6ff;
        }

        .text-green-800 {
            color: #166534;
        }

        .text-red-800 {
            color: #991b1b;
        }

        .text-yellow-800 {
            color: #92400e;
        }

        .text-blue-800 {
            color: #1e40af;
        }

        .text-opacity-80 {
            --tw-text-opacity: 0.8;
        }

        .text-opacity-70 {
            --tw-text-opacity: 0.7;
        }

        .bg-opacity-20 {
            --tw-bg-opacity: 0.2;
        }

        .bg-opacity-90 {
            --tw-bg-opacity: 0.9;
        }

        .fixed {
            position: fixed;
        }

        .absolute {
            position: absolute;
        }

        .relative {
            position: relative;
        }

        .inset-0 {
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        .top-0 {
            top: 0;
        }

        .top-4 {
            top: 1rem;
        }

        .left-0 {
            left: 0;
        }

        .left-4 {
            left: 1rem;
        }

        .right-0 {
            right: 0;
        }

        .right-4 {
            right: 1rem;
        }

        .z-50 {
            z-index: 50;
        }

        .z-9999 {
            z-index: 9999;
        }

        .transition-opacity {
            transition-property: opacity;
        }

        .transition-none {
            transition: none;
        }

        .duration-300 {
            transition-duration: 300ms;
        }

        .hover\:text-white:hover {
            color: #ffffff;
        }

        /* 响应式设计 */
        @media (min-width: 640px) {
            .sm\:justify-center {
                justify-content: center;
            }

            .sm\:pt-0 {
                padding-top: 0;
            }

            .sm\:max-w-md {
                max-width: 28rem;
            }

            .sm\:rounded-lg {
                border-radius: 0.5rem;
            }
        }

        /* 企业级动画效果 */
        .guest-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 加载指示器 */
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--color-primary);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* 通知样式 */
        .alert {
            border-radius: 0.375rem;
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
        }
    </style>

    {{-- 页面特定样式栈 --}}
    @stack('styles')

    {{-- 企业级 JavaScript 预加载配置 --}}
    @stack('head-scripts')

    {{-- 企业级结构化数据系统 (JSON-LD SEO 优化) --}}
    @if(isset($structuredData))
        <script type="application/ld+json">
            {!! json_encode($structuredData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
        </script>
    @endif

    {{-- 默认企业级结构化数据 --}}
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "{{ $title ?? '用户登录' }} - {{ config('app.name', 'WorkHub') }}",
            "url": "{{ request()->url() }}",
            "description": "{{ $description ?? '安全的企业级用户认证系统' }}",
            "isPartOf": {
                "@type": "WebSite",
                "name": "{{ config('app.name', 'WorkHub') }}",
                "url": "{{ config('app.url') }}"
            },
            "provider": {
                "@type": "Organization",
                "name": "{{ config('app.name', 'WorkHub') }}",
                "url": "{{ config('app.url') }}"
            }
        }
    </script>

    {{-- 自定义 Meta 标签栈 --}}
    @stack('meta')

    {{-- 企业级无障碍支持声明 --}}
    <meta name="accessibility" content="WCAG 2.1 AA compliant">
    <meta name="color-scheme" content="light dark">

    {{-- 样式加载检测脚本 --}}
    <script>
        // 检测样式是否正确加载
        document.addEventListener('DOMContentLoaded', function () {
            // 检查 Tailwind CSS 是否加载
            const testElement = document.createElement('div');
            testElement.className = 'flex';
            testElement.style.visibility = 'hidden';
            testElement.style.position = 'absolute';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const isFlexLoaded = computedStyle.display === 'flex';

            document.body.removeChild(testElement);

            if (!isFlexLoaded && window.console) {
                console.warn('样式可能未正确加载。请运行 "npm run dev" 启动 Vite 开发服务器。');

                // 显示样式加载提示
                const notice = document.createElement('div');
                notice.innerHTML = `
                    <div style="position: fixed; top: 0; left: 0; right: 0; background: #f59e0b; color: white; padding: 8px; text-align: center; z-index: 9999; font-size: 14px;">
                        ⚠️ 样式未完全加载。请运行 <code style="background: rgba(0,0,0,0.2); padding: 2px 4px; border-radius: 3px;">npm run dev</code> 启动开发服务器。
                        <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; margin-left: 10px; cursor: pointer;">✕</button>
                    </div>
                `;
                document.body.appendChild(notice);

                // 5秒后自动隐藏
                setTimeout(() => {
                    if (notice.parentNode) {
                        notice.remove();
                    }
                }, 5000);
            }
        });
    </script>
</head>

{{-- 企业级访客页面主体 - 完整响应式优化，支持640px+小屏幕，增强无障碍和性能，使用全局样式 --}}
<body class="enterprise-guest-body responsive-body enterprise-guest {{ $bodyClass ?? '' }}"
      data-page="{{ request()->route()->getName() ?? 'guest' }}"
      data-theme="{{ $theme ?? 'light' }}"
      data-layout="guest"
      data-viewport-width=""
      data-viewport-height=""
      data-device-type=""
      data-connection-type="">

{{-- 企业级无障碍跳转链接 --}}
<a href="#main-content"
   class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50 transition-none">
    跳转到主要内容
</a>

{{-- 企业级加载指示器 --}}
<div id="page-loading-indicator"
     class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50 transition-opacity duration-300"
     style="display: none;">
    <div class="flex flex-col items-center space-y-4">
        <div class="loading-spinner"></div>
        <p class="text-gray-600 text-sm">页面加载中...</p>
    </div>
</div>

{{-- 企业级错误提示容器 --}}
<div id="global-error-container" class="fixed top-4 right-4 z-50 space-y-4" style="pointer-events: none;">
    {{-- JavaScript 错误提示将在这里显示 --}}
</div>

{{-- 企业级访客主容器 - 完整响应式布局系统 --}}
<div class="guest-container" id="guest-container">
    {{-- 企业级 Logo 区域 --}}
    <div class="guest-logo-container">
        <a href="{{ url('/') }}" class="guest-logo-link no-focus-outline" aria-label="返回首页">
            @if(file_exists(public_path('images/logo.svg')))
                <img src="{{ asset('images/logo.svg') }}" alt="{{ config('app.name', 'WorkHub') }} Logo"
                     class="w-20 h-20 mx-auto">
            @elseif(View::exists('components.application-logo'))
                <x-application-logo class="w-20 h-20 fill-current text-white mx-auto"/>
            @else
                <div class="w-20 h-20 mx-auto bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <span class="text-white text-2xl font-bold">{{ substr(config('app.name', 'W'), 0, 1) }}</span>
                </div>
            @endif
        </a>

        {{-- 企业级品牌标题 --}}
        <h1 class="mt-4 text-white text-xl font-semibold text-center">
            {{ config('app.name', 'WorkHub') }}
        </h1>
        <p class="mt-2 text-white text-opacity-80 text-sm text-center">
            {{ $subtitle ?? '现代化企业工作协作平台' }}
        </p>
    </div>

    {{-- 企业级主要内容卡片 --}}
    <main class="guest-card" id="main-content" role="main" aria-label="主要内容区域">
        {{-- 页面通知区域 --}}
        @if(session('success'))
            <div class="alert bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md mb-4"
                 role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2" aria-hidden="true"></i>
                    <span>{{ session('success') }}</span>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="alert bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md mb-4" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2" aria-hidden="true"></i>
                    <span>{{ session('error') }}</span>
                </div>
            </div>
        @endif

        @if(session('warning'))
            <div class="alert bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-md mb-4"
                 role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2" aria-hidden="true"></i>
                    <span>{{ session('warning') }}</span>
                </div>
            </div>
        @endif

        @if(session('info'))
            <div class="alert bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded-md mb-4" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-info-circle mr-2" aria-hidden="true"></i>
                    <span>{{ session('info') }}</span>
                </div>
            </div>
        @endif

        {{-- 主要页面内容 --}}
        {{ $slot }}
    </main>

    {{-- 企业级页脚信息 --}}
    <footer class="mt-8 text-center text-white text-opacity-70 text-sm">
        <p>&copy; {{ date('Y') }} {{ config('app.name', 'WorkHub') }}. 保留所有权利.</p>
        @if(config('app.company.icp'))
            <p class="mt-1">{{ config('app.company.icp') }}</p>
        @endif
        <div class="mt-2 space-x-4">
            <a href="{{ url('/privacy') }}" class="hover:text-white no-focus-outline">隐私政策</a>
            <a href="{{ url('/terms') }}" class="hover:text-white no-focus-outline">服务条款</a>
            <a href="{{ url('/help') }}" class="hover:text-white no-focus-outline">帮助中心</a>
        </div>
    </footer>
</div>

{{-- 企业级 JavaScript 资源 - Vite 优化加载 --}}
@if(app()->environment('production'))
    @vite(['resources/js/app.js'])
@else
    @php
        $viteManifestExists = file_exists(public_path('build/manifest.json'));
        $viteDevServerRunning = false;

        // 简单检查 Vite 开发服务器是否可能在运行
        if (!$viteManifestExists && function_exists('fsockopen')) {
            $connection = @fsockopen('127.0.0.1', 5173, $errno, $errstr, 1);
            if ($connection) {
                $viteDevServerRunning = true;
                fclose($connection);
            }
        }
    @endphp

    @if($viteManifestExists || $viteDevServerRunning)
        @vite(['resources/js/app.js'])
    @else
        {{-- Vite 开发服务器未运行时的备用方案 --}}
        <script>
            console.log('Vite 开发服务器未运行，使用备用配置');
            // 基础的 Vue.js 和 Axios 配置
            if (typeof Vue === 'undefined') {
                window.Vue = {};
            }
            if (typeof axios === 'undefined') {
                window.axios = {
                    defaults: {headers: {common: {}}},
                    interceptors: {
                        response: {
                            use: function () {
                            }
                        }
                    }
                };
            }
        </script>
    @endif
@endif

{{-- 页面特定脚本栈 --}}
@stack('scripts')

{{-- 企业级访客页面初始化脚本 - 完整响应式支持和性能监控 --}}
<script>
    // 企业级访客页面管理器
    class EnterpriseGuestPageManager {
        constructor() {
            this.isInitialized = false;
            this.deviceInfo = {};
            this.performanceMetrics = {};
            this.errorCount = 0;
            this.maxErrors = 5;

            this.init();
        }

        init() {
            if (this.isInitialized) return;

            // 设置全局错误处理
            this.setupErrorHandling();

            // 初始化设备检测
            this.initDeviceDetection();

            // 初始化响应式系统
            this.initResponsiveSystem();

            // 初始化性能监控
            this.initPerformanceMonitoring();

            // 初始化无障碍支持
            this.initAccessibilitySupport();

            // 初始化网络状态监控
            this.initNetworkMonitoring();

            // 标记为已初始化
            this.isInitialized = true;

            // 触发初始化完成事件
            this.dispatchPageLoadedEvent();

            console.log('企业级访客页面管理器初始化完成');
        }

        // 全局错误处理系统
        setupErrorHandling() {
            const self = this;

            window.addEventListener('error', function (event) {
                self.handleError('JavaScript Error', event.error, {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            });

            window.addEventListener('unhandledrejection', function (event) {
                self.handleError('Promise Rejection', event.reason);
            });
        }

        handleError(type, error, details = {}) {
            this.errorCount++;

            if (this.errorCount > this.maxErrors) {
                console.warn('错误数量超过限制，停止错误处理');
                return;
            }

            const errorInfo = {
                type: type,
                message: error?.message || error,
                stack: error?.stack,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                ...details
            };

            // 开发环境显示详细错误
            if (window.AppConfig?.debug) {
                console.error('页面错误:', errorInfo);
            }

            // 显示用户友好的错误提示
            this.showErrorNotification(type, error?.message || error);
        }

        showErrorNotification(type, message) {
            const container = document.getElementById('global-error-container');
            if (!container) return;

            const notification = document.createElement('div');
            notification.className = 'bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md shadow-sm pointer-events-auto';
            notification.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2" aria-hidden="true"></i>
                            <span class="text-sm">系统遇到问题，请刷新页面重试</span>
                        </div>
                        <button type="button" class="ml-4 text-red-600 hover:text-red-800" onclick="this.parentElement.parentElement.remove()">
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                `;

            container.appendChild(notification);

            // 5秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 设备检测系统
        initDeviceDetection() {
            const body = document.body;

            // 检测设备类型
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isTablet = /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)|Android(?=.*\bMobile\b)/i.test(navigator.userAgent) && window.innerWidth >= 768;
            const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

            // 设置设备类型属性
            body.dataset.deviceType = isMobile ? 'mobile' : (isTablet ? 'tablet' : 'desktop');

            // 添加设备类型类
            if (isMobile) body.classList.add('is-mobile-device');
            if (isTablet) body.classList.add('is-tablet-device');
            if (isTouch) body.classList.add('is-touch-device');

            // 存储设备信息
            this.deviceInfo = {
                isMobile,
                isTablet,
                isTouch,
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language
            };
        }

        // 响应式系统
        initResponsiveSystem() {
            const self = this;

            function updateResponsiveState() {
                const width = window.innerWidth;
                const height = window.innerHeight;
                const body = document.body;

                // 移除所有响应式类
                body.classList.remove('is-mobile', 'is-tablet', 'is-desktop', 'is-large-desktop');

                // 添加对应的响应式类
                if (width < 768) {
                    body.classList.add('is-mobile');
                } else if (width < 1024) {
                    body.classList.add('is-tablet');
                } else if (width < 1760) {
                    body.classList.add('is-desktop');
                } else {
                    body.classList.add('is-large-desktop');
                }

                // 更新 body 数据属性
                body.dataset.viewportWidth = width;
                body.dataset.viewportHeight = height;

                // 更新CSS自定义属性
                document.documentElement.style.setProperty('--viewport-width', width + 'px');
                document.documentElement.style.setProperty('--viewport-height', height + 'px');

                // 触发响应式变化事件
                window.dispatchEvent(new CustomEvent('viewportChanged', {
                    detail: {width, height}
                }));
            }

            // 初始化响应式状态
            updateResponsiveState();

            // 监听窗口大小变化 - 防抖处理
            let resizeTimer;
            window.addEventListener('resize', function () {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(updateResponsiveState, 100);
            });

            // 监听屏幕方向变化
            if (screen.orientation) {
                screen.orientation.addEventListener('change', updateResponsiveState);
            }
        }

        // 性能监控系统
        initPerformanceMonitoring() {
            const self = this;

            if ('performance' in window) {
                window.addEventListener('load', function () {
                    setTimeout(function () {
                        const perfData = performance.getEntriesByType('navigation')[0];
                        if (perfData) {
                            self.performanceMetrics = {
                                domContentLoaded: Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
                                loadComplete: Math.round(perfData.loadEventEnd - perfData.loadEventStart),
                                totalTime: Math.round(perfData.loadEventEnd - perfData.fetchStart),
                                dnsLookup: Math.round(perfData.domainLookupEnd - perfData.domainLookupStart),
                                tcpConnect: Math.round(perfData.connectEnd - perfData.connectStart),
                                serverResponse: Math.round(perfData.responseEnd - perfData.requestStart)
                            };

                            if (window.AppConfig?.debug) {
                                console.log('访客页面性能指标:', self.performanceMetrics);
                            }
                        }
                    }, 0);
                });
            }
        }

        // 无障碍支持系统
        initAccessibilitySupport() {
            // 键盘导航支持
            document.addEventListener('keydown', function (event) {
                // Tab 键导航高亮
                if (event.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });

            document.addEventListener('mousedown', function () {
                document.body.classList.remove('keyboard-navigation');
            });

            // 跳转链接支持
            const skipLink = document.querySelector('a[href="#main-content"]');
            if (skipLink) {
                skipLink.addEventListener('click', function (event) {
                    event.preventDefault();
                    const mainContent = document.getElementById('main-content');
                    if (mainContent) {
                        mainContent.focus();
                        mainContent.scrollIntoView({behavior: 'smooth'});
                    }
                });
            }
        }

        // 网络状态监控
        initNetworkMonitoring() {
            const body = document.body;

            // 检测网络连接状态
            if ('navigator' in window && 'onLine' in navigator) {
                const updateOnlineStatus = () => {
                    body.dataset.connectionType = navigator.onLine ? 'online' : 'offline';
                    body.classList.toggle('is-offline', !navigator.onLine);
                };

                window.addEventListener('online', updateOnlineStatus);
                window.addEventListener('offline', updateOnlineStatus);
                updateOnlineStatus();
            }

            // 检测网络连接质量
            if ('connection' in navigator) {
                const connection = navigator.connection;
                const updateConnectionInfo = () => {
                    body.dataset.connectionType = connection.effectiveType || 'unknown';

                    if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                        body.classList.add('is-slow-connection');
                    } else {
                        body.classList.remove('is-slow-connection');
                    }
                };

                connection.addEventListener('change', updateConnectionInfo);
                updateConnectionInfo();
            }
        }

        // 触发页面加载完成事件
        dispatchPageLoadedEvent() {
            window.dispatchEvent(new CustomEvent('guestPageLoaded', {
                detail: {
                    page: document.body.dataset.page,
                    timestamp: Date.now(),
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    device: this.deviceInfo,
                    performance: this.performanceMetrics,
                    isMobile: window.innerWidth < 768,
                    isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
                    isDesktop: window.innerWidth >= 1024,
                    isTouch: 'ontouchstart' in window
                }
            }));
        }
    }

    // 页面加载完成后初始化企业级访客页面管理器
    document.addEventListener('DOMContentLoaded', function () {
        window.EnterpriseGuestPageManager = new EnterpriseGuestPageManager();
    });

    // 页面卸载前清理
    window.addEventListener('beforeunload', function () {
        // 清理定时器和事件监听器
        if (window.EnterpriseGuestPageManager) {
            console.log('访客页面卸载，清理资源');
        }
    });
</script>

{{-- 企业级访客页面 Vue.js 全局配置系统 --}}
<script>
    // 企业级访客页面全局配置对象
    window.AppConfig = {
        // 应用基础信息
        name: @json(config('app.name')),
        url: @json(config('app.url')),
        locale: @json(app()->getLocale()),
        timezone: @json(config('app.timezone')),

        // 安全配置
        csrfToken: @json(csrf_token()),

        // API 配置
        api: {
            baseUrl: @json(config('app.url') . '/api'),
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 1000
        },

        // 访客页面数据
        page: {
            title: @json($title ?? ''),
            route: @json(request()->route()?->getName() ?? ''),
            params: {!! json_encode(request()->route()?->parameters() ?? [], JSON_UNESCAPED_UNICODE) !!},
            query: {!! json_encode(request()->query(), JSON_UNESCAPED_UNICODE) !!},
            method: @json(request()->method()),
            isSecure: @json(request()->secure()),
            userAgent: @json(request()->userAgent())
        },

        // 环境配置
        env: @json(config('app.env')),
        debug: @json(config('app.debug')),
        version: @json(config('app.version', '1.0.0')),

        // 功能配置
        features: {
            registration: @json(config('features.registration', true)),
            passwordReset: @json(config('features.password_reset', true)),
            socialLogin: @json(config('features.social_login', false)),
            rememberMe: @json(config('features.remember_me', true))
        },

        // 通知配置
        notifications: {
            position: 'top-right',
            duration: 5000,
            maxVisible: 3
        },

        // 主题配置
        theme: {
            primary: '#5247ef',
            secondary: '#5247ef',
            success: '#04c717',
            warning: '#FF5000',
            danger: '#FF0036',
            gray: '#97a6ba'
        },

        // 企业级配置
        enterprise: {
            companyName: @json(config('app.company.name', config('app.name'))),
            companyLogo: @json(asset('images/logo.png')),
            supportEmail: @json(config('app.company.email', '')),
            supportPhone: @json(config('app.company.phone', '')),
            privacyUrl: @json(url('/privacy')),
            termsUrl: @json(url('/terms')),
            helpUrl: @json(url('/help'))
        }
    };

    // 设置 Axios 默认配置
    if (typeof axios !== 'undefined') {
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        axios.defaults.headers.common['X-CSRF-TOKEN'] = window.AppConfig.csrfToken;
        axios.defaults.timeout = window.AppConfig.api.timeout;
        axios.defaults.baseURL = window.AppConfig.api.baseUrl;

        // 响应拦截器
        axios.interceptors.response.use(
            function (response) {
                return response;
            },
            function (error) {
                // 统一错误处理
                if (error.response?.status === 419) {
                    // CSRF 令牌过期，刷新页面
                    window.location.reload();
                }
                return Promise.reject(error);
            }
        );
    }

    // 设置 Vue.js 全局配置
    if (typeof Vue !== 'undefined') {
        Vue.config.productionTip = window.AppConfig.debug;
        Vue.config.devtools = window.AppConfig.debug;
    }

    // 全局工具函数
    window.AppUtils = {
        // 防抖函数
        debounce: function (func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 节流函数
        throttle: function (func, limit) {
            let inThrottle;
            return function () {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        // 表单验证辅助
        validateEmail: function (email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },

        // 密码强度检查
        checkPasswordStrength: function (password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }
    };
</script>

{{-- 企业级页面底部脚本 - 最后加载的脚本 --}}
@stack('bottom-scripts')

{{-- 企业级页面加载完成隐藏加载指示器 --}}
<script>
    window.addEventListener('load', function () {
        // 隐藏页面加载指示器
        const loadingIndicator = document.getElementById('page-loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.opacity = '0';
            setTimeout(() => {
                loadingIndicator.style.display = 'none';
            }, 300);
        }

        // 触发页面完全加载事件
        window.dispatchEvent(new CustomEvent('guestPageFullyLoaded', {
            detail: {
                timestamp: Date.now(),
                loadTime: performance.now()
            }
        }));
    });
</script>

{{-- 企业级无 JavaScript 支持提示 --}}
<noscript>
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-9999">
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
            <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
            <h2 class="text-xl font-semibold text-gray-900 mb-2">需要启用 JavaScript</h2>
            <p class="text-gray-600 mb-4">
                {{ config('app.name', 'WorkHub') }} 需要 JavaScript 才能正常运行。请在浏览器设置中启用 JavaScript 后刷新页面。
            </p>
            <button onclick="window.location.reload()"
                    class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700">
                刷新页面
            </button>
        </div>
    </div>
</noscript>
</body>
</html>
