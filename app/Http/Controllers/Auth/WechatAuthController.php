<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\UserCenter\User;
use App\Services\Auth\AuthService;
use App\Services\Wechat\WechatQrCodeService;
use App\Services\Wechat\WechatService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\View\View;

/**
 * 微信认证控制器
 *
 * 负责处理微信公众号扫码登录注册功能，包括：
 * - 生成微信登录二维码
 * - 处理微信授权回调
 * - 微信用户信息获取
 * - 微信账号绑定和解绑
 * - 扫码状态查询
 *
 * @package App\Http\Controllers\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class WechatAuthController extends Controller
{
    /**
     * 微信服务实例
     *
     * @var WechatService
     */
    protected WechatService $wechatService;

    /**
     * 微信二维码服务实例
     *
     * @var WechatQrCodeService
     */
    protected WechatQrCodeService $qrCodeService;

    /**
     * 认证服务实例
     *
     * @var AuthService
     */
    protected AuthService $authService;

    /**
     * 构造函数
     *
     * 注入所需的服务依赖
     *
     * @param WechatService $wechatService 微信服务
     * @param WechatQrCodeService $qrCodeService 二维码服务
     * @param AuthService $authService 认证服务
     */
    public function __construct(
        WechatService       $wechatService,
        WechatQrCodeService $qrCodeService,
        AuthService         $authService
    )
    {
        $this->wechatService = $wechatService;
        $this->qrCodeService = $qrCodeService;
        $this->authService = $authService;
    }

    /**
     * 显示微信登录页面
     *
     * 渲染微信扫码登录页面
     *
     * @return View|RedirectResponse 返回登录视图或重定向响应
     */
    public function showLoginForm(): View|RedirectResponse
    {
        // 如果用户已经登录，重定向到仪表板
        if (Auth::check()) {
            Log::info('已登录用户尝试访问微信登录页面', [
                'user_id' => Auth::id(),
                'ip' => request()->ip()
            ]);

            return redirect()->intended(route('dashboard', absolute: false));
        }

        return view('auth.wechat-login', [
            'title' => '微信登录',
            'description' => '使用微信扫码快速登录'
        ]);
    }

    /**
     * 生成微信登录二维码
     *
     * 生成用于微信扫码登录的二维码
     *
     * @param Request $request HTTP请求对象
     * @return JsonResponse 返回二维码信息
     */
    public function generateQrCode(Request $request): JsonResponse
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'type' => 'required|string|in:login,register,bind',
                'redirect_url' => 'sometimes|string|url'
            ]);

            $type = $validated['type'];
            $redirectUrl = $validated['redirect_url'] ?? route('dashboard', absolute: false);

            // 生成场景值（用于标识此次扫码请求）
            $sceneStr = $this->generateSceneString($type);

            // 生成微信二维码
            $qrCodeData = $this->qrCodeService->createTempQrCode($sceneStr, 600); // 10分钟有效期

            if (!$qrCodeData['success']) {
                throw new \Exception($qrCodeData['message'] ?? '二维码生成失败');
            }

            // 存储扫码会话信息
            $this->storeQrCodeSession($sceneStr, [
                'type' => $type,
                'redirect_url' => $redirectUrl,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'created_at' => now(),
                'expires_at' => now()->addMinutes(10)
            ]);

            // 记录二维码生成日志
            Log::info('微信登录二维码生成成功', [
                'scene_str' => $sceneStr,
                'type' => $type,
                'ip' => $request->ip(),
                'expires_at' => now()->addMinutes(10)->toDateTimeString()
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'qr_code_url' => $qrCodeData['url'],
                    'scene_str' => $sceneStr,
                    'expires_in' => 600, // 秒
                    'polling_interval' => 2000, // 轮询间隔（毫秒）
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('微信登录二维码生成失败', [
                'type' => $request->input('type'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成场景字符串
     *
     * 生成唯一的场景值用于标识扫码请求
     *
     * @param string $type 操作类型
     * @return string 返回场景字符串
     */
    private function generateSceneString(string $type): string
    {
        return $type . '_' . time() . '_' . Str::random(8);
    }

    /**
     * 存储二维码会话信息
     *
     * @param string $sceneStr 场景值
     * @param array $data 会话数据
     */
    private function storeQrCodeSession(string $sceneStr, array $data): void
    {
        $key = "wechat_qr_session:{$sceneStr}";
        cache()->put($key, $data, now()->addMinutes(15)); // 15分钟过期
    }

    /**
     * 查询扫码状态
     *
     * 前端轮询查询二维码扫描状态
     *
     * @param Request $request HTTP请求对象
     * @return JsonResponse 返回扫码状态
     */
    public function checkQrCodeStatus(Request $request): JsonResponse
    {
        try {
            $sceneStr = $request->input('scene_str');

            if (!$sceneStr) {
                return response()->json([
                    'success' => false,
                    'message' => '场景值不能为空'
                ], 400);
            }

            // 获取扫码会话信息
            $sessionData = $this->getQrCodeSession($sceneStr);

            if (!$sessionData) {
                return response()->json([
                    'success' => false,
                    'status' => 'expired',
                    'message' => '二维码已过期，请重新获取'
                ]);
            }

            // 检查是否已扫码
            $scanResult = $this->checkScanResult($sceneStr);

            return response()->json([
                'success' => true,
                'status' => $scanResult['status'],
                'message' => $scanResult['message'],
                'data' => $scanResult['data'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('查询微信扫码状态失败', [
                'scene_str' => $request->input('scene_str'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '查询状态失败'
            ], 500);
        }
    }

    /**
     * 获取二维码会话信息
     *
     * @param string $sceneStr 场景值
     * @return array|null 返回会话数据或null
     */
    private function getQrCodeSession(string $sceneStr): ?array
    {
        $key = "wechat_qr_session:{$sceneStr}";
        return cache()->get($key);
    }

    /**
     * 检查扫码结果
     *
     * @param string $sceneStr 场景值
     * @return array 返回扫码状态
     */
    private function checkScanResult(string $sceneStr): array
    {
        $key = "wechat_scan_result:{$sceneStr}";
        $result = cache()->get($key);

        if (!$result) {
            return [
                'status' => 'waiting',
                'message' => '等待扫码'
            ];
        }

        return $result;
    }

    /**
     * 处理微信授权回调
     *
     * 处理微信公众号的授权回调请求
     *
     * @param Request $request HTTP请求对象
     * @return JsonResponse|RedirectResponse 返回处理结果
     */
    public function handleCallback(Request $request): JsonResponse|RedirectResponse
    {
        try {
            // 验证微信签名
            if (!$this->wechatService->verifySignature($request)) {
                Log::warning('微信回调签名验证失败', [
                    'ip' => $request->ip(),
                    'params' => $request->all()
                ]);

                return response()->json(['success' => false, 'message' => '签名验证失败'], 403);
            }

            // 获取微信消息内容
            $message = $this->wechatService->parseMessage($request);

            if (!$message) {
                return response()->json(['success' => false, 'message' => '消息解析失败'], 400);
            }

            // 处理扫码事件
            if ($message['MsgType'] === 'event' && $message['Event'] === 'SCAN') {
                return $this->handleScanEvent($message);
            }

            // 处理关注事件（带参数二维码）
            if ($message['MsgType'] === 'event' && $message['Event'] === 'subscribe') {
                return $this->handleSubscribeEvent($message);
            }

            return response()->json(['success' => true, 'message' => 'ok']);

        } catch (\Exception $e) {
            Log::error('微信回调处理失败', [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['success' => false, 'message' => '处理失败'], 500);
        }
    }

    /**
     * 处理扫码事件
     *
     * 处理用户扫描二维码的事件
     *
     * @param array $message 微信消息数据
     * @return JsonResponse 返回处理结果
     */
    private function handleScanEvent(array $message): JsonResponse
    {
        try {
            $sceneStr = $message['EventKey'] ?? '';
            $openId = $message['FromUserName'] ?? '';

            if (!$sceneStr || !$openId) {
                return response()->json(['success' => false, 'message' => '参数不完整'], 400);
            }

            // 获取扫码会话信息
            $sessionData = $this->getQrCodeSession($sceneStr);

            if (!$sessionData) {
                return response()->json(['success' => false, 'message' => '会话已过期'], 400);
            }

            // 获取微信用户信息
            $userInfo = $this->wechatService->getUserInfo($openId);

            if (!$userInfo['success']) {
                throw new \Exception('获取微信用户信息失败');
            }

            // 根据类型处理登录或注册
            $result = match ($sessionData['type']) {
                'login' => $this->processWechatLogin($openId, $userInfo['data'], $sessionData),
                'register' => $this->processWechatRegister($openId, $userInfo['data'], $sessionData),
                'bind' => $this->processWechatBind($openId, $userInfo['data'], $sessionData),
                default => throw new \Exception('不支持的操作类型')
            };

            // 更新扫码结果
            $this->updateScanResult($sceneStr, $result);

            return response()->json(['success' => true, 'message' => 'ok']);

        } catch (\Exception $e) {
            Log::error('处理微信扫码事件失败', [
                'scene_str' => $sceneStr ?? 'unknown',
                'open_id' => $openId ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            // 更新扫码结果为失败
            if (isset($sceneStr)) {
                $this->updateScanResult($sceneStr, [
                    'status' => 'failed',
                    'message' => $e->getMessage()
                ]);
            }

            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * 处理微信登录
     *
     * 使用微信OpenID进行用户登录
     *
     * @param string $openId 微信OpenID
     * @param array $userInfo 微信用户信息
     * @param array $sessionData 扫码会话数据
     * @return array 返回处理结果
     */
    private function processWechatLogin(string $openId, array $userInfo, array $sessionData): array
    {
        try {
            // 查找绑定的用户
            $user = User::where('mp_openid', $openId)->first();

            if (!$user) {
                return [
                    'status' => 'failed',
                    'message' => '该微信账号尚未绑定用户，请先注册或绑定账号'
                ];
            }

            // 检查用户状态
            if (isset($user->status) && $user->status !== 'active') {
                return [
                    'status' => 'failed',
                    'message' => '账户已被禁用，请联系客服'
                ];
            }

            // 更新用户微信信息
            $user->update([
                'nickname' => $userInfo['nickname'] ?? $user->nickname,
                'head_img_url' => $userInfo['headimgurl'] ?? $user->head_img_url,
                'last_login_at' => now(),
            ]);

            // 记录登录成功日志
            Log::info('微信扫码登录成功', [
                'user_id' => $user->id,
                'open_id' => $openId,
                'nickname' => $userInfo['nickname'] ?? '',
                'ip' => $sessionData['ip'] ?? ''
            ]);

            return [
                'status' => 'success',
                'message' => '登录成功',
                'data' => [
                    'user_id' => $user->id,
                    'redirect_url' => $sessionData['redirect_url'] ?? route('dashboard', absolute: false)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('微信登录处理失败', [
                'open_id' => $openId,
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'failed',
                'message' => '登录处理失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理微信注册
     *
     * 使用微信信息创建新用户账号
     *
     * @param string $openId 微信OpenID
     * @param array $userInfo 微信用户信息
     * @param array $sessionData 扫码会话数据
     * @return array 返回处理结果
     */
    private function processWechatRegister(string $openId, array $userInfo, array $sessionData): array
    {
        try {
            // 检查OpenID是否已注册
            if (User::where('mp_openid', $openId)->exists()) {
                return [
                    'status' => 'failed',
                    'message' => '该微信账号已注册，请直接登录'
                ];
            }

            // 使用数据库事务创建用户
            $user = DB::transaction(function () use ($openId, $userInfo) {
                return User::create([
                    'name' => $userInfo['nickname'] ?? '微信用户' . substr($openId, -6),
                    'nickname' => $userInfo['nickname'] ?? '',
                    'mp_openid' => $openId,
                    'head_img_url' => $userInfo['headimgurl'] ?? '',
                    'password' => Hash::make(Str::random(32)), // 随机密码
                    'email_verified_at' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            });

            // 触发注册事件
            event(new Registered($user));

            // 记录注册成功日志
            Log::info('微信扫码注册成功', [
                'user_id' => $user->id,
                'open_id' => $openId,
                'nickname' => $userInfo['nickname'] ?? '',
                'ip' => $sessionData['ip'] ?? ''
            ]);

            return [
                'status' => 'success',
                'message' => '注册成功',
                'data' => [
                    'user_id' => $user->id,
                    'redirect_url' => $sessionData['redirect_url'] ?? route('dashboard', absolute: false)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('微信注册处理失败', [
                'open_id' => $openId,
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'failed',
                'message' => '注册处理失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理微信绑定
     *
     * 为已登录用户绑定微信账号
     *
     * @param string $openId 微信OpenID
     * @param array $userInfo 微信用户信息
     * @param array $sessionData 扫码会话数据
     * @return array 返回处理结果
     */
    private function processWechatBind(string $openId, array $userInfo, array $sessionData): array
    {
        try {
            // 检查OpenID是否已被其他用户绑定
            $existingUser = User::where('mp_openid', $openId)->first();

            if ($existingUser) {
                return [
                    'status' => 'failed',
                    'message' => '该微信账号已被其他用户绑定'
                ];
            }

            // 这里需要从会话中获取当前用户ID
            // 实际实现中可能需要通过其他方式传递用户信息
            $userId = $sessionData['user_id'] ?? null;

            if (!$userId) {
                return [
                    'status' => 'failed',
                    'message' => '用户信息缺失，请重新登录后绑定'
                ];
            }

            $user = User::find($userId);

            if (!$user) {
                return [
                    'status' => 'failed',
                    'message' => '用户不存在'
                ];
            }

            // 更新用户微信信息
            $user->update([
                'mp_openid' => $openId,
                'nickname' => $userInfo['nickname'] ?? $user->nickname,
                'head_img_url' => $userInfo['headimgurl'] ?? $user->head_img_url,
            ]);

            // 记录绑定成功日志
            Log::info('微信账号绑定成功', [
                'user_id' => $user->id,
                'open_id' => $openId,
                'nickname' => $userInfo['nickname'] ?? '',
                'ip' => $sessionData['ip'] ?? ''
            ]);

            return [
                'status' => 'success',
                'message' => '微信账号绑定成功',
                'data' => [
                    'user_id' => $user->id,
                    'redirect_url' => $sessionData['redirect_url'] ?? route('profile.edit')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('微信绑定处理失败', [
                'open_id' => $openId,
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'failed',
                'message' => '绑定处理失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 更新扫码结果
     *
     * @param string $sceneStr 场景值
     * @param array $result 扫码结果
     */
    private function updateScanResult(string $sceneStr, array $result): void
    {
        $key = "wechat_scan_result:{$sceneStr}";
        cache()->put($key, $result, now()->addMinutes(5)); // 5分钟过期
    }

    /**
     * 处理关注事件
     *
     * 处理用户关注公众号的事件（带参数二维码）
     *
     * @param array $message 微信消息数据
     * @return JsonResponse 返回处理结果
     */
    private function handleSubscribeEvent(array $message): JsonResponse
    {
        try {
            $eventKey = $message['EventKey'] ?? '';
            $openId = $message['FromUserName'] ?? '';

            // 提取场景值（去掉qrscene_前缀）
            $sceneStr = str_replace('qrscene_', '', $eventKey);

            if (!$sceneStr || !$openId) {
                return response()->json(['success' => false, 'message' => '参数不完整'], 400);
            }

            // 发送欢迎消息
            $this->wechatService->sendTextMessage($openId, '欢迎关注！正在为您处理登录请求...');

            // 处理扫码事件（复用扫码逻辑）
            $modifiedMessage = $message;
            $modifiedMessage['Event'] = 'SCAN';
            $modifiedMessage['EventKey'] = $sceneStr;

            return $this->handleScanEvent($modifiedMessage);

        } catch (\Exception $e) {
            Log::error('处理微信关注事件失败', [
                'event_key' => $eventKey ?? 'unknown',
                'open_id' => $openId ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * 解绑微信账号
     *
     * 为当前用户解绑微信账号
     *
     * @param Request $request HTTP请求对象
     * @return JsonResponse 返回解绑结果
     */
    public function unbindWechat(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => '请先登录'
                ], 401);
            }

            if (!$user->mp_openid) {
                return response()->json([
                    'success' => false,
                    'message' => '您尚未绑定微信账号'
                ], 400);
            }

            // 解绑微信
            $user->update([
                'mp_openid' => null,
                'nickname' => null,
                'head_img_url' => null,
            ]);

            // 记录解绑日志
            Log::info('微信账号解绑成功', [
                'user_id' => $user->id,
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => true,
                'message' => '微信账号解绑成功'
            ]);

        } catch (\Exception $e) {
            Log::error('微信账号解绑失败', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '解绑失败，请稍后重试'
            ], 500);
        }
    }
}
