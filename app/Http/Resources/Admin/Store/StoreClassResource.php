<?php

namespace App\Http\Resources\Admin\Store;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class StoreClassResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'platform_id' => $this->platform_id ?? null,
            //
            'uuid' => $this->uuid ?? null,
            'category_id' => $this->category_id ?? null,
            'class_name' => $this->class_name ?? null,
            'class_english_name' => $this->class_english_name ?? null,
            'commission_rate' => $this->commission_rate ?? null,
            'qualification' => $this->qualification ?? null,
            'is_state' => $this->is_state ?? null,
            'is_check' => $this->is_check ?? null,
            'categories' => isset($this->categories) ? $this->categories()->select('id')->pluck('id')->toArray() : null,
        ]);
    }
}
