<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceTrendResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid,
            'platform_id' => $this->platform_id,
            'site_id' => $this->site_id,
            'category_id' => $this->category_id,
            'group_id' => $this->group_id,
            'trend_no' => $this->trend_no,
            'trend_name' => $this->trend_name,
            'trend_english_name' => $this->trend_english_name,
            'alias_names' => $this->alias_names,
            'trend_value' => $this->trend_value,
            'trend_english_value' => $this->trend_english_value,
            'trend_thumb' => $this->trend_thumb,
            'trend_description' => $this->trend_description,
            'deep' => $this->deep,
            'sort' => $this->sort,
            'is_thumb' => $this->is_thumb,
        ]);
    }
}
