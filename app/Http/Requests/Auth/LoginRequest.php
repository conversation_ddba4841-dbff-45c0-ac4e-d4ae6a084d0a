<?php

namespace App\Http\Requests\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

/**
 * 用户登录请求验证类
 *
 * 负责处理用户登录请求的验证逻辑，包括：
 * - 验证登录表单数据
 * - 执行用户身份认证
 * - 实施登录频率限制
 * - 防止暴力破解攻击
 *
 * @package App\Http\Requests\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class LoginRequest extends FormRequest
{
    /**
     * 最大登录尝试次数
     * 在指定时间窗口内允许的最大失败登录次数
     */
    private const MAX_LOGIN_ATTEMPTS = 5;

    /**
     * 登录限制时间窗口（秒）
     * 达到最大尝试次数后的锁定时间
     */
    private const LOCKOUT_DURATION = 900; // 15分钟

    /**
     * 确定用户是否有权限发出此请求
     *
     * 对于登录请求，任何用户都可以尝试登录
     *
     * @return bool 始终返回true，允许所有用户尝试登录
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * 定义登录表单字段的验证规则，包括邮箱和密码的格式验证
     *
     * @return array<string, ValidationRule|array<mixed>|string> 返回验证规则数组
     */
    public function rules(): array
    {
        return [
            // 邮箱字段验证规则
            'email' => [
                'required',          // 必填字段
                'string',           // 必须是字符串
                'email',            // 必须是有效的邮箱格式
                'max:255',          // 最大长度限制
                'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', // 严格的邮箱格式验证
            ],

            // 密码字段验证规则
            'password' => [
                'required',          // 必填字段
                'string',           // 必须是字符串
                'min:1',            // 最小长度（登录时不需要太严格）
                'max:255',          // 最大长度限制
            ],

            // 记住我选项（可选）
            'remember' => [
                'sometimes',         // 可选字段
                'boolean',          // 必须是布尔值
            ],
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * 为验证失败提供用户友好的中文错误消息
     *
     * @return array<string, string> 返回自定义错误消息数组
     */
    public function messages(): array
    {
        return [
            // 邮箱字段错误消息
            'email.required' => '邮箱地址是必填项',
            'email.string' => '邮箱地址必须是文本格式',
            'email.email' => '请输入有效的邮箱地址',
            'email.max' => '邮箱地址不能超过255个字符',
            'email.regex' => '邮箱格式不正确，请检查输入',

            // 密码字段错误消息
            'password.required' => '密码是必填项',
            'password.string' => '密码必须是文本格式',
            'password.min' => '密码不能为空',
            'password.max' => '密码长度不能超过255个字符',

            // 记住我选项错误消息
            'remember.boolean' => '记住我选项格式不正确',
        ];
    }

    /**
     * 获取验证字段的自定义属性名称
     *
     * 为验证字段提供中文名称，用于错误消息显示
     *
     * @return array<string, string> 返回字段中文名称数组
     */
    public function attributes(): array
    {
        return [
            'email' => '邮箱地址',
            'password' => '密码',
            'remember' => '记住我',
        ];
    }

    /**
     * 尝试使用请求的凭据进行身份验证
     *
     * 执行用户身份验证流程，包括频率限制检查、凭据验证
     * 成功认证后清除频率限制计数器
     *
     * @throws ValidationException 当认证失败或被频率限制时抛出异常
     */
    public function authenticate(): void
    {
        // 检查是否被频率限制
        $this->ensureIsNotRateLimited();

        // 记录登录尝试
        Log::info('用户尝试登录', [
            'email' => $this->input('email'),
            'ip' => $this->ip(),
            'user_agent' => $this->userAgent(),
            'remember' => $this->boolean('remember'),
            'attempt_time' => now()->toDateTimeString()
        ]);

        // 尝试进行身份验证
        $credentials = $this->only('email', 'password');
        $remember = $this->boolean('remember');

        if (!Auth::attempt($credentials, $remember)) {
            // 认证失败，增加频率限制计数
            RateLimiter::hit($this->throttleKey());

            // 记录认证失败日志
            Log::warning('用户登录认证失败', [
                'email' => $this->input('email'),
                'ip' => $this->ip(),
                'user_agent' => $this->userAgent(),
                'failed_attempts' => RateLimiter::attempts($this->throttleKey()),
                'attempt_time' => now()->toDateTimeString()
            ]);

            // 抛出验证异常
            throw ValidationException::withMessages([
                'email' => '登录凭据不正确，请检查您的邮箱和密码。',
            ]);
        }

        // 认证成功，清除频率限制计数器
        RateLimiter::clear($this->throttleKey());

        // 记录认证成功日志
        Log::info('用户登录认证成功', [
            'user_id' => Auth::id(),
            'email' => $this->input('email'),
            'ip' => $this->ip(),
            'user_agent' => $this->userAgent(),
            'remember' => $remember,
            'success_time' => now()->toDateTimeString()
        ]);
    }

    /**
     * 确保登录请求未被频率限制
     *
     * 检查当前用户是否超过了最大登录尝试次数
     * 如果超过限制，则触发锁定事件并抛出异常
     *
     * @throws ValidationException 当请求被频率限制时抛出异常
     */
    public function ensureIsNotRateLimited(): void
    {
        $throttleKey = $this->throttleKey();

        // 检查是否超过最大尝试次数
        if (!RateLimiter::tooManyAttempts($throttleKey, self::MAX_LOGIN_ATTEMPTS)) {
            return;
        }

        // 触发账户锁定事件
        event(new Lockout($this));

        // 获取剩余锁定时间
        $seconds = RateLimiter::availableIn($throttleKey);
        $minutes = ceil($seconds / 60);

        // 记录频率限制日志
        Log::warning('用户登录被频率限制', [
            'email' => $this->input('email'),
            'ip' => $this->ip(),
            'user_agent' => $this->userAgent(),
            'attempts' => RateLimiter::attempts($throttleKey),
            'lockout_seconds' => $seconds,
            'lockout_minutes' => $minutes,
            'throttle_key' => $throttleKey,
            'lockout_time' => now()->toDateTimeString()
        ]);

        // 抛出频率限制异常
        throw ValidationException::withMessages([
            'email' => "登录尝试次数过多，请在 {$minutes} 分钟后再试。为了您的账户安全，我们暂时限制了登录功能。",
        ]);
    }

    /**
     * 获取频率限制的节流键
     *
     * 生成用于频率限制的唯一标识符
     * 基于用户邮箱和IP地址组合，防止同一用户或IP的暴力攻击
     *
     * @return string 返回节流键字符串
     */
    public function throttleKey(): string
    {
        // 获取邮箱地址并转换为小写
        $email = Str::lower($this->string('email'));

        // 获取客户端IP地址
        $ip = $this->ip();

        // 生成节流键：邮箱|IP地址
        $throttleKey = Str::transliterate($email . '|' . $ip);

        // 记录节流键生成日志（仅在调试模式下）
        if (config('app.debug')) {
            Log::debug('生成登录节流键', [
                'email' => $email,
                'ip' => $ip,
                'throttle_key' => $throttleKey
            ]);
        }

        return $throttleKey;
    }

    /**
     * 获取剩余尝试次数
     *
     * 计算用户还可以尝试登录的次数
     *
     * @return int 返回剩余尝试次数
     */
    public function getRemainingAttempts(): int
    {
        $attempts = $this->getAttempts();
        return max(0, self::MAX_LOGIN_ATTEMPTS - $attempts);
    }

    /**
     * 获取当前IP的登录尝试次数
     *
     * 返回当前IP地址的登录失败次数
     * 用于前端显示剩余尝试次数
     *
     * @return int 返回当前尝试次数
     */
    public function getAttempts(): int
    {
        return RateLimiter::attempts($this->throttleKey());
    }

    /**
     * 检查是否接近频率限制
     *
     * 判断用户是否接近最大尝试次数
     * 用于前端显示警告信息
     *
     * @return bool 如果接近限制返回true
     */
    public function isNearRateLimit(): bool
    {
        return $this->getAttempts() >= (self::MAX_LOGIN_ATTEMPTS - 2);
    }
}
