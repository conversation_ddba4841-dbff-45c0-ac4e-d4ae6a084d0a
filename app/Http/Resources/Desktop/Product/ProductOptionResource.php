<?php

namespace App\Http\Resources\Desktop\Product;


use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProductOptionResource extends BaseResource
{


    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return [
            'uuid' => $this->uuid,
            'option_no' => $this->option_no,
            'name' => $this->name,
            'value' => $this->name,
            'skus' => empty($this->skus) ? null : ProductSkuResource::collection($this->skus),
        ];
    }
}
