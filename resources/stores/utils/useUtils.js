import {defineStore} from 'pinia'

/**
 * 通用工具函数
 * 提供一些常用的工具函数，例如格式化时间、处理字符串等
 */
export const useUtils = defineStore('utils', {
    state: () => ({
        // 工具配置
        config: {
            dateFormat: 'YYYY-MM-DD',
            timeFormat: 'HH:mm:ss',
            locale: 'zh-CN',
            currency: 'CNY'
        }
    }),

    getters: {
        // 获取当前时间戳
        getCurrentTimestamp: () => Date.now(),

        // 获取当前日期字符串
        getCurrentDate: (state) => {
            return state.formatDate(new Date())
        },

        // 获取当前时间字符串
        getCurrentTime: (state) => {
            return state.formatTime(new Date())
        }
    },

    actions: {
        /**
         * 格式化日期
         */
        formatDate(date, format = null) {
            if (!date) return ''

            const d = new Date(date)
            if (isNaN(d.getTime())) return ''

            const fmt = format || this.config.dateFormat

            const year = d.getFullYear()
            const month = String(d.getMonth() + 1).padStart(2, '0')
            const day = String(d.getDate()).padStart(2, '0')

            return fmt.replace('YYYY', year).replace('MM', month).replace('DD', day)
        },

        /**
         * 格式化时间
         */
        formatTime(date, format = null) {
            if (!date) return ''

            const d = new Date(date)
            if (isNaN(d.getTime())) return ''

            const fmt = format || this.config.timeFormat

            const hours = String(d.getHours()).padStart(2, '0')
            const minutes = String(d.getMinutes()).padStart(2, '0')
            const seconds = String(d.getSeconds()).padStart(2, '0')

            return fmt.replace('HH', hours).replace('mm', minutes).replace('ss', seconds)
        },

        /**
         * 格式化日期时间
         */
        formatDateTime(date, dateFormat = null, timeFormat = null) {
            if (!date) return ''

            const dateStr = this.formatDate(date, dateFormat)
            const timeStr = this.formatTime(date, timeFormat)

            return `${dateStr} ${timeStr}`
        },

        /**
         * 相对时间格式化
         */
        formatRelativeTime(date) {
            if (!date) return ''

            const now = new Date()
            const target = new Date(date)
            const diff = now - target

            const seconds = Math.floor(diff / 1000)
            const minutes = Math.floor(seconds / 60)
            const hours = Math.floor(minutes / 60)
            const days = Math.floor(hours / 24)

            if (days > 0) {
                if (days === 1) return '昨天'
                if (days < 7) return `${days}天前`
                return this.formatDate(target)
            }

            if (hours > 0) return `${hours}小时前`
            if (minutes > 0) return `${minutes}分钟前`
            if (seconds > 30) return `${seconds}秒前`

            return '刚刚'
        },

        /**
         * 格式化文件大小
         */
        formatFileSize(bytes) {
            if (!bytes || bytes === 0) return '0 B'

            const units = ['B', 'KB', 'MB', 'GB', 'TB']
            let size = bytes
            let unitIndex = 0

            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024
                unitIndex++
            }

            return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
        },

        /**
         * 格式化数字
         */
        formatNumber(number, options = {}) {
            if (number === null || number === undefined) return ''

            const {
                locale = this.config.locale,
                minimumFractionDigits = 0,
                maximumFractionDigits = 2
            } = options

            return new Intl.NumberFormat(locale, {
                minimumFractionDigits,
                maximumFractionDigits
            }).format(number)
        },

        /**
         * 格式化货币
         */
        formatCurrency(amount, currency = null) {
            if (amount === null || amount === undefined) return ''

            const currencyCode = currency || this.config.currency

            return new Intl.NumberFormat(this.config.locale, {
                style: 'currency',
                currency: currencyCode
            }).format(amount)
        },

        /**
         * 格式化百分比
         */
        formatPercentage(value, decimals = 1) {
            if (value === null || value === undefined) return ''

            return `${(value * 100).toFixed(decimals)}%`
        },

        /**
         * 防抖函数
         */
        debounce(func, wait) {
            let timeout
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout)
                    func(...args)
                }
                clearTimeout(timeout)
                timeout = setTimeout(later, wait)
            }
        },

        /**
         * 节流函数
         */
        throttle(func, limit) {
            let inThrottle
            return function (...args) {
                if (!inThrottle) {
                    func.apply(this, args)
                    inThrottle = true
                    setTimeout(() => inThrottle = false, limit)
                }
            }
        },

        /**
         * 深拷贝
         */
        deepClone(obj) {
            if (obj === null || typeof obj !== 'object') return obj
            if (obj instanceof Date) return new Date(obj.getTime())
            if (obj instanceof Array) return obj.map(item => this.deepClone(item))
            if (typeof obj === 'object') {
                const clonedObj = {}
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        clonedObj[key] = this.deepClone(obj[key])
                    }
                }
                return clonedObj
            }
        },

        /**
         * 生成UUID
         */
        generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                const r = Math.random() * 16 | 0
                const v = c === 'x' ? r : (r & 0x3 | 0x8)
                return v.toString(16)
            })
        },

        /**
         * 生成随机字符串
         */
        generateRandomString(length = 8) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
            let result = ''
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length))
            }
            return result
        },

        /**
         * 检查字符串是否为空
         */
        isEmpty(str) {
            return !str || str.trim().length === 0
        },

        /**
         * 首字母大写
         */
        capitalize(str) {
            if (!str) return ''
            return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
        },

        /**
         * 驼峰转换
         */
        toCamelCase(str) {
            return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
        },

        /**
         * 短横线转换
         */
        toKebabCase(str) {
            return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
        },

        /**
         * 截断字符串
         */
        truncate(str, length, suffix = '...') {
            if (!str || str.length <= length) return str
            return str.substring(0, length) + suffix
        },

        /**
         * 验证邮箱
         */
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            return emailRegex.test(email)
        },

        /**
         * 验证手机号
         */
        isValidPhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/
            return phoneRegex.test(phone)
        },

        /**
         * 验证身份证号
         */
        isValidIDCard(idCard) {
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
            return idCardRegex.test(idCard)
        },

        /**
         * 获取URL参数
         */
        getUrlParams(url = window.location.href) {
            const params = {}
            const urlObj = new URL(url)
            urlObj.searchParams.forEach((value, key) => {
                params[key] = value
            })
            return params
        },

        /**
         * 下载文件
         */
        downloadFile(data, filename, mimeType = 'text/plain') {
            const blob = new Blob([data], {type: mimeType})
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = filename
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
        },

        /**
         * 复制到剪贴板
         */
        async copyToClipboard(text) {
            try {
                if (navigator.clipboard) {
                    await navigator.clipboard.writeText(text)
                    return true
                } else {
                    // 降级处理
                    const textArea = document.createElement('textarea')
                    textArea.value = text
                    document.body.appendChild(textArea)
                    textArea.select()
                    document.execCommand('copy')
                    document.body.removeChild(textArea)
                    return true
                }
            } catch (error) {
                console.error('复制失败:', error)
                return false
            }
        },

        /**
         * 获取随机颜色
         */
        getRandomColor() {
            return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
        },

        /**
         * 计算字符串哈希值
         */
        hashCode(str) {
            let hash = 0
            if (str.length === 0) return hash
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i)
                hash = ((hash << 5) - hash) + char
                hash = hash & hash // 转换为32位整数
            }
            return Math.abs(hash)
        },

        /**
         * 更新配置
         */
        updateConfig(config) {
            this.config = {...this.config, ...config}
        }
    }
})
