{{-- 企业级管理后台侧边栏 - 无动画设计，完整功能导航 --}}
<aside class="admin-sidebar {{ ($sidebarCollapsed ?? false) ? 'collapsed' : '' }}"
       id="admin-sidebar"
       role="navigation"
       aria-label="管理后台导航">

    {{-- 侧边栏头部 - Logo 和品牌 --}}
    <div class="admin-sidebar-header p-6 border-b border-gray-700">
        <div class="flex items-center">
            @if(file_exists(public_path('images/admin-logo.svg')))
                <img src="{{ asset('images/admin-logo.svg') }}" alt="{{ config('app.name', 'WorkHub') }} Admin"
                     class="w-8 h-8">
            @elseif(file_exists(public_path('images/logo.svg')))
                <img src="{{ asset('images/logo.svg') }}" alt="{{ config('app.name', 'WorkHub') }}" class="w-8 h-8">
            @else
                <div class="w-8 h-8 bg-primary-600 rounded flex items-center justify-center">
                    <span class="text-white text-sm font-bold">{{ substr(config('app.name', 'A'), 0, 1) }}</span>
                </div>
            @endif
            <div class="ml-3 sidebar-text">
                <h1 class="text-white font-semibold text-lg">{{ config('app.name', 'WorkHub') }}</h1>
                <p class="text-gray-400 text-xs">管理后台</p>
            </div>
        </div>
    </div>

    {{-- 侧边栏导航菜单 --}}
    <nav class="admin-sidebar-nav flex-1 overflow-y-auto py-4" aria-label="主要导航菜单">
        {{-- 仪表板 --}}
        <div class="px-4 mb-6">
            <h3 class="text-gray-400 text-xs font-semibold uppercase tracking-wider mb-3 sidebar-text">仪表板</h3>
            <ul class="space-y-1">
                <li>
                    <a href="{{ route('admin.dashboard') ?? '/admin' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                       aria-label="管理后台首页">
                        <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                        <span class="sidebar-text">概览</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.analytics') ?? '/admin/analytics' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.analytics') ? 'active' : '' }}"
                       aria-label="数据分析">
                        <i class="fas fa-chart-bar" aria-hidden="true"></i>
                        <span class="sidebar-text">数据分析</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.reports') ?? '/admin/reports' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.reports') ? 'active' : '' }}"
                       aria-label="报表中心">
                        <i class="fas fa-file-chart-line" aria-hidden="true"></i>
                        <span class="sidebar-text">报表中心</span>
                    </a>
                </li>
            </ul>
        </div>

        {{-- 用户管理 --}}
        <div class="px-4 mb-6">
            <h3 class="text-gray-400 text-xs font-semibold uppercase tracking-wider mb-3 sidebar-text">用户管理</h3>
            <ul class="space-y-1">
                <li>
                    <a href="{{ route('admin.users.index') ?? '/admin/users' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}"
                       aria-label="用户列表">
                        <i class="fas fa-users" aria-hidden="true"></i>
                        <span class="sidebar-text">用户列表</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.roles.index') ?? '/admin/roles' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}"
                       aria-label="角色权限">
                        <i class="fas fa-user-shield" aria-hidden="true"></i>
                        <span class="sidebar-text">角色权限</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.permissions.index') ?? '/admin/permissions' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.permissions.*') ? 'active' : '' }}"
                       aria-label="权限管理">
                        <i class="fas fa-key" aria-hidden="true"></i>
                        <span class="sidebar-text">权限管理</span>
                    </a>
                </li>
            </ul>
        </div>

        {{-- 内容管理 --}}
        <div class="px-4 mb-6">
            <h3 class="text-gray-400 text-xs font-semibold uppercase tracking-wider mb-3 sidebar-text">内容管理</h3>
            <ul class="space-y-1">
                <li>
                    <a href="{{ route('admin.posts.index') ?? '/admin/posts' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.posts.*') ? 'active' : '' }}"
                       aria-label="文章管理">
                        <i class="fas fa-file-alt" aria-hidden="true"></i>
                        <span class="sidebar-text">文章管理</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.categories.index') ?? '/admin/categories' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}"
                       aria-label="分类管理">
                        <i class="fas fa-folder" aria-hidden="true"></i>
                        <span class="sidebar-text">分类管理</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.media.index') ?? '/admin/media' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.media.*') ? 'active' : '' }}"
                       aria-label="媒体库">
                        <i class="fas fa-images" aria-hidden="true"></i>
                        <span class="sidebar-text">媒体库</span>
                    </a>
                </li>
            </ul>
        </div>

        {{-- 系统管理 --}}
        <div class="px-4 mb-6">
            <h3 class="text-gray-400 text-xs font-semibold uppercase tracking-wider mb-3 sidebar-text">系统管理</h3>
            <ul class="space-y-1">
                <li>
                    <a href="{{ route('admin.settings.index') ?? '/admin/settings' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}"
                       aria-label="系统设置">
                        <i class="fas fa-cogs" aria-hidden="true"></i>
                        <span class="sidebar-text">系统设置</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.logs.index') ?? '/admin/logs' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.logs.*') ? 'active' : '' }}"
                       aria-label="系统日志">
                        <i class="fas fa-list-alt" aria-hidden="true"></i>
                        <span class="sidebar-text">系统日志</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.backup.index') ?? '/admin/backup' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.backup.*') ? 'active' : '' }}"
                       aria-label="备份管理">
                        <i class="fas fa-database" aria-hidden="true"></i>
                        <span class="sidebar-text">备份管理</span>
                    </a>
                </li>
                <li>
                    <a href="{{ route('admin.cache.index') ?? '/admin/cache' }}"
                       class="admin-nav-link {{ request()->routeIs('admin.cache.*') ? 'active' : '' }}"
                       aria-label="缓存管理">
                        <i class="fas fa-memory" aria-hidden="true"></i>
                        <span class="sidebar-text">缓存管理</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    {{-- 侧边栏底部 - 用户信息和快捷操作 --}}
    <div class="admin-sidebar-footer p-4 border-t border-gray-700">
        @auth
            <div class="flex items-center mb-3">
                @if(Auth::user()->avatar)
                    <img src="{{ Auth::user()->avatar }}" alt="{{ Auth::user()->name }}" class="w-8 h-8 rounded-full">
                @else
                    <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>
                @endif
                <div class="ml-3 sidebar-text">
                    <p class="text-white text-sm font-medium">{{ Auth::user()->name }}</p>
                    <p class="text-gray-400 text-xs">{{ Auth::user()->role ?? '管理员' }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <a href="{{ route('admin.profile') ?? '/admin/profile' }}"
                   class="flex-1 admin-button secondary text-center text-xs py-2"
                   aria-label="个人设置">
                    <i class="fas fa-user-cog mr-1" aria-hidden="true"></i>
                    <span class="sidebar-text">设置</span>
                </a>
                <form method="POST" action="{{ route('admin.logout') ?? '/admin/logout' }}" class="flex-1">
                    @csrf
                    <button type="submit"
                            class="w-full admin-button text-xs py-2"
                            aria-label="退出登录">
                        <i class="fas fa-sign-out-alt mr-1" aria-hidden="true"></i>
                        <span class="sidebar-text">退出</span>
                    </button>
                </form>
            </div>
        @endauth
    </div>
</aside>

{{-- 侧边栏样式 --}}
<style>
    /* 侧边栏导航链接样式 */
    .admin-nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: #94a3b8;
        text-decoration: none;
        border-radius: 0.375rem;
        transition: none;
        font-size: 0.875rem;
    }

    .admin-nav-link:hover {
        background-color: #374151;
        color: #f8fafc;
    }

    .admin-nav-link.active {
        background-color: #5247ef;
        color: #ffffff;
    }

    .admin-nav-link i {
        width: 1.25rem;
        margin-right: 0.75rem;
        text-align: center;
    }

    /* 侧边栏折叠状态 */
    .admin-sidebar.collapsed {
        width: var(--admin-sidebar-collapsed-width);
        min-width: var(--admin-sidebar-collapsed-width);
    }

    .admin-sidebar.collapsed .sidebar-text {
        display: none;
    }

    .admin-sidebar.collapsed .admin-sidebar-header h1,
    .admin-sidebar.collapsed .admin-sidebar-header p {
        display: none;
    }

    .admin-sidebar.collapsed .admin-nav-link {
        justify-content: center;
        padding: 0.75rem 0.5rem;
    }

    .admin-sidebar.collapsed .admin-nav-link i {
        margin-right: 0;
    }

    /* 响应式设计 */
    @media (max-width: 1199px) {
        .admin-sidebar {
            width: 240px;
            min-width: 240px;
        }

        .admin-sidebar.collapsed {
            width: 60px;
            min-width: 60px;
        }
    }
</style>
