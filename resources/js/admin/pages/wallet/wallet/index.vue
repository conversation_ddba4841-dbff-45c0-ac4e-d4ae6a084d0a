<!--
/**
 * 钱包管理页面
 *
 * 功能特性：
 * - 管理钱包账户和余额
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/wallet/wallet
 * 页面标题：钱包管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="admin-wallet-wallet-page">
        <BackendPageListLayout
            :loading="loading"
            :refreshing="refreshing"
            :show-empty-state="showEmptyState"
            empty-description="当前没有任何钱包数据，您可以刷新页面或创建新的钱包"
            empty-title="暂无钱包数据"
            @create="handleCreate"
            @refresh="handleRefresh"
        >
            <div class="content-area">
                <el-card class="data-card" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>钱包列表</span>
                            <el-button size="small" type="primary" @click="handleCreate">
                                <i class="fas fa-plus"></i>
                                新增钱包
                            </el-button>
                        </div>
                    </template>

                    <!-- 搜索区域 -->
                    <div class="search-section">
                        <el-form
                            :inline="true"
                            :model="searchForm"
                            class="search-form"
                            @submit.prevent="handleSearch"
                        >
                            <el-form-item label="用户名称">
                                <el-input
                                    v-model="searchForm.keyword"
                                    clearable
                                    placeholder="请输入用户名称"
                                    style="width: 200px"
                                    @keyup.enter="handleSearch"
                                />
                            </el-form-item>
                            <el-form-item label="钱包类型">
                                <el-select
                                    v-model="searchForm.type"
                                    clearable
                                    placeholder="请选择钱包类型"
                                    style="width: 150px"
                                >
                                    <el-option label="个人钱包" value="personal"/>
                                    <el-option label="企业钱包" value="business"/>
                                    <el-option label="商户钱包" value="merchant"/>
                                    <el-option label="系统钱包" value="system"/>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="状态">
                                <el-select
                                    v-model="searchForm.status"
                                    clearable
                                    placeholder="请选择状态"
                                    style="width: 120px"
                                >
                                    <el-option label="正常" value="1"/>
                                    <el-option label="冻结" value="0"/>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button
                                    :loading="loading"
                                    type="primary"
                                    @click="handleSearch"
                                >
                                    搜索
                                </el-button>
                                <el-button @click="handleReset">
                                    重置
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>

                    <!-- 表格工具栏 -->
                    <div class="table-toolbar">
                        <div class="toolbar-left">
                            <el-button
                                :disabled="!selectedRows.length"
                                size="small"
                                type="warning"
                                @click="handleBatchFreeze"
                            >
                                <i class="fas fa-lock"></i>
                                批量冻结
                            </el-button>
                            <el-button
                                size="small"
                                type="info"
                                @click="handleExport"
                            >
                                <i class="fas fa-download"></i>
                                导出数据
                            </el-button>
                        </div>
                        <div class="toolbar-right">
                            <el-tooltip content="刷新数据" placement="top">
                                <el-button
                                    circle
                                    size="small"
                                    @click="handleRefresh"
                                >
                                    <i class="fas fa-sync-alt"></i>
                                </el-button>
                            </el-tooltip>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <el-table
                        :data="tableData"
                        :loading="loading"
                        border
                        class="admin-table"
                        stripe
                        @selection-change="handleSelectionChange"
                    >
                        <el-table-column type="selection" width="55"/>
                        <el-table-column label="ID" prop="id" sortable width="80"/>
                        <el-table-column
                            label="用户名称"
                            min-width="150"
                            prop="user_name"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            label="钱包号"
                            min-width="180"
                            prop="wallet_no"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            align="center"
                            label="钱包类型"
                            prop="type"
                            width="120"
                        >
                            <template #default="{ row }">
                                <el-tag size="small">{{ getTypeName(row.type) }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column
                            align="right"
                            label="余额"
                            prop="balance"
                            width="120"
                        >
                            <template #default="{ row }">
                                <span :class="{ 'text-success': row.balance > 0, 'text-danger': row.balance < 0 }">
                                    ¥{{ row.balance }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            align="right"
                            label="冻结金额"
                            prop="frozen_amount"
                            width="120"
                        >
                            <template #default="{ row }">
                                ¥{{ row.frozen_amount }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            align="right"
                            label="总收入"
                            prop="total_income"
                            width="120"
                        >
                            <template #default="{ row }">
                                ¥{{ row.total_income }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            align="right"
                            label="总支出"
                            prop="total_expense"
                            width="120"
                        >
                            <template #default="{ row }">
                                ¥{{ row.total_expense }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            align="center"
                            label="状态"
                            prop="status"
                            width="100"
                        >
                            <template #default="{ row }">
                                <el-tag
                                    :type="row.status === 1 ? 'success' : 'danger'"
                                    size="small"
                                >
                                    {{ row.status === 1 ? '正常' : '冻结' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="创建时间"
                            prop="created_at"
                            sortable
                            width="180"
                        />
                        <el-table-column
                            align="center"
                            fixed="right"
                            label="操作"
                            width="250"
                        >
                            <template #default="{ row }">
                                <el-button
                                    size="small"
                                    type="primary"
                                    @click="handleView(row)"
                                >
                                    查看
                                </el-button>
                                <el-button
                                    size="small"
                                    type="success"
                                    @click="handleAdjust(row)"
                                >
                                    调整
                                </el-button>
                                <el-button
                                    :type="row.status === 1 ? 'warning' : 'info'"
                                    size="small"
                                    @click="handleToggleStatus(row)"
                                >
                                    {{ row.status === 1 ? '冻结' : '解冻' }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="pagination-wrapper">
                        <el-pagination
                            v-model:current-page="pagination.currentPage"
                            v-model:page-size="pagination.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="pagination.total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </el-card>
            </div>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'

export default {
    name: 'AdminWalletWalletIndexPage',
    components: {
        BackendPageListLayout
    },
    data() {
        return {
            loading: false,
            refreshing: false,
            showEmptyState: false,
            tableData: [],
            selectedRows: [],
            searchForm: {
                keyword: '',
                type: '',
                status: ''
            },
            pagination: {
                currentPage: 1,
                pageSize: 20,
                total: 0
            },
            typeMap: {
                'personal': '个人钱包',
                'business': '企业钱包',
                'merchant': '商户钱包',
                'system': '系统钱包'
            }
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 获取类型名称
        getTypeName(type) {
            return this.typeMap[type] || type
        },

        // 加载数据
        async loadData() {
            try {
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟钱包数据
                const mockData = [
                    {
                        id: 1,
                        user_name: '张三',
                        wallet_no: 'WLT202401150001',
                        type: 'personal',
                        balance: 1580.50,
                        frozen_amount: 0.00,
                        total_income: 5680.50,
                        total_expense: 4100.00,
                        status: 1,
                        created_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        user_name: '北京科技有限公司',
                        wallet_no: 'WLT202401150002',
                        type: 'business',
                        balance: 25680.00,
                        frozen_amount: 1000.00,
                        total_income: 156800.00,
                        total_expense: 131120.00,
                        status: 1,
                        created_at: '2024-01-15 09:45:00'
                    },
                    {
                        id: 3,
                        user_name: '李四商户',
                        wallet_no: 'WLT202401140001',
                        type: 'merchant',
                        balance: 8950.30,
                        frozen_amount: 500.00,
                        total_income: 23450.30,
                        total_expense: 14500.00,
                        status: 1,
                        created_at: '2024-01-14 16:20:00'
                    },
                    {
                        id: 4,
                        user_name: '王五',
                        wallet_no: 'WLT202401140002',
                        type: 'personal',
                        balance: -150.00,
                        frozen_amount: 0.00,
                        total_income: 2350.00,
                        total_expense: 2500.00,
                        status: 0,
                        created_at: '2024-01-14 14:15:00'
                    },
                    {
                        id: 5,
                        user_name: '系统钱包',
                        wallet_no: 'WLT202401130001',
                        type: 'system',
                        balance: 500000.00,
                        frozen_amount: 0.00,
                        total_income: 1000000.00,
                        total_expense: 500000.00,
                        status: 1,
                        created_at: '2024-01-13 11:30:00'
                    }
                ]

                this.tableData = mockData
                this.pagination.total = 128 // 模拟总数
                this.showEmptyState = this.tableData.length === 0

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.loading = false
            }
        },

        // 刷新数据
        async handleRefresh() {
            try {
                this.refreshing = true
                await this.loadData()
                this.$message.success('刷新成功')
            } catch (error) {
                console.error('刷新失败:', error)
                this.$message.error('刷新失败，请重试')
            } finally {
                this.refreshing = false
            }
        },

        // 创建钱包
        handleCreate() {
            this.$message.info('新增钱包功能待实现')
        },

        // 搜索
        handleSearch() {
            this.pagination.currentPage = 1
            this.loadData()
        },

        // 重置搜索
        handleReset() {
            this.searchForm.keyword = ''
            this.searchForm.type = ''
            this.searchForm.status = ''
            this.handleSearch()
        },

        // 查看钱包详情
        handleView(row) {
            this.$message.info(`查看钱包详情: ${row.wallet_no}`)
        },

        // 调整余额
        handleAdjust(row) {
            this.$message.info(`调整钱包余额: ${row.wallet_no}`)
        },

        // 切换状态
        async handleToggleStatus(row) {
            const action = row.status === 1 ? '冻结' : '解冻'
            try {
                await this.$confirm(
                    `确定要${action}钱包 "${row.wallet_no}" 吗？`,
                    `确认${action}`,
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )

                this.$message.success(`${action}成功`)
                this.loadData()
            } catch {
                this.$message.info(`已取消${action}`)
            }
        },

        // 批量冻结
        async handleBatchFreeze() {
            if (!this.selectedRows.length) {
                this.$message.warning('请选择要冻结的钱包')
                return
            }

            // 检查是否都是正常状态
            const canFreeze = this.selectedRows.every(row => row.status === 1)
            if (!canFreeze) {
                this.$message.warning('只能冻结正常状态的钱包')
                return
            }

            try {
                await this.$confirm(
                    `确定要冻结选中的 ${this.selectedRows.length} 个钱包吗？`,
                    '确认批量冻结',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )

                this.$message.success('批量冻结成功')
                this.selectedRows = []
                this.loadData()
            } catch {
                this.$message.info('已取消冻结')
            }
        },

        // 导出数据
        handleExport() {
            this.$message.info('导出钱包数据功能待实现')
        },

        // 选择变化
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        // 分页大小变化
        handleSizeChange(size) {
            this.pagination.pageSize = size
            this.loadData()
        },

        // 当前页变化
        handleCurrentChange(page) {
            this.pagination.currentPage = page
            this.loadData()
        }
    }
}
</script>

<style lang="scss" scoped>
.admin-wallet-wallet-page {
    .content-area {
        .data-card {
            border-radius: 8px;

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 600;
                color: #1f2937;
            }

            .search-section {
                margin-bottom: 16px;
                padding: 16px;
                background-color: #f8f9fa;
                border-radius: 6px;

                .search-form {
                    margin-bottom: 0;
                }
            }

            .table-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding-bottom: 16px;
                border-bottom: 1px solid #ebeef5;

                .toolbar-left,
                .toolbar-right {
                    display: flex;
                    gap: 8px;
                }
            }

            .admin-table {
                margin-bottom: 20px;

                .text-success {
                    color: #67c23a;
                    font-weight: 600;
                }

                .text-danger {
                    color: #f56c6c;
                    font-weight: 600;
                }
            }

            .pagination-wrapper {
                display: flex;
                justify-content: flex-end;
                padding-top: 16px;
                border-top: 1px solid #ebeef5;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .admin-wallet-wallet-page {
        .search-form {
            .el-form-item {
                width: 100%;
                margin-right: 0;
            }
        }

        .table-toolbar {
            flex-direction: column;
            gap: 12px;
            align-items: stretch !important;

            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
        }

        .pagination-wrapper {
            justify-content: center;
        }
    }
}
</style>
