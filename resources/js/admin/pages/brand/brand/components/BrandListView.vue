<!--
/**
 * 品牌列表视图组件
 *
 * 功能特性：
 * - 品牌数据表格展示
 * - 状态标签和操作按钮
 * - 多选和批量操作支持
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="brand-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
            @sort-change="handleSortChange"
        >
            <!-- 多选列 -->
            <el-table-column
                align="center"
                type="selection"
                width="55"
            />

            <!-- 品牌ID -->
            <el-table-column
                align="center"
                label="ID"
                prop="id"
                sortable="custom"
                width="80"
            />

            <!-- 品牌信息 -->
            <el-table-column
                label="品牌信息"
                min-width="200"
            >
                <template #default="{ row }">
                    <div class="brand-info">
                        <div class="brand-name">{{ row.brand_name }}</div>
                        <div class="brand-code">代码: {{ row.brand_code }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 品牌类型 -->
            <el-table-column
                align="center"
                label="品牌类型"
                prop="brand_type"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getBrandTypeTagType(row.brand_type)"
                        size="small"
                    >
                        {{ getBrandTypeLabel(row.brand_type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 联系人信息 -->
            <el-table-column
                label="联系人信息"
                min-width="180"
            >
                <template #default="{ row }">
                    <div class="contact-info">
                        <div class="contact-name">{{ row.contact_person }}</div>
                        <div class="contact-phone">{{ row.contact_phone }}</div>
                        <div class="contact-email">{{ row.contact_email }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusTagType(row.status)"
                        size="small"
                    >
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column
                align="center"
                label="创建时间"
                prop="created_at"
                sortable="custom"
                width="160"
            >
                <template #default="{ row }">
                    {{ formatDateTime(row.created_at) }}
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="200"
            >
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            详情
                        </el-button>
                        <el-button
                            size="small"
                            type="warning"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>
                        <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
                            <el-button size="small" type="info">
                                更多
                                <i class="fas fa-chevron-down"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="approve"
                                    >
                                        <i class="fas fa-check"></i>
                                        审核通过
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'active'"
                                        command="disable"
                                    >
                                        <i class="fas fa-ban"></i>
                                        禁用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'disabled'"
                                        command="enable"
                                    >
                                        <i class="fas fa-check-circle"></i>
                                        启用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="archive"
                                        divided
                                    >
                                        <i class="fas fa-archive"></i>
                                        归档
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="delete"
                                        style="color: #f56c6c;"
                                    >
                                        <i class="fas fa-trash"></i>
                                        删除
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'BrandListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'data-change',
        'view-detail',
        'edit',
        'status-change',
        'delete'
    ],
    data() {
        return {
            // 品牌类型标签映射
            brandTypeLabels: {
                'own': '自主品牌',
                'agent': '代理品牌',
                'cooperation': '合作品牌'
            },
            // 状态标签映射
            statusLabels: {
                'active': '活跃',
                'pending': '待审核',
                'disabled': '已禁用',
                'archived': '已归档'
            }
        }
    },
    methods: {
        // 获取品牌类型标签
        getBrandTypeLabel(type) {
            return this.brandTypeLabels[type] || type
        },

        // 获取品牌类型标签样式
        getBrandTypeTagType(type) {
            const typeMap = {
                'own': 'success',
                'agent': 'warning',
                'cooperation': 'info'
            }
            return typeMap[type] || 'info'
        },

        // 获取状态标签
        getStatusLabel(status) {
            return this.statusLabels[status] || status
        },

        // 获取状态标签样式
        getStatusTagType(status) {
            const statusMap = {
                'active': 'success',
                'pending': 'warning',
                'disabled': 'danger',
                'archived': 'info'
            }
            return statusMap[status] || 'info'
        },

        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return '-'
            return new Date(dateTime).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            })
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 处理排序变化
        handleSortChange(sortInfo) {
            console.log('排序变化:', sortInfo)
            // TODO: 实现排序逻辑
        },

        // 处理查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 处理编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 处理下拉菜单命令
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'approve':
                    this.handleStatusChange(row, 'active')
                    break
                case 'disable':
                    this.handleStatusChange(row, 'disabled')
                    break
                case 'enable':
                    this.handleStatusChange(row, 'active')
                    break
                case 'archive':
                    this.handleStatusChange(row, 'archived')
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        },

        // 处理状态变更
        handleStatusChange(row, newStatus) {
            this.$emit('status-change', row, newStatus)
        },

        // 处理删除
        handleDelete(row) {
            this.$emit('delete', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.brand-list-view {
    .brand-info {
        .brand-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .brand-code {
            font-size: 12px;
            color: #666;
        }
    }

    .contact-info {
        .contact-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .contact-phone,
        .contact-email {
            font-size: 12px;
            color: #666;
            margin-bottom: 1px;
        }
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;

        .el-button {
            margin: 0;
        }
    }

    :deep(.el-table) {
        .el-table__header {
            th {
                background-color: #f8f9fa;
                color: #333;
                font-weight: 600;
            }
        }

        .el-table__row {
            &:hover {
                background-color: #f5f7fa;
            }
        }
    }
}
</style>
