<?php

namespace App\Http\Resources\Advocate\Retail;

use App\Http\Resources\Advocate\Shop\ShopResource;
use App\Models\RetailCenter\Retail;
use App\Models\ShopCenter\Shop;
use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RetailResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $ali1688_basic = $this->ali1688_basic;
        $shop = isset($this->shop) ? new ShopResource($this->shop) : null;
        //
        $Shop = Shop::where('shop_no', $this->retail_no)->first();
        //
        if (isset($Shop)) {
            //
            $Retail = Retail::where('id', $this->id)->update([
                'shop_id' => $Shop->id
            ]);
        }
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'source_id' => $this->source_id ?? null,
            'source_label' => $this->source_id->label ?? null,
            'source_name' => $this->source_name ?? null,
            'retail_no' => $this->retail_no ?? null,
            'retail_name' => $this->retail_name ?? null,
            //
            'login_id' => $ali1688_basic->loginId ?? null,
            'maturity' => $ali1688_basic->maturity ?? null,
            'trust_score' => $ali1688_basic->trustScore ?? null,
            'user_id' => $ali1688_basic->userId ?? null,
            'create_date' => $ali1688_basic->createDate ?? null,
            'rate_num' => $ali1688_basic->rateNum ?? null,
            'gmt_paid_join' => $ali1688_basic->gmtPaidJoin ?? null,
            'category_id' => $ali1688_basic->categoryId ?? null,
            'company_name' => $ali1688_basic->companyName ?? $shop->company_name ?? null,
            'homepage_url' => $ali1688_basic->homepageUrl ?? $ali1688_basic->homepage_url ?? null,
            'sale_keywords' => $ali1688_basic->saleKeywords ?? null,
            'member_biz_type' => $ali1688_basic->memberBizType ?? null,
            'rate_sum' => $ali1688_basic->rateSum ?? null,
            'domain_in_platforms' => $ali1688_basic->domainInPlatforms ?? null,
            'member_id' => $ali1688_basic->memberId ?? null,
            'shop_url' => $ali1688_basic->shopUrl ?? null,
            'supplier_name' => $ali1688_basic->supplierName ?? null,
            'phone_no' => $ali1688_basic->phoneNo ?? null,
            'product' => $ali1688_basic->product ?? null,
            'mobile_no' => $ali1688_basic->mobileNo ?? null,
            'address_location' => $ali1688_basic->addressLocation ?? null,
            'seller_name' => $ali1688_basic->sellerName ?? null,
            'pm' => $ali1688_basic->pm ?? null,
            'fm' => $ali1688_basic->fm ?? null,
            'is_offical_logistics' => $ali1688_basic->isOfficalLogistics ?? null,
            'open_uid' => $ali1688_basic->openUid ?? null,
            //
            'company_address' => $shop->company_address ?? null,
            'company_city' => $shop->company_city ?? null,
            'company_province' => $shop->company_province ?? null,
            'compliance_rate' => $shop->compliance_rate ?? null,
            'composite_score' => $shop->composite_score ?? null,
            'fuzzy_pay_amt_3m' => $shop->fuzzy_pay_amt_3m ?? null,
            'pay_mord_cnt_3m' => $shop->pay_mord_cnt_3m ?? null,
            'pay_ord_amt_3m' => $shop->pay_ord_amt_3m ?? null,
            'repeat_rate' => $shop->repeat_rate ?? null,
            'sale_quantity_3m' => $shop->sale_quantity_3m ?? null,
            'shop_code' => $shop->shop_code ?? null,
            'shop_domain' => $shop->shop_domain ?? null,
            'retail_icon' => $shop->shop_icon ?? (isset($ali1688_basic->icon) ? 'https://cbu01.alicdn.com' . $ali1688_basic->icon : null),
            'shop_name' => $shop->shop_name ?? null,
            'shop_no' => $shop->shop_no ?? null,
            'shop_real' => $shop->shop_real ?? null,
            'tp_member' => $shop->tp_member ?? null,
            'tp_num' => $shop->tp_num ?? null,
            'tp_year' => $ali1688_basic->tpYear ?? $shop->tp_year ?? null,
            //
            'shop' => $shop ?? null,
        ]);
    }
}
