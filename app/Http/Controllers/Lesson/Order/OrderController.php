<?php

namespace App\Http\Controllers\Lesson\Order;

use App\Http\Controllers\BaseController;
use Exception;
use Illuminate\Http\Request;

/**
 * 课程订单控制器
 * 管理课程订单的 CRUD 操作，包括：
 * - 订单列表查看
 * - 订单详情查看
 * - 订单状态更新
 * - 订单搜索和过滤
 */
class OrderController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 设置视图前缀
        $this->viewPrefix = 'lesson.order';

        // 设置路由前缀
        $this->routePrefix = 'lesson.order';

        // 设置每页显示数量
        $this->perPage = 20;
    }

    /**
     * 显示订单列表
     */
    public function index(Request $request)
    {
        try {
            // 模拟订单数据
            $orders = $this->getMockOrders($request);

            if ($request->expectsJson()) {
                return $this->successResponse($orders, '获取订单列表成功');
            }

            return view('lesson', [
                'title' => '订单管理',
                'description' => '课程订单管理系统',
                'orders' => $orders,
                'menuActive' => 'lesson.order'
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '获取订单列表失败');
        }
    }

    /**
     * 获取模拟订单列表
     */
    private function getMockOrders(Request $request): array
    {
        return [
            'data' => [
                [
                    'id' => 1,
                    'order_no' => 'ORD202412200001',
                    'customer_name' => '张三',
                    'product_name' => 'PHP高级开发课程',
                    'amount' => 1999.00,
                    'status' => 'paid',
                    'created_at' => '2024-12-20 10:30:00'
                ],
                [
                    'id' => 2,
                    'order_no' => 'ORD202412200002',
                    'customer_name' => '李四',
                    'product_name' => 'Vue.js实战课程',
                    'amount' => 1599.00,
                    'status' => 'pending',
                    'created_at' => '2024-12-20 11:15:00'
                ]
            ],
            'total' => 2,
            'per_page' => $this->perPage,
            'current_page' => 1
        ];
    }

    /**
     * 显示创建订单表单
     */
    public function create(Request $request)
    {
        if ($request->expectsJson()) {
            return $this->successResponse([
                'products' => $this->getAvailableProducts(),
                'customers' => $this->getCustomers()
            ], '获取创建表单数据成功');
        }

        return view('lesson', [
            'title' => '创建订单',
            'description' => '创建新的课程订单',
            'menuActive' => 'lesson.order'
        ]);
    }

    /**
     * 获取可用产品列表
     */
    private function getAvailableProducts(): array
    {
        return [
            ['id' => 1, 'name' => 'PHP高级开发课程', 'price' => 1999.00],
            ['id' => 2, 'name' => 'Vue.js实战课程', 'price' => 1599.00],
            ['id' => 3, 'name' => 'Laravel框架课程', 'price' => 2299.00]
        ];
    }

    /**
     * 获取客户列表
     */
    private function getCustomers(): array
    {
        return [
            ['id' => 1, 'name' => '张三', 'phone' => '13800138000'],
            ['id' => 2, 'name' => '李四', 'phone' => '13800138001'],
            ['id' => 3, 'name' => '王五', 'phone' => '13800138002']
        ];
    }

    /**
     * 存储新订单
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $this->validateStoreRequest($request);

            // 这里应该创建实际的订单
            $order = [
                'id' => rand(1000, 9999),
                'order_no' => 'ORD' . date('YmdHis') . rand(100, 999),
                'customer_name' => $validatedData['customer_name'] ?? '测试客户',
                'product_name' => $validatedData['product_name'] ?? '测试课程',
                'amount' => $validatedData['amount'] ?? 0,
                'status' => 'pending',
                'created_at' => now()
            ];

            if ($request->expectsJson()) {
                return $this->successResponse($order, '订单创建成功', 201);
            }

            return redirect()->route('lesson.order.index')
                ->with('success', '订单创建成功');

        } catch (Exception $e) {
            return $this->handleException($e, '订单创建失败');
        }
    }

    /**
     * 验证存储请求
     */
    protected function validateStoreRequest(Request $request): array
    {
        return $request->validate([
            'customer_name' => 'required|string|max:255',
            'product_name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string'
        ]);
    }

    /**
     * 显示订单详情
     */
    public function show(Request $request, $id)
    {
        try {
            $order = $this->getMockOrder($id);

            if ($request->expectsJson()) {
                return $this->successResponse($order, '获取订单详情成功');
            }

            return view('lesson', [
                'title' => '订单详情',
                'description' => "订单 #{$order['order_no']} 的详细信息",
                'order' => $order,
                'menuActive' => 'lesson.order'
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '获取订单详情失败');
        }
    }

    /**
     * 获取模拟订单详情
     */
    private function getMockOrder($id): array
    {
        return [
            'id' => $id,
            'order_no' => 'ORD202412200001',
            'customer_name' => '张三',
            'customer_phone' => '13800138000',
            'customer_email' => '<EMAIL>',
            'product_name' => 'PHP高级开发课程',
            'product_price' => 1999.00,
            'quantity' => 1,
            'amount' => 1999.00,
            'status' => 'paid',
            'payment_method' => 'wechat',
            'notes' => '学员要求加急处理',
            'created_at' => '2024-12-20 10:30:00',
            'updated_at' => '2024-12-20 10:35:00'
        ];
    }

    /**
     * 显示编辑订单表单
     */
    public function edit(Request $request, $id)
    {
        try {
            $order = $this->getMockOrder($id);

            if ($request->expectsJson()) {
                return $this->successResponse([
                    'order' => $order,
                    'products' => $this->getAvailableProducts(),
                    'customers' => $this->getCustomers()
                ], '获取编辑表单数据成功');
            }

            return view('lesson', [
                'title' => '编辑订单',
                'description' => "编辑订单 #{$order['order_no']}",
                'order' => $order,
                'menuActive' => 'lesson.order'
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '获取编辑表单失败');
        }
    }

    /**
     * 更新订单
     */
    public function update(Request $request, $id)
    {
        try {
            $validatedData = $this->validateUpdateRequest($request, $id);

            // 这里应该更新实际的订单
            $order = $this->getMockOrder($id);
            $order = array_merge($order, $validatedData);
            $order['updated_at'] = now();

            if ($request->expectsJson()) {
                return $this->successResponse($order, '订单更新成功');
            }

            return redirect()->route('lesson.order.index')
                ->with('success', '订单更新成功');

        } catch (Exception $e) {
            return $this->handleException($e, '订单更新失败');
        }
    }

    /**
     * 验证更新请求
     */
    protected function validateUpdateRequest(Request $request, $item): array
    {
        return $request->validate([
            'customer_name' => 'sometimes|required|string|max:255',
            'product_name' => 'sometimes|required|string|max:255',
            'amount' => 'sometimes|required|numeric|min:0',
            'status' => 'sometimes|required|in:pending,paid,completed,cancelled',
            'notes' => 'nullable|string'
        ]);
    }

    /**
     * 删除订单
     */
    public function destroy(Request $request, $id)
    {
        try {
            // 这里应该删除实际的订单

            if ($request->expectsJson()) {
                return $this->successResponse([], '订单删除成功');
            }

            return redirect()->route('lesson.order.index')
                ->with('success', '订单删除成功');

        } catch (Exception $e) {
            return $this->handleException($e, '订单删除失败');
        }
    }
}
