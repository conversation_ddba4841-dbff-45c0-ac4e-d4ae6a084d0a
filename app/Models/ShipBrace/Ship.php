<?php

namespace App\Models\ShipBrace;

use App\Enums\Is\IsActivationEnum;
use App\Enums\Is\IsCheckEnum;
use App\Enums\Is\IsConfirmEnum;
use App\Enums\Is\IsDefaultEnum;
use App\Enums\Is\IsRequiredEnum;
use App\Enums\Is\IsStateEnum;
use App\Enums\Time\ShipmentTimeEnum;
use App\Enums\Type\CampaignTypeEnum;
use App\Enums\Type\CpTypeEnum;
use App\Enums\Type\DelayTypeEnum;
use App\Enums\Type\PackTypeEnum;
use App\Enums\Type\PartTypeEnum;
use App\Enums\Type\PaymentTypeEnum;
use App\Enums\Type\PolicyTypeEnum;
use App\Enums\Type\PrintTypeEnum;
use App\Enums\Type\ServiceTypeEnum;
use App\Enums\Type\WaybillTypeEnum;
use App\Enums\Vest\ConductVestEnum;
use App\Enums\Vest\ReachVestEnum;
use App\Enums\Vest\SheetVestEnum;
use App\Enums\Vest\SourceVestEnum;
use App\Models\SiteBrace\Site;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ship extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'ship_brace';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'delay_type' => DelayTypeEnum::class,
        'shipment_time' => ShipmentTimeEnum::class,
        'cp_type' => CpTypeEnum::class,
        'service_type' => ServiceTypeEnum::class,
        'waybill_type' => WaybillTypeEnum::class,
        'source_vest' => SourceVestEnum::class,
        'sheet_vest' => SheetVestEnum::class,
        'payment_type' => PaymentTypeEnum::class,
        'print_type' => PrintTypeEnum::class,
        'part_type' => PartTypeEnum::class,
        'policy_type' => PolicyTypeEnum::class,
        'campaign_type' => CampaignTypeEnum::class,
        'pack_type' => PackTypeEnum::class,
        'is_required' => IsRequiredEnum::class,
        'shipment_vest' => ReachVestEnum::class,
        'conduct_vest' => ConductVestEnum::class,
        'is_default' => IsDefaultEnum::class,
        'is_confirm' => IsConfirmEnum::class,
        'is_activation' => IsActivationEnum::class,
        'is_check' => IsCheckEnum::class,
        'is_state' => IsStateEnum::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 一对一关联到 ShipPart
    public function farePart()
    {
        //
        return $this->belongsTo(ShipPart::class, 'fare_id');
    }

    public function packPart()
    {
        //
        return $this->belongsTo(ShipPart::class, 'pack_id');
    }

    public function carryPart()
    {
        return $this->belongsTo(ShipPart::class, 'carry_id');
    }

    public function wrapPart()
    {
        //
        return $this->belongsTo(ShipPart::class, 'wrap_id');
    }

    public function boxPart()
    {
        //
        return $this->belongsTo(ShipPart::class, 'box_id');
    }

    public function ship_parts()
    {
        return $this->belongsToMany(ShipPart::class, 'ship_brace.ship_by_ship_part', 'ship_id', 'ship_part_id');
    }

    /**
     * @return BelongsTo
     */
    public function site()
    {
        //
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
