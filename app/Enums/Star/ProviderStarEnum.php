<?php

namespace App\Enums\Star;

use App\Enums\BaseEnum;


final class ProviderStarEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'PROCESSING' => 1,
            'SHIPPED' => 2,
            'DELIVERED' => 3,
            'CANCELLED' => 4,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'PROCESSING' => '处理中',
            'SHIPPED' => '已发货',
            'DELIVERED' => '已送达',
            'CANCELLED' => '已取消',
        ];
    }
}
