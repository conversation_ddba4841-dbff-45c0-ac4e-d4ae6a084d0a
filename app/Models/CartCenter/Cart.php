<?php

namespace App\Models\CartCenter;

use App\Models\ProductCenter\Product;
use App\Models\ProductCenter\ProductSku;
use App\Models\StoreCenter\Store;
use App\Models\UserCenter\User;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Cart extends Model
{
    use GeneratesUuid;
    use HasFactory;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'cart_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function user()
    {
        //
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function store()
    {
        //
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function product()
    {
        //
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function sku()
    {
        //
        return $this->belongsTo(ProductSku::class, 'sku_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}

