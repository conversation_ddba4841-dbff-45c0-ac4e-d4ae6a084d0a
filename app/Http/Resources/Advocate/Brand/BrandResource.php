<?php

namespace App\Http\Resources\Advocate\Brand;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BrandResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'brand_name' => $this->brand_name ?? null,
            'value' => $this->brand_name ?? null,
            'brand_english_name' => $this->brand_english_name ?? null,
        ]);
    }
}
