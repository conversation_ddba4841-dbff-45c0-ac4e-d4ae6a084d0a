<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * 客户状态枚举
 * @package App\Enums\State
 * @method static self default()
 * @method static self active()
 * @method static self new()
 * @method static self vip()
 * @method static self long_term()
 * @method static self contract_signed()
 * @method static self renewed()
 * @method static self high_value()
 * @method static self low_value()
 * @method static self closed()
 * @method static self lead()
 * @method static self prospect()
 * @method static self opportunity()
 * @method static self converted()
 * @method static self negotiation()
 * @method static self contract_pending()
 * @method static self pending_renewal()
 * @method static self pending_payment()
 * @method static self closed_lost()
 * @method static self closed_won()
 * @method static self at_risk()
 * @method static self high_risk()
 * @method static self low_risk()
 * @method static self delinquent()
 * @method static self suspended()
 * @method static self on_hold()
 * @method static self under_review()
 * @method static self pending()
 * @method static self follow_up_required()
 * @method static self awaiting_approval()
 * @method static self disengaged()
 * @method static self churned()
 * @method static self lost_opportunity()
 * @method static self archived()
 * @method static self former_client()
 * @method static self temporary()
 * @method static self trial()
 * @method static self referral()
 * @method static self partner()
 * @method static self referred_out()
 * @method static self dormant()
 * @method static self reengaged()
 * @method static self prospecting()
 * @method static self strategic()
 * @method static self blacklisted()
 * @method static self evaluation()
 * @method static self pending_approval()
 * @method static self recontact_required()
 * @method static self legal_hold()
 */
final class CustomerStateEnum extends BaseEnum
{
    // 定义状态值
    /**
     * 获取状态值的标签
     * @param string $state
     * @return string
     */
    public static function getLabel(string $state): string
    {
        $labels = static::labels();
        return $labels[$state] ?? '未知状态';
    }

    // 定义状态标签

    protected static function labels(): array
    {
        return [
            // 默认和活跃状态 1-10
            'default' => '默认状态',
            'active' => '活跃客户',
            'new' => '新客户',
            'vip' => '重要客户',
            'long_term' => '长期客户',
            'contract_signed' => '合同已签署',
            'renewed' => '合同续签客户',
            'high_value' => '高价值客户',
            'low_value' => '低价值客户',
            'closed' => '合作结束',

            // 业务状态 11-20
            'lead' => '潜在客户',
            'prospect' => '有意向客户',
            'opportunity' => '商机客户',
            'converted' => '已转化客户',
            'negotiation' => '谈判中客户',
            'contract_pending' => '合同待签署',
            'pending_renewal' => '待续签',
            'pending_payment' => '待付款',
            'closed_lost' => '已关闭且失去',
            'closed_won' => '已关闭且赢得',

            // 风险状态 21-30
            'at_risk' => '存在风险的客户',
            'high_risk' => '高风险客户',
            'low_risk' => '低风险客户',
            'delinquent' => '拖欠款项的客户',
            'suspended' => '暂停合作',
            'on_hold' => '挂起客户',
            'under_review' => '审核中客户',
            'pending' => '待处理客户',
            'follow_up_required' => '需要跟进的客户',
            'awaiting_approval' => '等待审批的客户',

            // 特殊处理状态 31-40
            'disengaged' => '失去联系的客户',
            'churned' => '流失客户',
            'lost_opportunity' => '已失去的商机',
            'archived' => '已归档客户',
            'former_client' => '前客户',
            'temporary' => '临时客户',
            'trial' => '试用期客户',
            'referral' => '推荐客户',
            'partner' => '合作伙伴',
            'referred_out' => '转推荐出去的客户',

            // 挖掘状态 41-50
            'dormant' => '休眠客户',
            'reengaged' => '重新激活客户',
            'prospecting' => '处于挖掘阶段的客户',
            'strategic' => '战略客户',
            'blacklisted' => '黑名单客户',
            'evaluation' => '评估中客户',
            'pending_approval' => '等待批准的客户',
            'recontact_required' => '需要重新联系的客户',
            'legal_hold' => '法律保留的客户',
        ];
    }

    protected static function values(): array
    {
        return [
            // 默认和活跃状态 1-10
            'default' => 1,    // 默认状态
            'active' => 2,    // 活跃客户
            'new' => 3,    // 新客户
            'vip' => 4,    // 重要客户
            'long_term' => 5,    // 长期客户
            'contract_signed' => 6,    // 合同已签署
            'renewed' => 7,    // 合同续签客户
            'high_value' => 8,    // 高价值客户
            'low_value' => 9,    // 低价值客户
            'closed' => 10,   // 合作结束

            // 业务状态 11-20
            'lead' => 11,   // 潜在客户
            'prospect' => 12,   // 有意向客户
            'opportunity' => 13,   // 商机客户
            'converted' => 14,   // 已转化客户
            'negotiation' => 15,   // 谈判中客户
            'contract_pending' => 16,   // 合同待签署
            'pending_renewal' => 17,   // 待续签
            'pending_payment' => 18,   // 待付款
            'closed_lost' => 19,   // 已关闭且失去
            'closed_won' => 20,   // 已关闭且赢得

            // 风险状态 21-30
            'at_risk' => 21,   // 存在风险的客户
            'high_risk' => 22,   // 高风险客户
            'low_risk' => 23,   // 低风险客户
            'delinquent' => 24,   // 拖欠款项的客户
            'suspended' => 25,   // 暂停合作
            'on_hold' => 26,   // 挂起客户
            'under_review' => 27,   // 审核中客户
            'pending' => 28,   // 待处理客户
            'follow_up_required' => 29,   // 需要跟进的客户
            'awaiting_approval' => 30,   // 等待审批的客户

            // 特殊处理状态 31-40
            'disengaged' => 31,   // 失去联系的客户
            'churned' => 32,   // 流失客户
            'lost_opportunity' => 33,   // 已失去的商机
            'archived' => 34,   // 已归档客户
            'former_client' => 35,   // 前客户
            'temporary' => 36,   // 临时客户
            'trial' => 37,   // 试用期客户
            'referral' => 38,   // 推荐客户
            'partner' => 39,   // 合作伙伴
            'referred_out' => 40,   // 转推荐出去的客户

            // 挖掘状态 41-50
            'dormant' => 41,   // 休眠客户
            'reengaged' => 42,   // 重新激活客户
            'prospecting' => 43,   // 处于挖掘阶段的客户
            'strategic' => 44,   // 战略客户
            'blacklisted' => 45,   // 黑名单客户
            'evaluation' => 46,   // 评估中客户
            'pending_approval' => 47,   // 等待批准的客户
            'recontact_required' => 48,   // 需要重新联系的客户
            'legal_hold' => 49,   // 法律保留的客户
        ];
    }
}
