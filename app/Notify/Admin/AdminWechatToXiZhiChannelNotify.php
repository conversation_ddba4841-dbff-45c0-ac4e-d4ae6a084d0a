<?php

namespace App\Notify\Admin;

use Guanguans\Notify\XiZhi\Authenticator;
use Guanguans\Notify\XiZhi\Client;
use Guanguans\Notify\XiZhi\Messages\ChannelMessage;
use Guanguans\Notify\XiZhi\Messages\SingleMessage;
use Illuminate\Notifications\Notification;

class AdminWechatToXiZhiChannelNotify
{
    /**
     * @var Client XiZhi 客户端实例，用于发送消息
     */
    protected $client;

    /**
     * 构造函数，初始化 XiZhi 客户端.
     */
    public function __construct()
    {
        // 从配置文件或环境变量中获取认证令牌
        $authToken = env('EXCEPTION_NOTIFY_XIZHI_TOKEN', 'XZ901760bb8c7940eda1beaa838a2b415f');
        // 创建认证器实例，使用提供的认证令牌
        $authenticator = new Authenticator($authToken);
        // 使用认证器创建 XiZhi 客户端实例，用于后续的消息发送
        $this->client = new Client($authenticator);
    }

    /**
     * 发送给定的通知.
     * @param mixed $notifiable 通知的接收者
     * @param Notification $notification 通知实例
     */
    public function send($notifiable, Notification $notification): void
    {
        // 获取通知的 XiZhi 表示形式，通常由 toXiZhi 方法返回
        $data = $notification->toXiZhi($notifiable);

        // 判断消息类型并创建相应的消息实例
        if ($data['type'] === 'single') {
            // 创建单条消息实例
            $message = SingleMessage::make($data['message']);
        } else {
            // 创建频道消息实例
            $message = ChannelMessage::make($data['message']);
        }

        // 使用客户端发送消息
        $this->client->send($message);
    }
}
