<?php

namespace App\Http\Resources\Admin\Category;

use App\Http\Resources\Admin\Produce\ProduceSchemaResource;
use App\Http\Resources\BaseResource;
use App\Models\BrandBrace\Brand;
use App\Models\ProduceBrace\ProduceAccessory;
use App\Models\ProduceBrace\ProduceCraft;
use App\Models\ProduceBrace\ProduceCustom;
use App\Models\ProduceBrace\ProduceDesign;
use App\Models\ProduceBrace\ProduceFabric;
use App\Models\ProduceBrace\ProduceParam;
use App\Models\ProduceBrace\ProducePurpose;
use App\Models\ProduceBrace\ProduceShade;
use App\Models\ProduceBrace\ProduceSpec;
use App\Models\ProduceBrace\ProduceSuit;
use App\Models\ProduceBrace\ProduceTrend;
use Illuminate\Http\Request;

class CategoryReadyProduceSchemaResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'category_uuid' => $this->uuid ?? null,
            'category_name' => $this->category_name ?? null,
            'category_english_name' => $this->category_english_name ?? null,
            'level' => $this->level ?? null,
            'sort' => $this->sort ?? null,
            //
            'schemas' => isset($this->schemas) ? ProduceSchemaResource::collection($this->schemas)->resource : null,
            'suits' => ProduceSuit::where('platform_id', '=', config('app.platform_id'))->where("deep", "=", '1')->get(),
            'specs' => ProduceSpec::where('platform_id', '=', config('app.platform_id'))->where("deep", "=", '1')->get(),
            'customs' => ProduceCustom::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'fabrics' => ProduceFabric::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'designs' => ProduceDesign::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'trends' => ProduceTrend::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'crafts' => ProduceCraft::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'shades' => ProduceShade::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'purposes' => ProducePurpose::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'accessories' => ProduceAccessory::where('platform_id', '=', config('app.platform_id'))->where('deep', '=', '1')->get(),
            'params' => ProduceParam::where('platform_id', '=', config('app.platform_id'))->where("deep", "=", '1')->get(),
            'brands' => Brand::where('platform_id', '=', config('app.platform_id'))->get(),
        ]);
    }
}
