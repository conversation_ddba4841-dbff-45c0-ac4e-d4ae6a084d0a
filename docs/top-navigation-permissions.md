# 顶部导航栏权限配置说明

## 概述

本文档说明如何配置顶部导航栏的权限控制系统，确保用户只能看到和访问他们有权限的管理模块。

## 当前实现

### 1. 权限检查方式

目前在 `resources/views/layouts/partials/top-navigation.blade.php` 中使用了临时的权限检查方式：

```php
@php
    // 临时权限检查 - 可以根据实际需求调整
    $user = Auth::user();
    $showAdmin = true; // 暂时显示所有链接，后续可以根据用户角色调整
    $showLesson = true;
    $showChain = true;
    $showMonitor = true;
@endphp
```

### 2. 管理模块图标优化

每个管理模块现在使用不同的图标：

- **后台管理**: `fas fa-tachometer-alt` (仪表盘图标)
- **运营中心**: `fas fa-graduation-cap` (教育/课程图标)
- **渠道中心**: `fas fa-network-wired` (网络/渠道图标)
- **数据中心**: `fas fa-chart-line` (数据图表图标)
- **个人设置**: `fas fa-user-cog` (用户设置图标)

## 启用 Laratrust 权限系统

### 1. 配置步骤

项目已经配置了 Laratrust 权限系统的基础设置：

1. **用户模型已更新** (`app/Models/UserCenter/User.php`)：
   ```php
   use Laratrust\Contracts\LaratrustUser;
   use Laratrust\Traits\HasRolesAndPermissions;
   
   class User extends Authenticatable implements LaratrustUser
   {
       use HasFactory, Notifiable, HasRolesAndPermissions;
   }
   ```

2. **权限服务提供者已创建** (`app/Providers/AuthServiceProvider.php`)：
   - 定义了各模块的访问权限 Gates
   - 支持基于角色和权限的访问控制

3. **服务提供者已注册** (`bootstrap/app.php`)

### 2. 启用权限检查

要启用基于 Laratrust 的权限检查，请修改 `top-navigation.blade.php` 中的权限检查代码：

```php
@php
    $user = Auth::user();
    
    // 启用 Laratrust 权限检查
    $showAdmin = $user->hasRole(['superadministrator', 'administrator']) || $user->hasPermission('admin-access');
    $showLesson = $user->hasRole(['superadministrator', 'administrator', 'lesson-manager']) || $user->hasPermission('lesson-access');
    $showChain = $user->hasRole(['superadministrator', 'administrator', 'chain-manager']) || $user->hasPermission('chain-access');
    $showMonitor = $user->hasRole(['superadministrator', 'administrator', 'monitor-manager']) || $user->hasPermission('monitor-access');
@endphp
```

### 3. 创建角色和权限

使用 Laratrust 的 Seeder 或手动创建以下角色和权限：

#### 角色 (Roles)
- `superadministrator`: 超级管理员
- `administrator`: 管理员
- `lesson-manager`: 运营中心管理员
- `chain-manager`: 渠道中心管理员
- `monitor-manager`: 数据中心管理员

#### 权限 (Permissions)
- `admin-access`: 后台管理访问权限
- `lesson-access`: 运营中心访问权限
- `chain-access`: 渠道中心访问权限
- `monitor-access`: 数据中心访问权限

## 消息中心配置

### 未读消息数量

消息中心支持显示未读消息数量，当前实现包含以下特性：

1. **安全的数据获取**：使用 try-catch 防止错误
2. **多种数据源支持**：
   - 控制器传递的变量
   - 用户通知系统
   - 缓存或数据库查询

3. **数量显示优化**：
   - 超过99条显示为 "99+"
   - 无消息时不显示徽章

### 配置示例

在控制器中传递未读消息数量：

```php
public function index()
{
    $unreadMessages = Auth::user()->unreadNotifications()->count();
    
    return view('your-view', compact('unreadMessages'));
}
```

## 响应式设计

导航栏已针对不同屏幕尺寸进行优化：

- **大屏幕 (1760px+)**: 显示所有链接和完整文本
- **中等屏幕 (1024px-1759px)**: 显示主要链接，部分文本缩短
- **平板 (768px-1023px)**: 只显示图标，隐藏文本
- **手机 (640px-767px)**: 紧凑布局，隐藏分隔符

## 安全注意事项

1. **权限验证**: 除了前端显示控制，还需要在路由和控制器中进行权限验证
2. **路由保护**: 使用中间件保护敏感路由
3. **数据验证**: 确保用户只能访问有权限的数据

## 后续优化建议

1. **动态权限**: 根据业务需求动态调整权限规则
2. **缓存优化**: 缓存用户权限信息以提高性能
3. **审计日志**: 记录用户访问和权限变更日志
4. **权限管理界面**: 提供可视化的权限管理界面
