<?php

namespace App\Http\Resources\Advocate\ProductDraft;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductDraftResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $store = $this->store ?? null;
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            //
            'store_uuid' => $store->uuid ?? null,
            'schema_uuid' => $this->schema->uuid ?? null,
            //
            'store_name' => $store->store_name ?? null,
            'stall_number' => $store->stall_number ?? null,
            'draft_number' => $store->product_drafts->where('edit_type', 10)->count() ? ($store->product_drafts->where('edit_type', 10)->count()) + 10 : 10,
            //
            'category_name' => $this->category->category_name ?? null,
            'schema_name' => $this->schema->schema_name ?? null,
            'spec_name' => $this->spec->spec_name ?? null,
            //
            'product_no' => $this->product_no ?? null,
            'product_name' => $this->product_name ?? null,
            'product_title' => $this->product_title ?? null,
            'product_english_title' => $this->product_english_title ?? null,
            'product_sn' => $this->product_sn ?? null,
            'product_barcode' => $this->product_barcode ?? null,
            //
            'price' => ($this->min_price === $this->max_price) ? "￥{$this->min_price}" : "￥{$this->min_price}~￥{$this->max_price}",
            //
            //                                'media_image_uuid'      => $this->media_image_uuid ?? null,
            //                                'media_image_uuids'     => $this->media_image_uuids ?? null,
            //                                'media_desc_uuids'      => $this->media_desc_uuids ?? null,
            //                                'media_white_uuid'      => $this->media_white_uuid ?? null,
            //                                'media_video_uuid'      => $this->media_video_uuid ?? null,
            //
            'media_picture_id' => $this->media_picture_id ?? null,
            'media_picture_url' => $this->media_picture_url ?? null,
            'media_picture' => $this->media_picture ?? null,
            'media_images' => $this->media_images ?? null,
            'media_descries' => $this->media_descries ?? null,
            'media_white' => $this->media_white ?? null,
            'media_video' => $this->media_video ?? null,
            'keywords' => $this->keywords ?? null,
            //
            'fabric_ids' => $this->fabric_ids ?? null,
            'design_ids' => $this->design_ids ?? null,
            'trend_ids' => $this->trend_ids ?? null,
            'craft_ids' => $this->craft_ids ?? null,
            'shade_ids' => $this->shade_ids ?? null,
            'purpose_ids' => $this->purpose_ids ?? null,
            'accessory_ids' => $this->accessory_ids ?? null,
            'suit_ids' => $this->suit_ids ?? null,
            'spec_id' => $this->spec_id ?? null,
            'attributes' => $this->attributes ?? null,
            //
            'skus' => $this->skus ?? null,
            'arr_skus' => $this->arr_skus ?? null,
            //
            'arr_suits' => $this->arr_suits ?? null,
            'arr_specs' => $this->arr_specs ?? null,
            'arr_customs' => $this->arr_customs ?? null,
            //
            'arr_suit_ids' => $this->arr_suit_ids ?? null,
            'arr_spec_ids' => $this->arr_spec_ids ?? null,
            'arr_custom_ids' => $this->arr_custom_ids ?? null,
            //
            'suit_number' => isset($this->arr_suits) ? count($this->arr_suits) : null,
            'sku_number' => isset($this->arr_skus) ? count($this->arr_skus) : null,
            //
            'edit_type_label' => $this->edit_type->label ?? null,
            'edit_type_color' => $this->edit_type->color() ?? null,
            //
            'store_state_label' => $store->store_state->label ?? null,
            'store_state_color' => $store->store_state->color() ?? null,
            //
            'is_offline_label' => $this->is_offline->label ?? null,
            'is_offline_color' => $this->is_offline->color() ?? null,
            'is_closure_label' => $this->is_closure->label ?? null,
            'is_closure_color' => $this->is_closure->color() ?? null,
            //
            'product_state_label' => $this->product_state->label ?? null,
            'product_state_color' => $this->product_state->color() ?? null,
            'draft_status_label' => $this->draft_status->label ?? null,
            'draft_status_color' => $this->draft_status->color() ?? null,
        ]);
    }
}
