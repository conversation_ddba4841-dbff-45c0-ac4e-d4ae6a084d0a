<?php

namespace App\Http\Resources\Desktop\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * ProductResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class ProductResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $suits = $this->suits()->get();
        //
        $data = array_filter([
            'uuid' => $this->uuid,
            'product_uuid' => $this->uuid,
            //
            'store_uuid' => $this->store->uuid,
            'store_name' => $this->store->store_name,
            //
            'product_no' => $this->product_no,
            'product_title' => $this->product_title,
            'product_name' => $this->product_name,
            'property_id' => $this->property_id,
            //
            'max_price' => $this->max_price,
            'min_price' => $this->min_price,
            //
            'volume' => $this->volume_number,
            //
            'schema_name' => $this->schema_name,
            'suit_name' => $this->suit_name,
            'theme_name' => $this->theme_name,
            'brand_name' => $this->brand_name,
            // Handles image and video resources, ensuring only an array of URLs is returned
            'product_picture_url' => $this->product_picture_url ?? null,
            //
            'product_suits' => $suits ? ProductSuitResource::collection($suits) : null,
            'keywords' => isset($this->keywords) ? $this->keywords()->select('word')->pluck('word')->toArray() : null,
        ]);
        //
        // 根据 property_id 判断推送按钮的显示
        if ($this->property_id > 7) {
            // 如果 property_id 大于 6，不显示“批量推送”按钮
            $data['batch_push_button'] = false;
        } else {
            $data['batch_push_button'] = true;
        }
        //
        if ($this->property_id > 8) {
            // 如果 property_id 大于 7，不显示“单独推送”按钮
            $data['individual_push_button'] = false;
        } else {
            $data['individual_push_button'] = true;
        }
        // 返回最终的资源数据
        return $data;
    }
}

