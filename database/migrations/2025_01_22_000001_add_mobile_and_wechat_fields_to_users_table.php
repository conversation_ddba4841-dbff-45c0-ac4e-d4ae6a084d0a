<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 用户表添加手机号和微信字段迁移
 * 
 * 为用户表添加手机号验证和微信登录相关字段
 * 支持手机号短信验证码登录注册和微信公众号扫码登录注册
 * 
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
return new class extends Migration
{
    /**
     * 执行迁移
     * 
     * 添加手机号和微信相关字段到用户表
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 手机号相关字段
            $table->string('mobile', 20)->nullable()->unique()->after('email')->comment('手机号');
            $table->timestamp('mobile_verified_at')->nullable()->after('email_verified_at')->comment('手机号验证时间');
            
            // 微信相关字段
            $table->string('mp_openid', 100)->nullable()->unique()->after('mobile_verified_at')->comment('微信公众号OpenID');
            $table->string('mini_openid', 100)->nullable()->unique()->after('mp_openid')->comment('微信小程序OpenID');
            $table->string('unionid', 100)->nullable()->unique()->after('mini_openid')->comment('微信UnionID');
            $table->string('nickname', 100)->nullable()->after('unionid')->comment('微信昵称');
            $table->string('head_img_url')->nullable()->after('nickname')->comment('微信头像URL');
            $table->tinyInteger('sex')->nullable()->after('head_img_url')->comment('性别：0-未知，1-男，2-女');
            $table->string('country', 50)->nullable()->after('sex')->comment('国家');
            $table->string('province', 50)->nullable()->after('country')->comment('省份');
            $table->string('city', 50)->nullable()->after('province')->comment('城市');
            $table->string('language', 20)->nullable()->after('city')->comment('语言');
            
            // 登录相关字段
            $table->timestamp('last_login_at')->nullable()->after('language')->comment('最后登录时间');
            $table->string('last_login_ip', 45)->nullable()->after('last_login_at')->comment('最后登录IP');
            $table->string('last_login_device')->nullable()->after('last_login_ip')->comment('最后登录设备');
            
            // 账户状态字段
            $table->enum('status', ['active', 'inactive', 'suspended', 'banned'])->default('active')->after('last_login_device')->comment('账户状态');
            $table->text('status_reason')->nullable()->after('status')->comment('状态变更原因');
            
            // 添加索引
            $table->index(['mobile'], 'idx_users_mobile');
            $table->index(['mp_openid'], 'idx_users_mp_openid');
            $table->index(['mini_openid'], 'idx_users_mini_openid');
            $table->index(['unionid'], 'idx_users_unionid');
            $table->index(['status'], 'idx_users_status');
            $table->index(['last_login_at'], 'idx_users_last_login_at');
        });
    }

    /**
     * 回滚迁移
     * 
     * 移除添加的字段和索引
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_users_mobile');
            $table->dropIndex('idx_users_mp_openid');
            $table->dropIndex('idx_users_mini_openid');
            $table->dropIndex('idx_users_unionid');
            $table->dropIndex('idx_users_status');
            $table->dropIndex('idx_users_last_login_at');
            
            // 删除字段
            $table->dropColumn([
                'mobile',
                'mobile_verified_at',
                'mp_openid',
                'mini_openid',
                'unionid',
                'nickname',
                'head_img_url',
                'sex',
                'country',
                'province',
                'city',
                'language',
                'last_login_at',
                'last_login_ip',
                'last_login_device',
                'status',
                'status_reason'
            ]);
        });
    }
};
