<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class OrderCpsTypeEnum
 * @package App\Enums\Type\Order
 * @method static self default() 默认
 * @method static self NormalOrder() 普通订单
 * @method static self PromotionOrder() 推广订单
 * @method static self DistributionOrder() 分销订单
 * @method static self AgencyOrder() 代理订单
 */
final class CpsTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'NormalOrder' => 1,
            'PromotionOrder' => 2,
            'DistributionOrder' => 3,
            'AgencyOrder' => 4,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'NormalOrder' => '普通订单',
            'PromotionOrder' => '推广订单',
            'DistributionOrder' => '分销订单',
            'AgencyOrder' => '代理订单',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'default_color',  // default
            1 => 'red',            // NormalOrder
            2 => 'orange',         // PromotionOrder
            3 => 'yellow',         // DistributionOrder
            4 => 'green',          // AgencyOrder
            5 => 'rad',          // AgencyOrder
        ];
    }
}
