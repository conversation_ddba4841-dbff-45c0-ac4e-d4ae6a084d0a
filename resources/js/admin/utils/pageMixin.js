// pageMixin.js
import {ElLoading, ElMessage, ElMessageBox} from 'element-plus'; // 导入 Element Plus 组件
import axios from 'axios'; // 导入 axios 用于发送 HTTP 请求
import debounce from 'lodash/debounce'; // 防抖函数

export const pageMixin = {
    data() {
        return {
            currentPage: 1, // 当前页码
            pageSize: 10, // 每页条数
            total: 0, // 总数据条数
            searchQuery: '', // 搜索关键词
            searchCategory: '', // 搜索分类
            filters: {}, // 高级筛选条件
            sortField: '', // 排序字段
            sortOrder: '', // 排序顺序
            dataList: [], // 存储当前页的数据
            selectedRows: [], // 批量操作时选中的行
            errorMessage: '', // 错误信息
            retryCount: 0, // 请求失败重试次数
            items: {
                current_page: 1, // 当前页码
                per_page: 60, // 优先使用外部传入的 perPage，若没有传入则使用默认值
                total: 0, // 总条目数
                data: [] // 当前页的数据
            },
            v_loading: true, // 页面加载状态，控制加载动画
            loading: true, // 数据加载状态，控制加载动画
            // 分页跳转支持
            jumpPage: '', // 用于保存跳转的页码
        };
    },
    methods: {
        /**
         * 获取数据
         * 这个方法会向后台发送请求获取数据，加载时显示加载动画
         */
        async fetchData() {
            this.loading = true; // 设置加载状态为 true，显示加载动画
            this.errorMessage = ''; // 清除上次的错误信息

            const loadingInstance = ElLoading.service({
                fullscreen: true, // 加载时覆盖整个屏幕
                text: '加载中...', // 加载提示文本
            });

            try {
                const response = await this.fetchDataFromApi({
                    page: this.currentPage, // 当前页码
                    pageSize: this.pageSize, // 每页条数
                    searchQuery: this.searchQuery, // 搜索关键词
                    filters: this.filters, // 高级筛选条件
                    sortField: this.sortField, // 排序字段
                    sortOrder: this.sortOrder, // 排序顺序
                });

                if (response && response.data) {
                    this.dataList = response.data.items;
                    this.total = response.data.total;
                }
            } catch (error) {
                this.errorMessage = '数据请求失败，请稍后再试。'; // 设置错误信息
                ElMessage.error(this.errorMessage); // 使用 ElMessage 显示错误信息
                this.retryFetchData(); // 请求失败后自动重试
            } finally {
                this.loading = false; // 加载结束，关闭加载动画
                loadingInstance.close(); // 关闭加载实例
            }
        },

        /**
         * 子组件可以重写该方法，提供自定义的数据请求逻辑
         * @param {Object} params 请求参数，包括分页、搜索、筛选和排序等
         * @returns {Promise<Object>} 返回请求的数据
         */
        async fetchDataFromApi(params) {
            const response = await axios.get('/api/data', {params});
            return response.data;
        },

        /**
         * 处理分页变化
         * @param {number} page 新的页码
         */
        handlePageChange(page) {
            this.currentPage = page; // 更新当前页码
            this.fetchData(); // 重新请求数据
            this.updateRoute(); // 更新路由状态
        },

        /**
         * 处理每页显示条数变化
         * @param {number} pageSize 新的每页显示条数
         */
        handlePageSizeChange(pageSize) {
            this.pageSize = pageSize; // 更新每页显示条数
            this.fetchData(); // 重新请求数据
            this.updateRoute(); // 更新路由状态
        },

        /**
         * 搜索时触发（使用防抖技术）
         * 防抖的目的是减少用户快速输入时触发频繁的请求
         */
        handleSearch: debounce(function () {
            this.currentPage = 1; // 搜索时重置为第一页
            this.fetchData(); // 重新请求数据
            this.updateRoute(); // 更新路由状态
        }, 500), // 防抖时间 500ms

        /**
         * 高级搜索操作，用户通过筛选器选择筛选条件
         * @param {Object} filters 筛选条件
         */
        handleAdvancedSearch(filters) {
            this.filters = filters; // 更新筛选条件
            this.currentPage = 1; // 高级搜索时重置为第一页
            this.fetchData(); // 重新请求数据
            this.updateRoute(); // 更新路由状态
        },

        /**
         * 排序操作
         * @param {Object} column 排序的列
         * @param {string} prop 排序的字段
         * @param {string} order 排序的顺序（'ascending' 或 'descending'）
         */
        handleSortChange({column, prop, order}) {
            this.sortField = prop; // 更新排序字段
            this.sortOrder = order === 'ascending' ? 'asc' : 'desc'; // 更新排序顺序
            this.fetchData(); // 重新请求数据
        },

        /**
         * 处理行选择事件，用于批量操作时记录选择的行
         * @param {Array} selectedRows 当前选中的行
         */
        handleRowSelection(selectedRows) {
            this.selectedRows = selectedRows; // 更新选中的行
        },

        /**
         * 请求失败后自动重试，最多重试 3 次
         */
        async retryFetchData() {
            if (this.retryCount < 3) {
                this.retryCount += 1; // 增加重试次数
                this.fetchData(); // 再次请求数据
            } else {
                ElMessage.error('数据请求失败，请稍后再试'); // 重试次数超过限制后显示错误提示
            }
        },

        /**
         * 更新路由状态，将当前的分页、搜索、筛选等状态保存到路由中
         */
        updateRoute() {
            const query = {
                page: this.currentPage,
                pageSize: this.pageSize,
                searchQuery: this.searchQuery,
                filters: JSON.stringify(this.filters),
            };
            this.$router.push({query}); // 更新路由查询参数
        },

        /**
         * 从路由中恢复分页和搜索状态
         * 初始化时读取路由中的状态，恢复页面的状态
         */
        restoreStateFromRoute() {
            const {page, pageSize, searchQuery, filters} = this.$route.query;
            if (page) this.currentPage = Number(page); // 从路由中获取页码
            if (pageSize) this.pageSize = Number(pageSize); // 从路由中获取每页条数
            if (searchQuery) this.searchQuery = searchQuery; // 从路由中获取搜索关键词
            if (filters) {
                try {
                    this.filters = JSON.parse(filters); // 从路由中获取筛选条件
                } catch (error) {
                    console.warn('解析filters失败，使用默认值:', error);
                    this.filters = {}; // 解析失败时使用空对象
                }
            }
            this.fetchData(); // 请求数据
        },

        /**
         * 错误处理，显示详细的错误信息
         * @param {Error} error 错误对象
         */
        handleError(error) {
            this.errorMessage = error.message || '系统出错，请稍后再试'; // 设置错误信息
            ElMessage.error(this.errorMessage); // 显示错误提示
        },

        // 搜索时触发，使用防抖以减少请求次数
        onSearch: debounce(function () {
            this.resetPage(); // 重置页码为1
            this.updateRoute(); // 更新路由
            // 数据获取交给路由监听器处理
        }, 300),


        /**
         * 批量删除操作
         */
        async batchDelete() {
            if (this.selectedRows.length === 0) {
                ElMessage.warning('请先选择要删除的项');
                return;
            }

            try {
                await ElMessageBox.confirm('确定要删除选中的数据吗？', '批量删除', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning',
                });

                // 这里可以调用后端的批量删除接口
                ElMessage.success(`成功删除 ${this.selectedRows.length} 个项目`);
                this.selectedRows = []; // 清空已选项
                this.fetchData(); // 重新请求数据
            } catch (error) {
                ElMessage.info('已取消删除');
            }
        },

        /**
         * 批量编辑操作（可以根据需求修改为更新操作）
         */
        async batchEdit() {
            if (this.selectedRows.length === 0) {
                ElMessage.warning('请先选择要编辑的项');
                return;
            }

            try {
                // 这里可以调用后端的批量编辑接口
                ElMessage.success(`成功编辑 ${this.selectedRows.length} 个项目`);
                this.selectedRows = []; // 清空已选项
                this.fetchData(); // 重新请求数据
            } catch (error) {
                ElMessage.error('编辑失败');
            }
        },
    },

    watch: {
        currentPage() {
            // 避免重复调用，只在不是初始化时调用
            if (this._isMounted) {
                this.fetchData();
            }
        },
        pageSize() {
            // 避免重复调用，只在不是初始化时调用
            if (this._isMounted) {
                this.fetchData();
            }
        },
    },

    created() {
        // 标记组件为未挂载状态
        this._isMounted = false;
    },

    mounted() {
        // 标记组件为已挂载状态
        this._isMounted = true;
        // 在mounted中初始化，避免重复调用
        this.restoreStateFromRoute(); // 初始化时从路由恢复状态
    },

    beforeUnmount() {
        // 清理状态，避免内存泄漏
        this._isMounted = false;
    },
};
