<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\Admin\Brand\BrandResource;
use App\Http\Resources\BaseResource;
use App\Models\ProduceBrace\ProduceSuit;
use Illuminate\Http\Request;

class ProduceSchemaResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            //
            'industry_id' => $this->industry_id ?? null,
            'suit_id' => $this->suit_id ?? null,
            'custom_id' => $this->custom_id ?? null,
            //
            'schema_name' => $this->schema_name ?? null,
            'schema_english_name' => $this->schema_english_name ?? null,
            'schema_description' => $this->schema_description ?? null,
            //
            'sort' => $this->sort ?? null,
            'stock' => $this->stock ?? null,
            'weight' => $this->weight ?? null,
            //
            'suit_number' => $this->suit_number ?? null,
            'spec_number' => $this->spec_number ?? null,
            'custom_number' => $this->custom_number ?? null,
            'fabric_number' => $this->fabric_number ?? null,
            'design_number' => $this->design_number ?? null,
            'trend_number' => $this->trend_number ?? null,
            'craft_number' => $this->craft_number ?? null,
            'shade_number' => $this->shade_number ?? null,
            'purpose_number' => $this->purpose_number ?? null,
            'accessory_number' => $this->accessory_number ?? null,
            //
            'category_uuid' => isset($this->category) ? $this->category->uuid : null,
            'category_name' => isset($this->category) ? $this->category->category_name : null,
            //
            'keywords' => isset($this->keywords) ? $this->keywords()->select('word')->pluck('word')->toArray() : null,
            //
            'spec_ids' => isset($this->specs) ? $this->specs()->select('id')->pluck('id')->toArray() : null,
            'theme_ids' => isset($this->themes) ? $this->themes()->select('id')->pluck('id')->toArray() : null,
            'brand_ids' => isset($this->brands) ? $this->brands()->select('id')->pluck('id')->toArray() : null,
            'param_ids' => isset($this->params) ? $this->params()->select('id')->pluck('id')->toArray() : null,
            //
            'fabric_ids' => isset($this->fabrics) ? $this->fabrics()->select('id')->pluck('id')->toArray() : null,
            'design_ids' => isset($this->designs) ? $this->designs()->select('id')->pluck('id')->toArray() : null,
            'trend_ids' => isset($this->trends) ? $this->trends()->select('id')->pluck('id')->toArray() : null,
            'craft_ids' => isset($this->crafts) ? $this->crafts()->select('id')->pluck('id')->toArray() : null,
            'shade_ids' => isset($this->shades) ? $this->shades()->select('id')->pluck('id')->toArray() : null,
            'purpose_ids' => isset($this->purposes) ? $this->purposes()->select('id')->pluck('id')->toArray() : null,
            'accessory_ids' => isset($this->accessories) ? $this->accessories()->select('id')->pluck('id')->toArray() : null,
            // 同步关联关系数据
            'suits' => isset($this->suit_id) ? ProduceSuit::select(['id', 'uuid', 'suit_name', 'suit_name as value', 'suit_name', 'parent_id', '_lft', '_rgt'])->descendantsOf($this->suit_id)->toTree() : null,
            'specs' => isset($this->specs) ? ProduceSpecResource::collection($this->specs) : null,
            'themes' => isset($this->themes) ? ProduceCustomResource::collection($this->themes) : null,
            'params' => isset($this->params) ? ProduceParamResource::collection($this->params) : null,
            'brands' => isset($this->brands) ? BrandResource::collection($this->brands) : null,
        ]);
    }
}
