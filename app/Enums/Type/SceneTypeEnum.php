<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class ProductSceneTypeEnum
 * @package App\Enums\Type\Product
 * @method static self default() 默认
 * @method static self normal() 正常
 * @method static self video() 视频号
 * @method static self public () 公众号/小程序
 * @method static self platform_product_center() 平台产品中心
 * @method static self domestic_channel() 国内渠道
 * @method static self international_channel() 国际渠道
 */
final class SceneTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'normal' => 1,
            'video' => 2,
            'public' => 3,
            'platform_product_center' => 4,
            'domestic_channel' => 5,
            'international_channel' => 6,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'normal' => '正常',
            'video' => '视频号',
            'public' => '公众号/小程序',
            'platform_product_center' => '平台产品中心',
            'domestic_channel' => '国内渠道',
            'international_channel' => '国际渠道',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color', // 默认
            1 => 'red',           // 正常
            2 => 'orange',        // 视频号
            3 => 'yellow',        // 公众号/小程序
            4 => 'green',         // 平台产品中心
            5 => 'purple',        // 国内渠道
            6 => 'blue',          // 国际渠道
        ];
    }
}
