<?php

namespace App\Models\ShipmentCenter;

use App\Enums\Is\IsDefaultEnum;
use App\Enums\Is\IsStateEnum;
use App\Enums\Type\InteractTypeEnum;
use App\Enums\Vest\OwnerVestEnum;
use App\Enums\Vest\ReachVestEnum;
use App\Models\ExpressBrace\ExpressCorp;
use App\Models\PlatformBrace\Sheet;
use App\Models\PlatformBrace\Source;
use App\Models\SiteBrace\Site;
use App\Models\StoreCenter\Store;
use App\Models\WaybillCenter\Waybill;
use App\Models\WaybillCenter\WaybillAccount;
use App\Models\WaybillCenter\WaybillStation;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShipmentExpress extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'shipment_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];
    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'cat' => 'array',
        'copy_cat' => 'array',
        'market' => 'array',
        'id_card_valid_date' => 'array',
        'deleted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'shipment_vest' => ReachVestEnum::class,
        'conduct_vest' => OwnerVestEnum::class,
        'interact_type' => InteractTypeEnum::class,
        'is_default' => IsDefaultEnum::class,
        'is_state' => IsStateEnum::class,
    ];

    /**
     * @return BelongsTo
     */
    public function site()
    {
        //
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function source()
    {
        //
        return $this->belongsTo(Source::class, 'source_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function sheet()
    {
        //
        return $this->belongsTo(Sheet::class, 'sheet_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function store()
    {
        //
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function express_corp()
    {
        //
        return $this->belongsTo(ExpressCorp::class, 'express_corp_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function waybill()
    {
        //
        return $this->belongsTo(Waybill::class, 'waybill_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function waybill_station()
    {
        //
        return $this->belongsTo(WaybillStation::class, 'waybill_station_id', 'id');
    }

    public function shipment()
    {
        //
        return $this->belongsTo(Shipment::class, 'shipment_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function shipment_address()
    {
        //
        return $this->belongsTo(ShipmentAddress::class, 'shipment_address_id', 'id');
    }

    // 其他模型属性和方法
    public function deliveries()
    {
        //
        return $this->morphMany(Shipment::class, 'delivery');
    }

    /**
     * 获取这篇博客的所有评论
     */
    public function shipment_freights()
    {
        //
        return $this->hasMany(ShipmentFreight::class, 'shipment_express_id', 'id');
    }

    /**
     * 获取用户
     */
    public function waybill_account()
    {
        //
        return $this->belongsTo(WaybillAccount::class, 'waybill_account_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
