<?php

namespace App\Observers\Order;

use App\Models\OrderCenter\OrderSpread;

class OrderSpreadObserver
{
    /**
     * Handle the OrderSpread "created" event.
     */
    public function created(OrderSpread $OrderSpread): void
    {
        //
    }

    /**
     * Handle the OrderSpread "updated" event.
     */
    public function updated(OrderSpread $OrderSpread): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($OrderSpread->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($OrderSpread->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * Handle the OrderSpread "deleted" event.
     */
    public function deleted(OrderSpread $OrderSpread): void
    {
        //
    }

    /**
     * Handle the OrderSpread "restored" event.
     */
    public function restored(OrderSpread $OrderSpread): void
    {
        //
    }

    /**
     * Handle the OrderSpread "force deleted" event.
     */
    public function forceDeleted(OrderSpread $OrderSpread): void
    {
        //
    }
}
