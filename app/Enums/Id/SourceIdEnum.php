<?php

namespace App\Enums\Id;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * 来源类型枚举
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self pc()
 * @method static self mini()
 * @method static self app()
 * @method static self taobao()
 * @method static self ali1688()
 * @method static self pinduoduo()
 * @method static self jingdong()
 * @method static self vip()
 * @method static self aliExpress()
 * @method static self shopee()
 * @method static self amazon()
 * @method static self douyin()
 * @method static self kuaishou()
 * @method static self xiaohongshu()
 * @method static self wechat()
 * @method static self lazada()
 */
final class SourceIdEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    // 定义来源类型值
    protected static function values(): array
    {
        //
        return [
            'pc' => 1,   // PC平台
            'mini' => 2,   // 小程序
            'app' => 3,   // APP
            'taobao' => 4,   // 淘宝/天猫
            'ali1688' => 5,   // 1688
            'pinduoduo' => 6,   // 拼多多
            'jingdong' => 7,   // 京东
            'vip' => 8,   // 唯品会
            'aliexpress' => 9,   // 速卖通
            'shopee' => 10,  // ee
            'amazon' => 11,  // 亚马逊
            'douyin' => 12,  // 抖音小店
            'kuaishou' => 13,  // 快手
            'xiaohongshu' => 14,  // 小红书
            'wechat' => 15,  // 微信
            'lazada' => 16,  // Lazada
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'pc' => 'PC平台',
            'mini' => '小程序',
            'app' => 'APP',
            'taobao' => '淘宝/天猫',
            'ali1688' => '1688',
            'pinduoduo' => '拼多多',
            'jingdong' => '京东',
            'vip' => '唯品会',
            'aliExpress' => '速卖通',
            'shopee' => 'shopee',
            'amazon' => '亚马逊',
            'douyin' => '抖音小店',
            'kuaishou' => '快手',
            'xiaohongshu' => '小红书',
            'wechat' => '微信',
            'lazada' => 'Lazada',
        ];
    }
}
