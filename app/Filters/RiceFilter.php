<?php

namespace App\Filters;

/**
 * Class RiceFilter
 * 提供 Rice 模型的过滤逻辑。
 */
class RiceFilter extends ModelFilter
{
    /**
     * 相关模型及其对应的过滤输入键。
     * 例如：['relationMethod' => ['input_key1', 'input_key2']]
     * @var array
     */
    public $relations = [];

    /**
     * 智能多关键词搜索，支持AND/OR关系和自定义字段，自动过滤无效字段。
     * @param string $keywords 关键词（支持分词）
     * @param string $mode 关系模式：'and' 或 'or'
     * @param array|null $fields 指定要搜索的字段，默认为推荐字段
     * @return $this
     */
    public function keyword($keywords, $mode = 'and', $fields = null)
    {
        $keywords = trim($keywords);
        if (!$keywords)
            return $this;

        // 拆分关键词
        $words = preg_split('/[\s,，]+/u', $keywords);

        // 默认搜索字段白名单
        $safeFields = [
            'rice_number', 'pin_subject', 'rice_title', 'shop_name', 'pin_no'
        ];
        // 如自定义字段，过滤非法字段
        if (is_array($fields) && !empty($fields)) {
            $fields = array_intersect($fields, $safeFields);
            if (empty($fields))
                $fields = $safeFields; // 保底
        } else {
            $fields = $safeFields;
        }

        // 关系模式：and 或 or
        $mode = strtolower($mode) === 'or' ? 'or' : 'and';

        return $this->where(function ($query) use ($words, $fields, $mode) {
            if ($mode === 'and') {
                foreach ($words as $word) {
                    $word = trim($word);
                    if ($word === '')
                        continue;
                    $query->where(function ($q) use ($word, $fields) {
                        foreach ($fields as $field) {
                            $q->orWhere($field, 'like', "%$word%");
                        }
                    });
                }
            } else { // or
                $query->where(function ($q) use ($words, $fields) {
                    foreach ($words as $word) {
                        $word = trim($word);
                        if ($word === '')
                            continue;
                        foreach ($fields as $field) {
                            $q->orWhere($field, 'like', "%$word%");
                        }
                    }
                });
            }
        });
    }

    /**
     * 按创建日期起始范围过滤。
     * @param string $dateFrom 起始日期（格式：YYYY-MM-DD）
     * @return $this
     */
    public function date_from($dateFrom)
    {
        //
        if ($dateFrom) {
            //
            return $this->whereDate('created_at', '>=', $dateFrom);
        }
        //
        return $this;
    }

    /**
     * 按创建日期结束范围过滤。
     * @param string $dateTo 结束日期（格式：YYYY-MM-DD）
     * @return $this
     */
    public function date_to($dateTo)
    {
        if ($dateTo) {
            return $this->whereDate('created_at', '<=', $dateTo);
        }
        return $this;
    }

    /**
     * 按状态过滤商店。
     * @param mixed $state 商店状态，可以是单个状态或多个状态
     * @return $this
     */
    public function state($state)
    {
        if (is_array($state)) {
            return $this->whereIn('rices.rice_state', $state);
        }
        if ($state !== null && $state !== '') {
            return $this->where('rices.rice_state', $state);
        }
        return $this;
    }

    /**
     * 多字段排序
     * @param array|string $sorts ['字段'=>'asc|desc', ...] 或 '字段'
     * @param string|null $sortOrder
     * @return $this
     */
    public function sort($sorts, $sortOrder = null)
    {
        if (is_string($sorts)) {
            $sorts = [$sorts => ($sortOrder ?: 'asc')];
        }

        $safeFields = ['rice_number', 'pin_subject', 'rice_title', 'shop_name', 'pin_no', 'created_at', 'rice_state']; // 允许排序字段
        foreach ($sorts as $sortBy => $order) {
            $sortBy = trim($sortBy);
            $order = strtolower(trim($order));
            if (in_array($sortBy, $safeFields) && in_array($order, ['asc', 'desc'])) {
                $this->orderBy($sortBy, $order);
            }
        }
        return $this;
    }

    /**
     * 支持分页，每页多少条。
     * @param int $perPage 每页条数
     * @return $this
     */
    public function paginateResults($perPage)
    {
        $perPage = intval($perPage) ?: 15;
        return $this->paginate($perPage);
    }
}
