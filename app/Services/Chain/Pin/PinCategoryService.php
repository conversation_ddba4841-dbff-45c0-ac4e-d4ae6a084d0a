<?php

namespace App\Services\Chain\Pin;

use App\Models\PinCenter\Pin;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

/**
 * PinCategoryService 服务类
 * 功能说明：
 * 该服务负责为Pin记录自动分配商品类目。
 * 通过分析Pin标题中的品牌、产品类型、外壳类型等关键词，智能识别商品所属类目。
 * 核心算法：
 * 1. 三维匹配：品牌（Brand）+ 产品（Product）+ 外壳（Shell）
 * 2. 所有三个维度都必须匹配才能确定类目
 * 3. 支持正则表达式匹配，处理复杂的标题格式
 * 支持的类目：
 * - ID 7: iPhone 手机壳
 * - ID 8: 安卓手机壳（含折叠屏）
 * - ID 11: AirPods 耳机壳
 * - ID 12: 安卓系耳机壳
 * - ID 14: Apple Watch 表带
 * - ID 15: Apple Watch 表壳
 */
class PinCategoryService
{
    /**
     * 获取商品类目匹配规则
     * 规则结构说明：
     * - 每个类目ID对应一个三元组：[品牌关键词数组, 产品关键词数组, 外壳关键词数组]
     * - 支持普通字符串匹配和正则表达式匹配（以/开头的字符串）
     * - 三个维度必须同时满足才能匹配成功
     * @return array 类目匹配规则数组
     */
    public static function updatePinMatchCategoryRules(): array
    {
        /*------------------------------------------------------------------
         | 关键词规则配置（三组 AND 逻辑）
         | 格式：$rules[类目ID] = [品牌数组, 产品数组, 外壳数组]
         ------------------------------------------------------------------*/
        $rules = [
            // ========== iPhone 手机壳 ==========
            7 => [
                // 品牌关键词
                ['苹果', 'iphone', 'iphone16', 'iphone15', 'iphone14', 'iphone13', 'iphone12', 'iphone11'],

                // 产品关键词
                ['手机', '折叠屏', 'iphone'],

                // 外壳关键词（包含材质、样式等）
                ['手机壳', '手机套', '手机保护壳', '手机保护套',
                    '防摔手机壳', '磁吸手机壳', '指环手机壳',
                    'tpu', 'imd', '彩绘', '闪闪', '斑马纹', '创意',
                    // 正则：匹配 "iphone + 挂件/链条 + 壳/套/保护" 的组合
                    '/iphone.*?(?:挂件|链条).*?(?:壳|套|保护)/ui',
                    // 兜底正则：匹配 "手机...壳/套/保护"
                    '/(?:手机).*?(?:壳|套|保护)/ui',
                ],
            ],

            // ========== 安卓手机壳（含折叠屏） ==========
            8 => [
                // 品牌关键词（涵盖主流安卓品牌）
                ['三星', 'samsung', 'galaxy', 'zflip', 'z flip', 'zflip3', 'zflip4', 'zflip5', 'zflip6',
                    's24', 's24plus', 's24+', 's24ultra', 's25', 's25plus', 's25+', 's25ultra',
                    '华为', 'huawei', '荣耀', 'honor', '小米', 'xiaomi', 'redmi',
                    'oppo', 'vivo', 'realme', 'note20', 'reno', 'maimang'],

                // 产品关键词
                ['手机', '折叠屏', '翻盖'],

                // 外壳关键词（包含皮套类）
                ['手机壳', '手机套', '折叠屏手机壳', '折叠屏手机套',
                    '防摔手机壳', '磁吸手机壳', '指环手机壳', '支架手机壳',
                    '手机皮套', '翻盖手机皮套', '钱包手机皮套', '钱夹手机皮套',
                    '插卡手机皮套', '侧扣手机皮套', '油蜡皮手机皮套', '二合一手机壳',
                    // 正则兜底：匹配各种手机保护壳变体
                    '/手机.*(?:壳|套|皮套|保护壳|保护套)/ui',
                ],
            ],

            // ========== AirPods 耳机壳 ==========
            11 => [
                // 品牌关键词
                ['airpods', 'airpods2', 'airpods3', 'airpods4', 'airpodspro', '苹果耳机'],

                // 产品关键词
                ['耳机', 'buds', 'airpods'],

                // 外壳关键词
                ['耳机壳', '耳机套', '耳机保护壳', '耳机保护套',
                    '防摔耳机壳', '液态硅胶耳机壳',
                    'tpu', 'imd', '彩绘', '斑马纹', '创意',
                    // 正则：匹配 "airpods + 挂件/链条 + 壳/套/保护"
                    '/airpods.*?(?:挂件|链条).*?(?:壳|套|保护)/ui',
                    // 兜底正则：匹配 "耳机...壳/套/保护"
                    '/耳机.*?(?:壳|套|保护)/ui',
                ],
            ],

            // ========== 安卓系耳机壳 ==========
            12 => [
                // 品牌关键词（各品牌耳机型号）
                ['小米', 'xiaomi', '红米', 'redmi', 'airdots',
                    '华为', 'huawei', '荣耀', 'honor', 'freebuds', 'flypods',
                    '三星', 'samsung', 'galaxybuds', 'buds', 'budsplus', 'buds+',
                    'buds2', 'budsfe', 'budspro', 'budslive'],

                // 产品关键词
                ['耳机', 'buds'],

                // 外壳关键词
                ['耳机壳', '耳机套', '耳机保护壳', '耳机保护套',
                    '液态硅胶耳机壳', '防摔耳机壳',
                    // 正则：匹配 "耳机...壳/套"
                    '/耳机.*(?:壳|套)/ui'],
            ],

            // ========== Apple Watch 表带 ==========
            14 => [
                // 品牌关键词
                ['苹果', 'apple', 'iwatch', 'applewatch'],

                // 产品关键词
                ['表', 'watch'],

                // 表带关键词
                ['表带', '磁吸表带', '运动表带', '金属表带', '硅胶表带'],
            ],

            // ========== Apple Watch 表壳 ==========
            15 => [
                // 品牌关键词
                ['苹果', 'apple', 'iwatch', 'applewatch'],

                // 产品关键词
                ['表', 'watch'],

                // 表壳关键词
                ['表壳', '保护壳', '保护套',
                    // 正则：匹配 "表...壳"
                    '/表.*壳/ui'],
            ],
        ];

        return $rules;
    }

    /**
     * 为单个Pin记录更新类目
     * 处理流程：
     * 1. 将标题转换为小写，便于匹配
     * 2. 遍历所有类目规则，进行三维匹配
     * 3. 根据匹配结果更新Pin状态
     * 4. 未匹配的记录根据条件分配不同的待处理状态
     * Pin状态说明：
     * - 1: 已分类（成功匹配类目）
     * - 2: 过滤状态（不满足处理条件）
     * - 3: 机器审核（需要进一步自动处理）
     * - 4: 人工介入（需要人工处理）
     * - 5: 未识别（无法自动分类）
     * @param Pin $pin 需要处理的Pin对象
     * @param array $rules 类目匹配规则
     * @return void
     */
    public static function updatePinCategory(Pin $pin, array $rules): void
    {
        $now = Carbon::now();

        try {
            // 将标题转换为小写，提高匹配准确性
            $subject = mb_strtolower($pin->pin_subject);

            // 特殊处理：避免耳机产品误判为手机壳
            $isEarphone = Str::contains($subject, '耳机');

            $matchedId = null;

            // 遍历所有类目规则
            foreach ($rules as $id => [$brands, $products, $shells]) {
                // 耳机商品跳过手机壳类目（ID 7, 8）
                if ($isEarphone && in_array($id, [7, 8], true)) {
                    continue;
                }

                // 第一步：品牌匹配（必须满足）
                if (!Str::contains($subject, $brands)) {
                    continue;
                }

                // 第二步：产品匹配（必须满足）
                if (!Str::contains($subject, $products)) {
                    continue;
                }

                // 第三步：外壳类型匹配（支持普通字符串和正则）
                $shellOk = false;
                foreach ($shells as $kw) {
                    // 正则表达式匹配（以/开头的规则）
                    if ($kw[0] === '/' && preg_match($kw, $subject)) {
                        $shellOk = true;
                        break;
                    }
                    // 普通字符串匹配
                    if (Str::contains($subject, $kw)) {
                        $shellOk = true;
                        break;
                    }
                }

                if (!$shellOk) {
                    continue;
                }

                // 三个维度全部匹配成功
                $matchedId = $id;
                break;
            }

            /*--------------------------------------------------------------
             | 根据匹配结果更新Pin状态
             --------------------------------------------------------------*/
            if ($matchedId !== null) {
                // 匹配成功：更新类目ID和状态
                if ($pin->category_id !== $matchedId) {
                    $pin->category_id = $matchedId;
                }

                $pin->pin_state = 1;   // 已分类
                $pin->is_category = 1;   // 标记已有类目

                Log::info('Pin分类成功', [
                    'pin_id' => $pin->id,
                    'pin_no' => $pin->pin_no,
                    'category_id' => $matchedId,
                    'pin_subject' => $pin->pin_subject
                ]);
            } else {
                // 未匹配：根据条件分配不同的待处理状态
                $pin->pin_state = 5;   // 默认为"未识别"
                $pin->is_category = 0;   // 标记未分类

                // 获取关联的店铺信息
                $shop = $pin->shop;

                // 检查标题是否包含相关关键词
                if (static::subjectHasHeadsetOrPhone($pin->pin_subject ?? $pin->pin_title)) {
                    // 根据店铺状态和商品原始类目决定处理方式
                    if (isset($shop) && ($shop->shop_state == 1 || $shop->shop_state == 2)) {
                        // 检查原始类目编号是否在特定列表中
                        $specialCategories = [
                            "201891801", "1044131", "1042207", "1046694", "201345601",
                            "124810006", "124592001", "124270010", "121504002", "1046692"
                        ];

                        if (isset($pin->pin_category_no) && in_array($pin->pin_category_no, $specialCategories)) {
                            $pin->pin_state = 4;   // 需要人工介入
                        } else {
                            $pin->pin_state = 3;   // 机器审核
                        }
                    } else {
                        $pin->pin_state = 2;   // 过滤状态
                    }
                }

                Log::info('Pin分类失败', [
                    'pin_id' => $pin->id,
                    'pin_no' => $pin->pin_no,
                    'pin_state' => $pin->pin_state,
                    'pin_subject' => $pin->pin_subject
                ]);
            }

            // 更新确认时间并保存
            $pin->confirm_time = $now;
            $pin->save();

        } catch (Throwable $e) {
            // 记录异常但不中断流程
            Log::error('Pin分类处理异常', [
                'pin_id' => $pin->id,
                'pin_no' => $pin->pin_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 判断标题是否包含相关关键词
     * 功能说明：
     * - 检查标题中是否包含"耳机"、"手机"、"套"、"壳"等关键词
     * - 用于初步筛选可能需要分类的商品
     * @param string|null $pin_subject Pin标题
     * @return bool 是否包含关键词
     */
    public static function subjectHasHeadsetOrPhone(?string $pin_subject): bool
    {
        if (empty($pin_subject)) {
            return false;
        }

        // 定义关键词列表
        $keywords = ['耳机', '手机', '套', '壳', '保护', '表带'];

        // 使用Str::contains进行批量匹配
        return Str::contains($pin_subject, $keywords);
    }
}
