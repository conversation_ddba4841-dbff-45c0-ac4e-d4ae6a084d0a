<?php

namespace App\Services\Browser\Beast\Rice;

use App\Enums\State\BeastStateEnum;
use App\Enums\State\RiceStateEnum;
use App\Jobs\Chain\Rice\ProcessRiceMatchKeywordQueue;
use App\Models\IndustryBrace\Category;
use App\Models\PinCenter\Pin;
use App\Models\RiceCenter\Rice;
use App\Models\RiceCenter\RiceCorn;
use App\Models\RiceCenter\RiceProp;
use App\Models\RiceCenter\RiceSeed;
use App\Services\BaseService;
use App\Services\Lesson\KeywordService;
use App\Services\Process\Rice\RiceMatchMainRiceService;
use App\Services\Process\Rice\RiceProcessMainService;
use App\Services\Process\RiceSeed\RiceSeedMatchProduceService;
use App\Services\Process\RiceSeed\RiceSeedProcessRiceSeedService;
use Exception;
use Illuminate\Support\Facades\DB;

class ProcessBeastToUpdateOrCreateRiceService extends BaseService
{
    /**
     * 将阿里巴巴的JSON数据转换为Rice, RiceCorn, RiceSeed模型并更新或创建相关记录
     * @param object $Beast - 阿里巴巴传递的商品数据
     * @return Rice - 返回更新或创建的Rice实例
     */
    public static function updateOrCreateRiceServiceData($Beast)
    {
        //
        try {
            // 开始数据库事务
            DB::beginTransaction();
            //
            // 获取Rice、Pin和Category数据
            $rice = Rice::where('pin_no', $Beast->pin_no)->first() ?? new Rice();
            $Pin = Pin::where('pin_no', $Beast->pin_no)->first();
            $Category = Category::where('id', $Beast->category_id ?? $Pin->category_id)->first();
            $jsonData = $Beast->beast_json;
            //
            // 如果传入的Beast数据存在JSON并且Rice记录已找到或新建
            if (isset($Beast->beast_json) && isset($jsonData['formValues'])) {
                // 处理表单数据
                self::processRiceData($rice, $Pin, $Beast, $jsonData, $Category);
            }
            //
            // 提交事务
            DB::commit();
        } catch (Exception $e) {
            // 回滚事务并记录错误
            DB::rollBack();
            // 在此记录日志或抛出异常
            throw new Exception("更新或创建Rice时发生错误：" . $e->getMessage());
        }
    }

    /**
     * 处理Rice相关的数据并保存
     * @param Rice $rice - Rice实例
     * @param Pin $Pin - Pin实例
     * @param object $Beast - 商品数据
     * @param array $jsonData - 阿里巴巴传递的JSON数据
     * @param Category $Category - 分类信息
     */
    private static function processRiceData($rice, $Pin, $Beast, $jsonData, $Category)
    {
        //
        $formValues = $jsonData['formValues'];
        //
        // 分配Rice实例的基本字段，一对一赋值：从获取的表单数据中取出对应的值
        $rice->platform_id = config('app.platform_id'); // 设置平台ID
        $rice->source_id = $Beast->source_id ?? $Pin->source_id ?? $rice->source_id ?? null;
        $rice->category_id = $rice->category_id ?? $Pin->category_id ?? $Beast->category_id ?? $Category->id ?? null;
        //
        $rice->rice_category_no = $Beast->beast_category_no ?? $Pin->pin_category_no ?? $rice->rice_category_no ?? null;
        $rice->rice_category_name = $Beast->beast_category_name ?? $Pin->pin_category_name ?? $rice->rice_category_name ?? null;
        $rice->group_name = $Beast->group_name ?? $rice->group_name ?? null;
        //
        $rice->shop_id = $rice->shop_id ?? $Beast->shop_id ?? $Pin->shop_id ?? null;
        $rice->shop_no = $Beast->shop_no ?? $Pin->shop_no ?? $rice->shop_no ?? null;
        $rice->shop_name = $rice->shop_name ?? $Beast->shop_name ?? $Pin->shop_name ?? null;
        $rice->shop_url = $rice->shop_url ?? $Beast->shop_url ?? $Pin->shop_url ?? null;
        //
        $rice->pin_id = $rice->pin_id ?? $Beast->pin_id ?? $Pin->id ?? null;
        $rice->pin_no = $rice->pin_no ?? $Beast->pin_no ?? $Pin->pin_no ?? null;
        $rice->pin_subject = $rice->pin_subject ?? $Beast->pin_subject ?? $Pin->pin_subject ?? null;
        //
        $rice->rice_preview = $rice->rice_preview ?? $Beast->beast_image ?? $Pin->pin_pic ?? null;
        $rice->rice_number = $rice->rice_number ?? $Beast->beast_number ?? null;
        $rice->rice_image = $formValues['primaryPicture']['imageList'][0]['url'] ?? $Beast->beast_image ?? $rice->rice_image ?? null;
        //
        $rice->amount_sale = $rice->amount_sale ?? $Beast->amountOnSale ?? null;
        //
        // 设置主图、描述、尺寸、重量等
        $rice->rice_title = $formValues['title'] ?? $rice->rice_title ?? null;
        $rice->rice_images = $Beast->beast_images ?? null;
        $rice->rice_descries = self::processDescribes($formValues['description']['detailList'][0]['content'] ?? '') ?? null;
        //
        // 确保表单数据的各项尺寸存在，否则使用默认值
        $rice->length = $formValues['volume']['length'] ?? 12;
        $rice->width = $formValues['volume']['width'] ?? 10;
        $rice->height = $formValues['volume']['height'] ?? 3.5;
        $rice->weight = $formValues['weight'] ?? $formValues['suttleWeight'] ?? null;
        // 设置价格和单位
        $rice->min_price = self::getMinPriceFromSku($formValues) ?? null;
        $rice->max_price = self::getMaxPriceFromSku($formValues) ?? null;
        //
        $rice->rice_unit = $formValues['cbuUnit']['unit'] ?? $Beast->cbu_unit ?? $rice->rice_unit ?? null; // 设置单位
        $rice->total_sale = $Pin->sale_quantity ?? $rice->amount_sale ?? null;  // 设置总销售量
        // 保存Rice实例
        // 处理时间戳字段，使用 Carbon 来标准化时间格式
        $rice->create_time = $Pin->create_time ?? $Beast->create_time ?? null;
        $rice->update_time = $Pin->update_time ?? $Beast->update_time ?? null;
        $rice->publish_time = $Pin->publish_time ?? $Beast->publish_time ?? null;
        $rice->timing_time = $Pin->timing_time ?? $Beast->timing_time ?? null;
        //
        $rice->is_filter = $Beast->is_filter ?? false;
        $rice->is_optimize = $Beast->is_optimize ?? false;
        // 保存
        $rice->rice_state = RiceStateEnum::expand();
        $rice->save(); // 保存Rice实例
        //
        if (isset($rice->category_id)) {
            //
            $rice->is_category = 1;
            $rice->save();
        }
        //
        if (isset($Pin)) {
            //
            $rice->is_pin = 1;
            $rice->save();
            //
            $Pin->is_rice = 1;
            $Pin->save();
        }
        //
        // 使用 refresh() 刷新模型
        $rice->refresh();
        //
        $word_text = '' . $rice->pin_subject . $rice->rice_title . $rice->rice_number;
        // 处理关键词、SKU和分类属性
        $word_text = self::processSaleProperties($rice, $formValues, $word_text);
        //
        self::handleKeywords($rice, $Category, $word_text);
        self::processSkuData($rice, $formValues);
        self::processCategoryProperties($rice, $formValues);
        //
        // 处理Rice与RiceSeed的多对多关联
        self::updateRiceCornSeeds($rice);
        self::assignRiceNumber($rice);
        //
        RiceMatchMainRiceService::processMainRice($rice);
        // 派发关键词匹配队列任务
        ProcessRiceMatchKeywordQueue::dispatch($rice);
        // 使用 refresh() 刷新模型
        $Beast->refresh();
        //
        $Beast->beast_state = BeastStateEnum::record(); // 已建档
        $Beast->rice_state = RiceStateEnum::expand(); // 收获制品
        $Beast->save(); // 保存Rice实例
    }

    /**
     * 处理商品描述中的特殊内容，同时提取所有图片链接
     * @param string $description - 商品描述内容
     * @return array - 包含图片URL的rice_describes数组和处理后的描述内容
     */
    private static function processDescribes(string $description): array
    {
        // 排除的图片URL列表
        $excluded_images = [
            'O1CN01hBll2W1OFCAqJ7128_!!3872131675-0-cib.jpg?__r__=1656234317820',
            '2018/077/340/8763043770_446054626.jpg',
            'O1CN01Nsl2Bk1OFC8LZBFsL_!!3872131675-0-cib.jpg?__r__=1638776795550',
            '2019/416/476/11447674614_446054626.jpg',
            'O1CN01ne9rL71DIRFwfcBgV_!!2164230193-0-cib.jpg',
            '2019/416/476/11447674614_446054626.jpg',
            'O1CN01nmVEi61OFC7oKie7c_!!3872131675-0-cib.jpg?__r__=1635151167207',
            '2019/756/782/11796287657_1071712873.jpg',
            '2019/691/992/11796299196_1071712873.jpg',
            'O1CN01XL4tKl27Y7WJGaIv7_!!2201434797808-0-cib.jpg',
            'O1CN017Bwt4L2ICYbBFthIE_!!2203426429250-0-cib.jpg',
            'O1CN01FSGQ8O1qOBTYvrd2V_!!2218074005485-0-cib.jpg',
            '2020/879/421/13643124978_1054453983.jpg',
            '2020/932/241/13643142239_1054453983.jpg',
            '2020/352/748/13680847253_1054453983.jpg',
            'O1CN01co4DGX2Moo8I04vbT_!!2216922809875-0-cib.jpg'
        ];

        // 提取所有图片URL
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/', $description, $matches);

        // 用于存储有效图片URL的数组
        $rice_describes = [];

        // 检查是否提取到图片URL
        if (isset($matches[1])) {
            //
            foreach ($matches[1] as $imageUrl) {
                // 如果图片URL是相对路径，则添加基础URL
                if (strpos($imageUrl, 'http') === false) {
                    //
                    $imageUrl = 'https://cbu01.alicdn.com/img/ibank/' . $imageUrl;
                }

                // 使用 strpos() 检查图片URL是否包含在排除列表中，如果包含则跳过
                $exclude = false;
                foreach ($excluded_images as $excluded_image) {
                    // 如果 URL 中包含排除的图片路径，则跳过该图片
                    if (strpos($imageUrl, $excluded_image) !== false) {
                        $exclude = true;
                        break;  // 一旦匹配到，跳出循环
                    }
                }

                // 如果没有匹配到排除的图片，添加到有效图片数组
                if (!$exclude) {
                    //
                    $rice_describes[] = $imageUrl;
                }
            }
        }

        // 返回过滤后的图片URL数组
        return $rice_describes;
    }

    /**
     * 从SKU信息中获取最小价格
     * @param array $formValues - 表单数据
     * @return float|null - 最小价格
     */
    private static function getMinPriceFromSku($formValues)
    {
        //
        $minPrice = null;
        //
        if (isset($formValues['skuTable']) && count($formValues['skuTable']) > 0) {
            //
            $prices = array_map(function ($sku) {
                //
                return $sku['sku_price'];
            }, $formValues['skuTable']);
            //
            $minPrice = min($prices);
        }
        //
        return $minPrice;
    }

    /**
     * 从SKU信息中获取最大价格
     * @param array $formValues - 表单数据
     * @return float|null - 最大价格
     */
    private static function getMaxPriceFromSku($formValues)
    {
        //
        $maxPrice = null;
        //
        if (isset($formValues['skuTable']) && count($formValues['skuTable']) > 0) {
            //
            $prices = array_map(function ($sku) {
                //
                return $sku['sku_price'];
            }, $formValues['skuTable']);
            //
            $maxPrice = max($prices);
        }
        //
        return $maxPrice;
    }

    /**
     * 处理销售属性（saleProp）
     * @param Rice $rice - Rice实例
     * @param array $formValues - 表单数据
     */
    private static function processSaleProperties($rice, $formValues, $word_text)
    {
        //
        if (isset($formValues['saleProp'])) {
            //
            foreach ($formValues['saleProp'] as $seedPropKey => $seedPropValues) {
                //
                foreach ($seedPropValues as $seedPropValue) {
                    //
                    $attr_type = self::getSalePropertyType($seedPropKey);
                    //
                    $riceSeed = RiceSeed::updateOrCreate(
                        [
                            'rice_id' => $rice->id,
                            'seed_name' => $seedPropKey,
                            'seed_value' => $seedPropValue['value'],
                        ],
                        [
                            'attr_type' => $attr_type,
                            'seed_text' => $seedPropValue['text'] ?? '',
                            'seed_custom' => filter_var($seedPropValue['custom'] ?? false, FILTER_VALIDATE_BOOLEAN),
                            'seed_image_url' => $seedPropValue['imgUrl'] ?? null,
                        ]);
                    //
                    RiceSeedProcessRiceSeedService::RiceSeedMatchProduceService($riceSeed, $rice);
                    //
                    $word_text = $word_text . $riceSeed->seed_text;
                }
            }
        }
        //
        return $word_text;
    }

    /**
     * 获取销售属性的类型
     * @param string $seedPropKey - 销售属性键
     * @return string - 销售属性类型
     */
    private static function getSalePropertyType($seedPropKey)
    {
        //
        $attr_type = '';
        //
        if (in_array($seedPropKey, ['p-404', 'p-3216'])) {
            //
            $attr_type = 'spec'; // 规格
        } elseif (in_array($seedPropKey, ['p-100018377', 'p-3151', 'p-1234', 'p-446'])) {
            //
            $attr_type = 'suit'; // 配套
        } else {
            //
            $attr_type = 'custom';
        }
        //
        return $attr_type;
    }

    /**
     * 处理关键词
     * @param Rice $rice - Rice实例
     */
    private static function handleKeywords($rice, $Category, $word_text)
    {
        //
        $matchedKeywordIds = [];
        //
        // 匹配分类中的关键词
        if ($Category && isset($Category->keywords)) {
            //
            foreach ($Category->keywords as $keyword) {
                //
                if (strpos($word_text, $keyword->word) !== false) {
                    //
                    $matchedKeywordIds[] = $keyword->id;
                }
            }
            //
            if (!empty($matchedKeywordIds)) {
                //
                $rice->keywords()->syncWithoutDetaching($matchedKeywordIds);
            }
        }
        //
        // 使用KeywordService进一步处理关键词
        $keywords = KeywordService::SubjectPSCWS4APIProcessWord($word_text);
        //
        if (count($keywords) > 0) {
            //
            $existingKeywordIds = $rice->keywords()->pluck('id')->toArray();
            $allIds = array_unique(array_merge($existingKeywordIds, array_column($keywords, 'id')));
            //
            $rice->keywords()->sync($allIds);
        }
    }

    /**
     * 处理SKU数据
     * @param Rice $rice - Rice实例
     * @param array $formValues - 表单数据
     */
    private static function processSkuData($rice, $formValues)
    {
        //
        if (isset($formValues['skuTable'])) {
            //
            foreach ($formValues['skuTable'] as $sku) {
                //
                // 假设已经有 $riceCorn 实例和 $sku 数组 先查找，找不到就新建
                $riceCorn = RiceCorn::where('rice_id', $rice->id)->where('corn_key', $sku['key'])->first() ?? new RiceCorn();
                //
                $riceCorn->rice_id = $rice->id; // 设置rice_id
                $riceCorn->corn_key = $riceCorn->corn_key ?? $sku['key'] ?? null; // corn_key
                // 一对一赋值
                $riceCorn->corn_no = $riceCorn->corn_no ?? $sku['sku_skuId'] ?? null;
                $riceCorn->corn_price = $riceCorn->corn_price ?? $sku['sku_price'] ?? null;
                $riceCorn->corn_take_price = $riceCorn->corn_take_price ?? $sku['sku_takePrice'] ?? null;
                $riceCorn->corn_values = $riceCorn->corn_values ?? $sku['values'] ?? null;
                $riceCorn->corn_props = $riceCorn->corn_props ?? $sku['sku_props'] ?? null;
                $riceCorn->corn_sale = $riceCorn->corn_sale ?? $sku['sku_amountOnSale'] ?? null;
                $riceCorn->corn_process = $riceCorn->corn_process ?? $sku['sku_amountOnProcess'] ?? null;
                $riceCorn->corn_sn = $riceCorn->corn_sn ?? $sku['sku_cargoNumber'] ?? null;
                $riceCorn->corn_weight = $riceCorn->corn_weight ?? $sku['weight'] ?? 0;
                $riceCorn->corn_length = $riceCorn->corn_length ?? $sku['length'] ?? 0;
                $riceCorn->corn_width = $riceCorn->corn_width ?? $sku['width'] ?? 0;
                $riceCorn->corn_height = $riceCorn->corn_height ?? $sku['height'] ?? 0;
                $riceCorn->corn_status = $riceCorn->corn_status ?? $sku['sku_status'] ?? 1;
                //
                $riceCorn->save();
            }
        }
    }

    /**
     * 处理商品分类属性
     * @param Rice $rice - Rice实例
     * @param array $formValues - 表单数据
     */
    private static function processCategoryProperties($rice, $formValues)
    {
        //
        if (isset($formValues['catProp'])) {
            //
            foreach ($formValues['catProp'] as $catNo => $catProp) {
                //
                if (is_array($catProp)) {
                    //
                    foreach ($catProp as $cat) {
                        //
                        RiceProp::updateOrCreate(
                            [
                                'rice_id' => $rice->id,
                                'category_no' => $rice->rice_category_no,
                                'prop_no' => $catNo,
                                'prop_text' => $cat['text'] ?? null,
                            ],
                            [
                                'prop_value' => $cat['value'] ?? null,
                                'prop_name' => $cat['propName'] ?? null,
                            ]
                        );
                        //
                        // 处理特定的分类属性
                        if ($catNo === 'p-1398' || $catNo === 'custom') {
                            //
                            if (is_string($cat['text'])) {
                                //
                                $rice->rice_number = $cat['text'];
                                $rice->save();
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新RiceCorn与RiceSeed的多对多关联
     * @param Rice $rice - Rice实例
     */
    private static function updateRiceCornSeeds($rice)
    {
        //
        $rice_corns = $rice->rice_corns; // 获取Rice_corns
        //
        // 确保Rice_corns存在并且有数据
        if ($rice_corns->isNotEmpty()) {
            //
            foreach ($rice_corns as $rice_corn) {
                // 获取当前RiceCorn的corn_values
                $corn_values = $rice_corn->corn_values;
                // 获取所有相关的RiceSeed数据
                $riceSeeds = RiceSeed::where('rice_id', $rice->id)->whereIn('seed_value', $corn_values)->get();// 根据corn_values筛选RiceSeed
                // 如果找到相关的RiceSeed，使用syncWithoutDetaching方法更新多对多关联
                if ($riceSeeds->isNotEmpty()) {
                    //
                    $rice_corn->rice_corn_seeds()->syncWithoutDetaching($riceSeeds->pluck('id')->unique()->toArray());
                }
            }
        }
    }

    /**
     * 分配Rice实例的尺寸和重量
     * @param Rice $rice - Rice实例
     */
    private static function assignRiceNumber($rice)
    {
        //
        $rice_corn = $rice->rice_corns->first();
        // 只有当 $rice_corn 存在、corn_sn 属性存在，且去除前后空白后非空时才拼接
        if (isset($rice_corn, $rice_corn->corn_sn) && trim($rice_corn->corn_sn) !== '' && strpos($rice->rice_number, '——') === false) {
            // 拼接 rice_number
            $rice->rice_number .= '——' . $rice_corn->corn_sn;
            $rice->save(); // 保存 Rice 数据
        }
        // 假设 $rice 已经取出，准备保存前……
        if ($rice->rice_number) {
            // 把末尾连续的 “——” 全部去掉
            $rice->rice_number = preg_replace([
                '/^(?:其他——|——|其他)+/u',  // 开头多余段
                '/(?:——)+$/u',          // 末尾多余分隔符
            ], '', trim($rice->rice_number));
            // 1. 先 trim 整体空白
            $str = trim($rice->rice_number);
            //
            $rice->rice_number = $str;
        }
        //
        $rice->save();
    }
}
