<template>
    <div class="frontend-main-layout">
        <!-- 头部插槽 -->
        <slot :header-config="headerConfig" :is-scrolled="isScrolled" name="header">
            <!-- 默认头部 -->
            <FrontendHeader
                :header-config="headerConfig"
                :is-scrolled="isScrolled"
                @menu-select="handleMenuSelect"
            />
        </slot>

        <!-- 主要内容区域 -->
        <div :class="contentContainerClasses">
            <!-- 面包屑插槽 -->
            <slot v-if="showBreadcrumb" :breadcrumb-config="breadcrumbConfig" name="breadcrumb">
                <!-- 默认面包屑可以在这里添加 -->
            </slot>

            <!-- 加载状态 -->
            <div v-if="isContentLoading" class="content-loading">
                <div class="loading-container">
                    <el-skeleton animated>
                        <template #template>
                            <div class="skeleton-content">
                                <el-skeleton-item style="width: 60%; margin-bottom: 20px;" variant="h1"/>
                                <el-skeleton-item style="width: 80%; margin-bottom: 16px;" variant="text"/>
                                <el-skeleton-item style="width: 70%; margin-bottom: 16px;" variant="text"/>
                                <el-skeleton-item style="width: 90%; margin-bottom: 32px;" variant="text"/>
                                <div class="skeleton-cards">
                                    <div v-for="i in 3" :key="i" class="skeleton-card">
                                        <el-skeleton-item style="width: 100%; height: 200px; margin-bottom: 16px;"
                                                          variant="image"/>
                                        <el-skeleton-item style="width: 80%; margin-bottom: 8px;" variant="h3"/>
                                        <el-skeleton-item style="width: 100%;" variant="text"/>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-skeleton>
                </div>
            </div>

            <!-- 错误状态 -->
            <div v-if="hasError" class="error-container">
                <el-result :sub-title="errorMessage" :title="errorTitle" icon="error">
                    <template #extra>
                        <div class="error-actions">
                            <el-button :loading="isRetrying" type="primary" @click="retryLoad">
                                <el-icon v-if="!isRetrying">
                                    <Refresh/>
                                </el-icon>
                                {{ isRetrying ? '重新加载中...' : '重新加载' }}
                            </el-button>
                            <el-button @click="goBack">
                                <el-icon>
                                    <ArrowLeft/>
                                </el-icon>
                                返回上页
                            </el-button>
                            <el-button text @click="goHome">
                                回到首页
                            </el-button>
                        </div>
                    </template>
                </el-result>
            </div>

            <!-- 正常内容 -->
            <main v-if="!isContentLoading && !hasError" class="frontend-main">
                <slot name="content">
                    <!-- 默认内容插槽 -->
                    <router-view v-slot="{ Component, route }">
                        <transition mode="out-in" name="page-transition">
                            <component :is="Component" v-if="Component" :key="route.path" class="page-component"/>
                        </transition>
                    </router-view>
                </slot>
            </main>
        </div>

        <!-- 底部插槽 -->
        <slot :footer-config="footerConfig" name="footer">
            <!-- 默认底部 -->
            <FrontendFooter :footer-config="footerConfig"/>
        </slot>

        <!-- 返回顶部按钮 -->
        <transition name="fab-scale">
            <div v-if="showBackToTop" class="back-to-top-fab" @click="scrollToTop">
                <div :style="{ transform: `rotate(${scrollProgress * 360}deg)` }" class="fab-progress">
                    <svg class="progress-ring" height="56" width="56">
                        <circle class="progress-ring-background" cx="28" cy="28" fill="none" r="24"
                                stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
                        <circle :stroke-dasharray="150.8" :stroke-dashoffset="150.8 * (1 - scrollProgress)"
                                class="progress-ring-progress" cx="28" cy="28" fill="none" r="24" stroke="#409eff"
                                stroke-width="2" transform="rotate(-90 28 28)"/>
                    </svg>
                </div>
                <el-icon :size="20" class="fab-icon">
                    <ArrowUp/>
                </el-icon>
            </div>
        </transition>
    </div>
</template>

<script>
import FrontendHeader from './components/FrontendHeader.vue'
import FrontendFooter from './components/FrontendFooter.vue'
import {useAppStoreSystem} from '@stores/app/useAppStoreSystem.js'
import {useUserStore} from '@stores/user/useUserStore'
import {ElMessage} from 'element-plus'
import {ArrowLeft, ArrowUp, Refresh} from '@element-plus/icons-vue'

export default {
    name: 'FrontendMainLayout',
    components: {
        FrontendHeader,
        FrontendFooter,
        ArrowLeft,
        ArrowUp,
        Refresh
    },

    /**
     * 组件属性定义
     * 支持通过 props 传递配置数据，实现更好的组件复用
     */
    props: {
        /**
         * 头部配置数据
         * @type {Object}
         * @default {}
         */
        headerConfig: {
            type: Object,
            default: () => ({})
        },

        /**
         * 面包屑配置
         * @type {Object}
         * @default {}
         */
        breadcrumbConfig: {
            type: Object,
            default: () => ({})
        },

        /**
         * 底部信息配置
         * @type {Object}
         * @default {}
         */
        footerConfig: {
            type: Object,
            default: () => ({})
        },

        /**
         * 布局主题配置
         * @type {String}
         * @default 'default'
         */
        layoutTheme: {
            type: String,
            default: 'default',
            validator: (value) => ['default', 'desktop', 'mobile', 'landing'].includes(value)
        },

        /**
         * 容器样式配置
         * @type {String}
         * @default 'container'
         */
        containerType: {
            type: String,
            default: 'container',
            validator: (value) => ['container', 'container-1760', 'fluid', 'none'].includes(value)
        }
    },

    setup() {
        const appStore = useAppStoreSystem()
        const userStore = useUserStore()

        return {
            appStore,
            userStore
        }
    },

    data() {
        return {
            // 滚动状态
            isScrolled: false,
            showBackToTop: false,
            scrollProgress: 0,

            // 错误状态
            hasError: false,
            errorTitle: '页面加载失败',
            errorMessage: '请稍后重试或联系客服',
            isRetrying: false,

            // 内容加载状态
            isContentLoading: false,

            // 移动端状态
            isMobile: false
        }
    },

    computed: {
        /**
         * 是否显示面包屑
         */
        showBreadcrumb() {
            return this.breadcrumbConfig?.show !== false
        },

        /**
         * 内容容器样式类
         */
        contentContainerClasses() {
            const classes = ['content-container']

            if (this.containerType !== 'none') {
                classes.push(this.containerType)
            }

            if (this.containerType === 'container-1760') {
                classes.push('container-mobile')
            }

            return classes
        }
    },

    mounted() {
        this.init()
    },

    beforeUnmount() {
        this.cleanup()
    },

    methods: {
        /**
         * 初始化组件
         */
        init() {
            // 初始化移动端检测
            this.handleResize()

            // 添加事件监听
            window.addEventListener('resize', this.handleResize)
            window.addEventListener('scroll', this.handleScroll, {passive: true})

            // 检查路由错误
            this.checkRouteError(this.$route)
        },

        /**
         * 清理资源
         */
        cleanup() {
            window.removeEventListener('resize', this.handleResize)
            window.removeEventListener('scroll', this.handleScroll)
        },

        /**
         * 处理窗口大小变化
         */
        handleResize() {
            this.isMobile = window.innerWidth < 768
            // 通知 appStore 移动端状态变化
            if (this.appStore.setMobileState) {
                this.appStore.setMobileState(this.isMobile)
            }
        },

        /**
         * 处理滚动事件
         */
        handleScroll() {
            const scrollY = window.scrollY
            this.isScrolled = scrollY > 10
            this.showBackToTop = scrollY > 300

            // 计算滚动进度
            const documentHeight = document.documentElement.scrollHeight - window.innerHeight
            this.scrollProgress = Math.min(scrollY / documentHeight, 1)
        },

        /**
         * 返回顶部
         */
        scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            })
        },

        /**
         * 处理菜单选择
         */
        handleMenuSelect(payload) {
            try {
                // 显示加载状态
                this.isContentLoading = true

                // 设置定时器自动关闭加载状态
                setTimeout(() => {
                    this.isContentLoading = false
                }, 2000)

                console.log('菜单选择:', payload)
            } catch (error) {
                console.error('处理菜单选择错误:', error)
                this.isContentLoading = false
            }
        },

        /**
         * 检查路由错误
         */
        checkRouteError(to) {
            if (to.name === 'NotFound' || to.name === '404') {
                this.hasError = true
                this.errorTitle = '页面不存在'
                this.errorMessage = `页面 "${to.path}" 不存在，请检查URL是否正确。`
            }
        },

        /**
         * 重新加载
         */
        async retryLoad() {
            this.isRetrying = true
            try {
                await new Promise(resolve => setTimeout(resolve, 1000))
                window.location.reload()
            } catch (error) {
                console.error('重新加载失败:', error)
                ElMessage.error('重新加载失败，请稍后重试')
            } finally {
                this.isRetrying = false
            }
        },

        /**
         * 返回上页
         */
        goBack() {
            if (window.history.length > 1) {
                this.$router.back()
            } else {
                this.goHome()
            }
        },

        /**
         * 回到首页
         */
        goHome() {
            const homeRoute = this.headerConfig?.homeRoute || '/'
            this.$router.push(homeRoute)
        }
    },

    watch: {
        // 监听路由变化
        $route: {
            handler(to, from) {
                // 检查路由错误
                this.checkRouteError(to)

                // 如果不是404错误，重置错误状态
                if (to.name !== 'NotFound' && to.name !== '404') {
                    this.hasError = false
                }

                // 如果是菜单点击导致的路由切换，关闭加载状态
                if (this.isContentLoading && from) {
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.isContentLoading = false
                        }, 500)
                    })
                }
            },
            immediate: true
        }
    }
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

/* === 主布局容器 === */
.frontend-main-layout {
    min-height: 100vh;
    @include flex-column;
    background-color: $body-bg;
    font-family: $font-family-primary;
    color: $text-primary;
    position: relative;
    @include no-animation; // 企业级无动画

    /* 主题切换 */
    &.theme-dark {
        background: $body-bg-dark;
        color: $text-inverse;
    }

    /* 减少动画 - 企业级设计 */
    &.reduced-motion * {
        @include no-animation;
    }
}

/* === 内容容器 === */
.content-container {
    flex: 1;
    @include flex-column;
    position: relative;

    &.container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 $spacing-lg;
    }

    &.container-1760 {
        max-width: 1760px;
        margin: 0 auto;
        padding: 0 $spacing-lg;

        &.container-mobile {
            @media (max-width: 768px) {
                padding: 0 $spacing-base;
            }
        }
    }

    &.fluid {
        width: 100%;
        padding: 0 $spacing-lg;
    }
}

/* === 主内容区域 === */
.frontend-main {
    flex: 1;
    min-height: calc(100vh - 200px);
    padding-top: 80px; // 为固定头部留出空间

    @media (max-width: 768px) {
        padding-top: 70px; // 移动端头部高度
    }

    .page-component {
        min-height: 400px;
    }
}

/* === 加载状态 === */
.content-loading {
    flex: 1;
    padding: $spacing-xl;

    .loading-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .skeleton-content {
        padding: $spacing-lg 0;
    }

    .skeleton-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: $spacing-lg;
        margin-top: $spacing-xl;

        .skeleton-card {
            padding: $spacing-base;
            border: 1px solid $border-color;
            border-radius: $border-radius-base;
        }
    }
}

/* === 错误状态 === */
.error-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;

    .error-actions {
        display: flex;
        gap: $spacing-base;
        flex-wrap: wrap;
        justify-content: center;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: center;

            .el-button {
                width: 200px;
            }
        }
    }
}

/* === 返回顶部按钮 === */
.back-to-top-fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 56px;
    height: 56px;
    background: $primary-color;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba($primary-color, 0.3);
    z-index: 1000;
    transition: all 0.3s ease;

    &:hover {
        background: color.adjust($primary-color, $lightness: -10%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba($primary-color, 0.4);
    }

    .fab-progress {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .progress-ring {
        transform: rotate(-90deg);
    }

    .progress-ring-progress {
        transition: stroke-dashoffset 0.3s ease;
    }

    .fab-icon {
        color: white;
        z-index: 1;
    }

    @media (max-width: 768px) {
        bottom: 20px;
        right: 20px;
        width: 48px;
        height: 48px;
    }
}

/* === 动画效果 === */
.fab-scale-enter-active,
.fab-scale-leave-active {
    transition: all 0.3s ease;
}

.fab-scale-enter-from,
.fab-scale-leave-to {
    opacity: 0;
    transform: scale(0.8);
}

// 页面切换动画 - 企业级无动画
.page-transition-enter-active,
.page-transition-leave-active {
    @include no-animation; // 企业级无动画
}

.page-transition-enter-from {
    opacity: 0;
}

.page-transition-leave-to {
    opacity: 0;
}

/* === 响应式设计 === */
@media (max-width: 1200px) {
    .content-container.container {
        max-width: 100%;
        padding: 0 $spacing-base;
    }
}

@media (max-width: 768px) {
    .frontend-main-layout {
        font-size: $font-size-sm;
    }

    .content-container {
        padding: 0 $spacing-base;
    }

    .frontend-main {
        min-height: calc(100vh - 160px);
    }
}

// 容器响应式优化
.container-mobile {
    max-width: 1760px; // 企业级最大宽度
    min-width: 640px; // 企业级最小宽度
    margin: 0 auto;
    padding: 0 $spacing-lg;

    @media (max-width: 640px) {
        padding: 0 $spacing-md;
    }
}
</style>
