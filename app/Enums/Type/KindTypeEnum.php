<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class KindTypeEnum
 * @package App\Enums\Type\
 * @method static self default() 默认
 * @method static self normal() 普通订单
 * @method static self virtual() 虚拟产品订单
 * @method static self poi() 电子券（poi核销）
 * @method static self third() 三方核销
 * @method static self presale() 预售单
 * @method static self sample() 样品
 * @method static self quality() 质检单
 * @method static self test() 测试单
 */
final class KindTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'normal' => 1,
            'virtual' => 2,
            'poi' => 4,
            'third' => 5,
            'presale' => 6,
            'sample' => 7,
            'quality' => 8,
            'test' => 9,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'normal' => '普通订单',
            'virtual' => '虚拟产品订单',
            'poi' => '电子券（poi核销）',
            'third' => '三方核销',
            'presale' => '预售单',
            'sample' => '样品',
            'quality' => '质检单',
            'test' => '测试单',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color', // 默认
            1 => 'red',           // 普通订单
            2 => 'orange',        // 虚拟产品订单
            4 => 'yellow',        // 电子券（poi核销）
            5 => 'green',         // 三方核销
            6 => 'purple',        // 预售单
            7 => 'blue',          // 样品
            8 => 'pink',          // 质检单
            9 => 'brown',         // 测试单
        ];
    }
}
