<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * 备用状态枚举
 * @package App\Enums\State\SpareStateEnum
 * @method static self pending()     // 待选
 * @method static self selected()    // 已选品
 * @method static self review()      // 审核中
 * @method static self approved()    // 机审通过
 * @method static self draft()       // 待建档
 * @method static self progress()    // 处理中
 * @method static self retry()       // 待重试
 * @method static self record()      // 已建档
 * @method static self reserved()    // 定时预约中
 * @method static self ready()       // 准备状态
 * @method static self exhibit()     // 已上货
 * @method static self archived()    // 已归档
 * @method static self complete()    // 已完成
 * @method static self expired()     // 已过期
 * @method static self cancel()      // 已取消
 * @method static self discard()     // 废弃状态
 */
final class SpareStateEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    /**
     * 定义备用状态的值
     */
    protected static function values(): array
    {
        //
        return [
            'pending' => 0,    // 待选
            'selected' => 1,    // 已选品
            'review' => 5,    // 审核中
            'approved' => 6,    // 机审通过
            'draft' => 10,   // 待建档
            'progress' => 20,   // 处理中
            'retry' => 30,   // 待重试
            'record' => 40,   // 已建档
            'reserved' => 45,   // 定时预约中
            'ready' => 50,   // 准备状态
            'exhibit' => 60,   // 已上货
            'archived' => 80,   // 已归档
            'complete' => 100,  // 已完成
            'expired' => 111,  // 已过期
            'cancel' => 122,  // 已取消
            'discard' => 200,  // 废弃状态
        ];
    }

    /**
     * 定义每个备用状态的中文标签
     */
    protected static function labels(): array
    {
        //
        return [
            'pending' => '待选',         // 待选
            'selected' => '已选品',       // 已选品
            'review' => '审核中',       // 审核中
            'approved' => '机审通过',     // 机审通过
            'draft' => '待建档',       // 待建档
            'progress' => '处理中',       // 处理中
            'retry' => '待重试',       // 待重试
            'record' => '已建档',       // 已建档
            'reserved' => '定时预约中',   // 定时预约中
            'ready' => '准备状态',     // 准备状态
            'exhibit' => '已上货',       // 已上货
            'archived' => '已归档',       // 已归档
            'complete' => '已完成',       // 已完成
            'expired' => '已过期',       // 已过期
            'cancel' => '已取消',       // 已取消
            'discard' => '废弃状态',     // 废弃状态
        ];
    }
}
