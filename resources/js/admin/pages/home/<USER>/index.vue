<template>
    <div class="dashboard-page">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt"></i>
                    仪表盘
                </h1>
                <p class="page-subtitle">欢迎回来，{{ userStore.username }}！这里是您的数据概览</p>
            </div>
            <div class="header-right">
                <div class="header-info">
                    <div class="current-time">
                        <i class="fas fa-clock"></i>
                        {{ currentTime }}
                    </div>
                    <div class="last-updated">
                        最后更新：{{ lastUpdated }}
                    </div>
                </div>
                <div class="header-actions">
                    <el-button :loading="refreshing" circle @click="handleRefresh">
                        <i class="fas fa-sync-alt"></i>
                    </el-button>
                    <el-button circle type="primary" @click="exportData">
                        <i class="fas fa-download"></i>
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div v-for="stat in statsData" :key="stat.id" class="stats-card">
                <div :style="{ background: stat.gradient }" class="card-icon">
                    <i :class="stat.icon"></i>
                </div>
                <div class="card-content">
                    <h3 class="card-value">{{ stat.value }}</h3>
                    <p class="card-label">{{ stat.label }}</p>
                    <div :class="stat.trendClass" class="card-trend">
                        <i :class="stat.trendIcon"></i>
                        <span>{{ stat.trend }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-row">
                <!-- 销售趋势图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>销售趋势</h3>
                        <el-select v-model="salesPeriod" class="period-select" size="small">
                            <el-option label="最近7天" value="7d"></el-option>
                            <el-option label="最近30天" value="30d"></el-option>
                            <el-option label="最近90天" value="90d"></el-option>
                        </el-select>
                    </div>
                    <div class="chart-content">
                        <canvas ref="salesChart" class="chart-canvas"></canvas>
                    </div>
                </div>

                <!-- 用户统计图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>用户统计</h3>
                        <el-button-group size="small">
                            <el-button v-for="period in userPeriods" :key="period.value"
                                       :type="userPeriod === period.value ? 'primary' : 'default'"
                                       @click="userPeriod = period.value">
                                {{ period.label }}
                            </el-button>
                        </el-button-group>
                    </div>
                    <div class="chart-content">
                        <canvas ref="userChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格和活动区域 -->
        <div class="data-section">
            <div class="data-row">
                <!-- 最新订单 -->
                <div class="data-card">
                    <div class="data-header">
                        <h3>最新订单</h3>
                        <router-link class="view-all-btn" to="/admin/orders">
                            查看全部
                            <i class="fas fa-arrow-right"></i>
                        </router-link>
                    </div>
                    <div class="data-content">
                        <el-table :data="recentOrders" size="small" style="width: 100%">
                            <el-table-column label="订单号" prop="id" width="120"></el-table-column>
                            <el-table-column label="客户" prop="customer"></el-table-column>
                            <el-table-column label="金额" prop="amount" width="100">
                                <template #default="scope">
                                    <span class="amount">¥{{ scope.row.amount }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="状态" prop="status" width="100">
                                <template #default="scope">
                                    <el-tag :type="getStatusType(scope.row.status)" size="small">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>

                <!-- 最新活动 -->
                <div class="data-card">
                    <div class="data-header">
                        <h3>最新活动</h3>
                        <router-link class="view-all-btn" to="/admin/activities">
                            查看全部
                            <i class="fas fa-arrow-right"></i>
                        </router-link>
                    </div>
                    <div class="data-content">
                        <div class="activity-list">
                            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                                <div class="activity-avatar">
                                    <img :alt="activity.user" :src="activity.avatar">
                                </div>
                                <div class="activity-content">
                                    <p class="activity-text">
                                        <strong>{{ activity.user }}</strong>
                                        {{ activity.action }}
                                    </p>
                                    <span class="activity-time">{{ activity.time }}</span>
                                </div>
                                <div class="activity-icon">
                                    <i :class="activity.icon" :style="{ color: activity.color }"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
            <h3 class="section-title">快速操作</h3>
            <div class="actions-grid">
                <div v-for="action in quickActions" :key="action.id" class="action-card"
                     @click="handleQuickAction(action)">
                    <div :style="{ background: action.gradient }" class="action-icon">
                        <i :class="action.icon"></i>
                    </div>
                    <h4>{{ action.title }}</h4>
                    <p>{{ action.description }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {useUserStore} from '@stores/user/useUserStore'
import {useNotificationStore} from '@stores/notification/useNotificationStore.js'
import {createSafeChart, createSafeChartConfig, destroyChart, updateChartData} from '@/admin/utils/chartConfig'

export default {
    name: 'DashboardIndexPage',
    data() {
        return {
            salesPeriod: '30d',
            userPeriod: 'month',
            salesChart: null,
            userChart: null,
            refreshing: false,
            currentTime: '',
            lastUpdated: '',
            refreshTimer: null,
            timeTimer: null,
            userPeriods: [
                {label: '日', value: 'day'},
                {label: '周', value: 'week'},
                {label: '月', value: 'month'}
            ],
            statsData: [
                {
                    id: 1,
                    icon: 'fas fa-users',
                    value: '12,458',
                    label: '总用户数',
                    trend: '+12.5%',
                    trendClass: 'trend-up',
                    trendIcon: 'fas fa-arrow-up',
                    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                },
                {
                    id: 2,
                    icon: 'fas fa-shopping-cart',
                    value: '8,642',
                    label: '总订单数',
                    trend: '+8.2%',
                    trendClass: 'trend-up',
                    trendIcon: 'fas fa-arrow-up',
                    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
                },
                {
                    id: 3,
                    icon: 'fas fa-dollar-sign',
                    value: '¥158,642',
                    label: '总收入',
                    trend: '+15.3%',
                    trendClass: 'trend-up',
                    trendIcon: 'fas fa-arrow-up',
                    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
                },
                {
                    id: 4,
                    icon: 'fas fa-chart-line',
                    value: '85.6%',
                    label: '转化率',
                    trend: '-2.4%',
                    trendClass: 'trend-down',
                    trendIcon: 'fas fa-arrow-down',
                    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
                }
            ],
            recentOrders: [
                {id: '#12001', customer: '张三', amount: '1,299.00', status: '已完成'},
                {id: '#12002', customer: '李四', amount: '899.00', status: '处理中'},
                {id: '#12003', customer: '王五', amount: '2,599.00', status: '已发货'},
                {id: '#12004', customer: '赵六', amount: '599.00', status: '已取消'},
                {id: '#12005', customer: '孙七', amount: '1,799.00', status: '已完成'}
            ],
            recentActivities: [
                {
                    id: 1,
                    user: '张三',
                    action: '创建了新订单',
                    time: '2分钟前',
                    avatar: '/default-avatar.png',
                    icon: 'fas fa-plus-circle',
                    color: '#28c76f'
                },
                {
                    id: 2,
                    user: '李四',
                    action: '更新了个人资料',
                    time: '5分钟前',
                    avatar: '/default-avatar.png',
                    icon: 'fas fa-edit',
                    color: '$primary-color'
                },
                {
                    id: 3,
                    user: '王五',
                    action: '上传了新文件',
                    time: '10分钟前',
                    avatar: '/default-avatar.png',
                    icon: 'fas fa-upload',
                    color: '#ff9f43'
                },
                {
                    id: 4,
                    user: '赵六',
                    action: '发表了评论',
                    time: '15分钟前',
                    avatar: '/default-avatar.png',
                    icon: 'fas fa-comment',
                    color: '#00cfe8'
                }
            ],
            quickActions: [
                {
                    id: 1,
                    title: '添加用户',
                    description: '快速创建新用户账户',
                    icon: 'fas fa-user-plus',
                    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    route: '/admin/users/create'
                },
                {
                    id: 2,
                    title: '创建订单',
                    description: '为客户创建新订单',
                    icon: 'fas fa-plus-circle',
                    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    route: '/admin/orders/create'
                },
                {
                    id: 3,
                    title: '系统设置',
                    description: '配置系统参数',
                    icon: 'fas fa-cog',
                    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                    route: '/admin/system/settings'
                },
                {
                    id: 4,
                    title: '数据报表',
                    description: '查看详细数据报表',
                    icon: 'fas fa-chart-bar',
                    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                    route: '/admin/reports'
                }
            ]
        }
    },
    setup() {
        //
        const userStore = useUserStore()
        const notificationStore = useNotificationStore()

        return {
            userStore,
            notificationStore
        }
    },
    mounted() {
        //
        this.initCharts()
        this.startAutoRefresh()
        this.updateCurrentTime()
        this.updateLastUpdated()
        this.startTimeUpdate()
        this.initDemoNotifications()
    },
    beforeUnmount() {
        destroyChart(this.salesChart)
        destroyChart(this.userChart)
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer)
        }
        if (this.timeTimer) {
            clearInterval(this.timeTimer)
        }
    },
    methods: {
        async initCharts() {
            // 延迟执行确保DOM已挂载
            await this.$nextTick()
            try {
                await this.initSalesChart()
                await this.initUserChart()
            } catch (error) {
                console.error('图表初始化失败:', error)
                this.$message.error('图表初始化失败，请刷新页面重试')
            }
        },
        async initSalesChart() {
            if (!this.$refs.salesChart) {
                console.error('销售图表DOM元素未找到')
                return
            }

            // 确保Canvas元素已正确挂载并可见
            await new Promise(resolve => {
                if (this.$refs.salesChart.offsetParent !== null) {
                    resolve()
                } else {
                    const observer = new IntersectionObserver((entries) => {
                        if (entries[0].isIntersecting) {
                            observer.disconnect()
                            resolve()
                        }
                    })
                    observer.observe(this.$refs.salesChart)
                }
            })

            const chartData = {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '销售额',
                    data: [12000, 19000, 8000, 25000, 22000, 30000],
                    borderColor: '$primary-color',
                    backgroundColor: '$primary-color',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                }]
            }

            const customOptions = {
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }

            const config = createSafeChartConfig('line', chartData, customOptions)
            try {
                this.salesChart = await createSafeChart(this.$refs.salesChart, config)
                console.log('销售图表创建成功')
            } catch (error) {
                console.error('创建销售图表失败:', error)
                this.$message.error('销售图表初始化失败')
            }
        },
        async initUserChart() {
            if (!this.$refs.userChart) {
                console.error('用户图表DOM元素未找到')
                return
            }

            // 确保Canvas元素已正确挂载并可见
            await new Promise(resolve => {
                if (this.$refs.userChart.offsetParent !== null) {
                    resolve()
                } else {
                    const observer = new IntersectionObserver((entries) => {
                        if (entries[0].isIntersecting) {
                            observer.disconnect()
                            resolve()
                        }
                    })
                    observer.observe(this.$refs.userChart)
                }
            })

            const chartData = {
                labels: ['新用户', '活跃用户', '沉睡用户'],
                datasets: [{
                    data: [45, 35, 20],
                    backgroundColor: [
                        '$primary-color',
                        '#28c76f',
                        '#ea5455'
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 2,
                    hoverBorderColor: '#fff'
                }]
            }

            const customOptions = {
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                size: 13
                            }
                        }
                    }
                }
            }

            const config = createSafeChartConfig('doughnut', chartData, customOptions)
            try {
                this.userChart = await createSafeChart(this.$refs.userChart, config)
                console.log('用户图表创建成功')
            } catch (error) {
                console.error('创建用户图表失败:', error)
                this.$message.error('用户图表初始化失败')
            }
        },
        getStatusType(status) {
            const statusMap = {
                '已完成': 'success',
                '处理中': 'warning',
                '已发货': 'info',
                '已取消': 'danger'
            }
            return statusMap[status] || 'default'
        },
        handleQuickAction(action) {
            if (action.route) {
                this.$router.push(action.route)
            }
        },

        // 开始自动刷新
        startAutoRefresh() {
            // 每30秒刷新一次数据
            this.refreshTimer = setInterval(() => {
                this.refreshDashboardData()
            }, 30000)
        },

        // 刷新仪表板数据
        async refreshDashboardData() {
            try {
                // 这里可以调用API获取最新数据
                await this.updateStatsData()
                await this.updateRecentActivities()
                await this.updateChartData()
            } catch (error) {
                //
                //console.error('刷新数据失败:', error)
            }
        },

        // 更新统计数据
        async updateStatsData() {
            // 模拟API调用
            const newStats = [
                {
                    id: 1,
                    icon: 'fas fa-users',
                    value: this.generateRandomValue(12000, 13000),
                    label: '总用户数',
                    trend: this.generateRandomTrend(),
                    trendClass: 'trend-up',
                    trendIcon: 'fas fa-arrow-up',
                    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                },
                {
                    id: 2,
                    icon: 'fas fa-shopping-cart',
                    value: this.generateRandomValue(8000, 9000),
                    label: '总订单数',
                    trend: this.generateRandomTrend(),
                    trendClass: 'trend-up',
                    trendIcon: 'fas fa-arrow-up',
                    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
                },
                {
                    id: 3,
                    icon: 'fas fa-dollar-sign',
                    value: `¥${this.generateRandomValue(150000, 170000).toLocaleString()}`,
                    label: '总收入',
                    trend: this.generateRandomTrend(),
                    trendClass: 'trend-up',
                    trendIcon: 'fas fa-arrow-up',
                    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
                },
                {
                    id: 4,
                    icon: 'fas fa-chart-line',
                    value: `${this.generateRandomValue(80, 90).toFixed(1)}%`,
                    label: '转化率',
                    trend: this.generateRandomTrend(),
                    trendClass: 'trend-up',
                    trendIcon: 'fas fa-arrow-up',
                    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
                }
            ]
            this.statsData = newStats
        },

        // 更新最新活动
        async updateRecentActivities() {
            // 模拟添加新活动
            const newActivity = {
                id: Date.now(),
                user: ['张三', '李四', '王五', '赵六'][Math.floor(Math.random() * 4)],
                action: ['登录系统', '更新资料', '上传文件', '创建订单'][Math.floor(Math.random() * 4)],
                time: '刚刚',
                avatar: '/default-avatar.png',
                icon: ['fas fa-sign-in-alt', 'fas fa-edit', 'fas fa-upload', 'fas fa-plus-circle'][Math.floor(Math.random() * 4)],
                color: ['#28c76f', '$primary-color', '#ff9f43', '#00cfe8'][Math.floor(Math.random() * 4)]
            }

            // 添加到列表开头，保持最多5个活动
            this.recentActivities.unshift(newActivity)
            if (this.recentActivities.length > 5) {
                this.recentActivities = this.recentActivities.slice(0, 5)
            }
        },

        // 更新图表数据
        async updateChartData() {
            // 更新销售图表数据
            if (this.salesChart) {
                const newSalesData = {
                    datasets: [{
                        data: Array.from({length: 6}, () =>
                            Math.floor(Math.random() * 20000) + 10000
                        )
                    }]
                }
                updateChartData(this.salesChart, newSalesData)
            }

            // 更新用户图表数据
            if (this.userChart) {
                const newUserData = {
                    datasets: [{
                        data: [
                            Math.floor(Math.random() * 30) + 30,  // 新用户 30-60%
                            Math.floor(Math.random() * 25) + 25,  // 活跃用户 25-50%
                            Math.floor(Math.random() * 20) + 10   // 沉睡用户 10-30%
                        ]
                    }]
                }
                updateChartData(this.userChart, newUserData)
            }
        },

        // 生成随机数值
        generateRandomValue(min, max) {
            return Math.floor(Math.random() * (max - min + 1)) + min
        },

        // 生成随机趋势
        generateRandomTrend() {
            const isPositive = Math.random() > 0.3 // 70%概率为正增长
            const value = (Math.random() * 20).toFixed(1)
            return isPositive ? `+${value}%` : `-${value}%`
        },

        // 手动刷新数据
        async handleRefresh() {
            this.refreshing = true
            this.$message.info('正在刷新数据...')
            try {
                await this.refreshDashboardData()
                this.updateLastUpdated()
                this.$message.success('数据刷新完成！')
            } catch (error) {
                this.$message.error('数据刷新失败！')
            } finally {
                this.refreshing = false
            }
        },

        // 更新当前时间
        updateCurrentTime() {
            const now = new Date()
            this.currentTime = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            })
        },

        // 开始时间更新
        startTimeUpdate() {
            this.timeTimer = setInterval(() => {
                this.updateCurrentTime()
            }, 1000)
        },

        // 更新最后更新时间
        updateLastUpdated() {
            const now = new Date()
            this.lastUpdated = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            })
        },

        // 导出数据
        exportData() {
            this.$message.info('正在导出数据...')

            // 模拟导出过程
            setTimeout(() => {
                const data = {
                    stats: this.statsData,
                    orders: this.recentOrders,
                    activities: this.recentActivities,
                    exportTime: new Date().toISOString()
                }

                const blob = new Blob([JSON.stringify(data, null, 2)], {
                    type: 'application/json'
                })

                const url = URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = `dashboard-data-${Date.now()}.json`
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
                URL.revokeObjectURL(url)

                this.$message.success('数据导出完成！')
            }, 1000)
        },

        // 初始化演示通知
        initDemoNotifications() {
            //
        }
    }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@css/variables' as *;

.dashboard-page {
    padding: 0;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);

    .header-left {
        flex: 1;

        .page-title {
            display: flex;
            align-items: center;
            font-size: 1.75rem;
            font-weight: 600;
            color: #5e5873;
            margin: 0 0 0.5rem 0;

            i {
                margin-right: 0.75rem;
                color: $primary-color;
            }
        }

        .page-subtitle {
            color: #6e6b7b;
            margin: 0;
            font-size: 1rem;
        }
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 1.5rem;

        .header-info {
            text-align: right;

            .current-time {
                font-size: 1rem;
                font-weight: 600;
                color: #5e5873;
                margin-bottom: 0.25rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;

                i {
                    color: $primary-color;
                }
            }

            .last-updated {
                font-size: 0.875rem;
                color: #6e6b7b;
            }
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;

            .el-button {
                width: 40px;
                height: 40px;
            }
        }
    }
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 35px rgba(0, 0, 0, 0.15);
    }

    .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;

        i {
            font-size: 1.5rem;
            color: white;
        }
    }

    .card-content {
        flex: 1;

        .card-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #5e5873;
            margin: 0 0 0.25rem 0;
        }

        .card-label {
            color: #6e6b7b;
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
        }

        .card-trend {
            display: flex;
            align-items: center;
            font-size: 0.75rem;
            font-weight: 500;

            &.trend-up {
                color: #28c76f;
            }

            &.trend-down {
                color: #ea5455;
            }

            i {
                margin-right: 0.25rem;
            }
        }
    }
}

.charts-section {
    margin-bottom: 2rem;
}

.chart-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 1.5rem 0;

        h3 {
            color: #5e5873;
            font-weight: 600;
            margin: 0;
        }
    }

    .chart-content {
        padding: 1rem 1.5rem 1.5rem;
        height: 300px;

        .chart-canvas {
            width: 100% !important;
            height: 100% !important;
        }
    }
}

.data-section {
    margin-bottom: 2rem;
}

.data-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.data-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);

    .data-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 1.5rem 0;

        h3 {
            color: #5e5873;
            font-weight: 600;
            margin: 0;
        }

        .view-all-btn {
            color: $primary-color;
            text-decoration: none;
            font-size: 0.875rem;
            display: flex;
            align-items: center;

            i {
                margin-left: 0.25rem;
                font-size: 0.75rem;
            }

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .data-content {
        padding: 1rem 1.5rem 1.5rem;
    }
}

.amount {
    font-weight: 600;
    color: #28c76f;
}

.activity-list {
    .activity-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f0f1f7;

        &:last-child {
            border-bottom: none;
        }

        .activity-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 0.75rem;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .activity-content {
            flex: 1;

            .activity-text {
                margin: 0 0 0.25rem 0;
                color: #5e5873;
                font-size: 0.875rem;
                line-height: 1.4;
            }

            .activity-time {
                color: #b9b9c3;
                font-size: 0.75rem;
            }
        }

        .activity-icon {
            i {
                font-size: 1rem;
            }
        }
    }
}

.quick-actions {
    .section-title {
        color: #5e5873;
        font-weight: 600;
        margin: 0 0 1rem 0;
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
}

.action-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 35px rgba(0, 0, 0, 0.15);
    }

    .action-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;

        i {
            font-size: 1.25rem;
            color: white;
        }
    }

    h4 {
        color: #5e5873;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
    }

    p {
        color: #6e6b7b;
        margin: 0;
        font-size: 0.875rem;
        line-height: 1.4;
    }
}

@media (max-width: 1200px) {
    .chart-row {
        grid-template-columns: 1fr;
    }

    .data-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;

        .header-right {
            width: 100%;
            justify-content: space-between;

            .header-info {
                text-align: left;
            }
        }
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .actions-grid {
        grid-template-columns: 1fr;
    }

    .header-right {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start !important;
    }
}
</style>
