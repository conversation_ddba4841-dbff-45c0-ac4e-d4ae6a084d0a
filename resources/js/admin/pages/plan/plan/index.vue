<!--
/**
 * 计划管理页面
 *
 * 功能特性：
 * - 企业级计划管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 计划状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/plan/plan
 * 页面标题：计划管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="plan-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索计划名称、负责人或计划编号'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出计划
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchArchive"
                    type="warning"
                    @click="handleBatchArchive"
                >
                    <i class="fas fa-archive"></i>
                    批量归档 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchActivate"
                    type="info"
                    @click="handleBatchActivate"
                >
                    <i class="fas fa-play"></i>
                    批量激活 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="计划状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in PLAN_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="计划类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="项目计划" value="project"></el-option>
                                <el-option label="营销计划" value="marketing"></el-option>
                                <el-option label="生产计划" value="production"></el-option>
                                <el-option label="财务计划" value="financial"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item label="优先级">
                            <el-select v-model="filters.priority" clearable placeholder="选择优先级">
                                <el-option label="全部优先级" value=""></el-option>
                                <el-option label="高" value="high"></el-option>
                                <el-option label="中" value="medium"></el-option>
                                <el-option label="低" value="low"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 计划列表 -->
            <template #default>
                <PlanListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总计划: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						进行中: <strong>{{ activeCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import PlanListView from './components/PlanListView.vue'

export default {
    name: 'AdminPlanPlanIndexPage',
    components: {
        BackendPageListLayout,
        PlanListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                start_date: '',
                end_date: '',
                priority: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            activeCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部计划', icon: 'fas fa-list', badge: 0},
                {name: 'draft', label: '草稿', icon: 'fas fa-edit', badge: 0},
                {name: 'active', label: '进行中', icon: 'fas fa-play-circle', badge: 0},
                {name: 'completed', label: '已完成', icon: 'fas fa-check-circle', badge: 0},
                {name: 'archived', label: '已归档', icon: 'fas fa-archive', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 计划状态标签
            PLAN_STATUS_LABELS: {
                'draft': '草稿',
                'active': '进行中',
                'completed': '已完成',
                'archived': '已归档',
                'cancelled': '已取消'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchArchive() {
            return this.selectedRows.some(row => ['active', 'completed'].includes(row.status))
        },
        canBatchActivate() {
            return this.selectedRows.some(row => row.status === 'draft')
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟计划数据
                const mockData = [
                    {
                        id: 1,
                        plan_no: 'PLAN202401150001',
                        name: '2024年度营销计划',
                        type: 'marketing',
                        status: 'active',
                        priority: 'high',
                        manager: '张三',
                        progress: 65,
                        start_date: '2024-01-01',
                        end_date: '2024-12-31',
                        created_at: '2024-01-15 10:30:00'
                    }
                ]

                this.dataList = mockData
                this.total = 156
                this.activeCount = 89
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 23
            this.tabOptions[2].badge = 89
            this.tabOptions[3].badge = 34
            this.tabOptions[4].badge = 10
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                start_date: '',
                end_date: '',
                priority: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('计划数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchArchive() {
            const archivableItems = this.selectedRows.filter(row =>
                ['active', 'completed'].includes(row.status)
            )

            if (archivableItems.length === 0) {
                this.$message.warning('没有可归档的计划')
                return
            }

            this.$confirm(`确定要归档选中的 ${archivableItems.length} 个计划吗？`, '确认批量归档', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功归档 ${archivableItems.length} 个计划`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchActivate() {
            const activatableItems = this.selectedRows.filter(row =>
                row.status === 'draft'
            )

            if (activatableItems.length === 0) {
                this.$message.warning('没有可激活的计划')
                return
            }

            this.$confirm(`确定要激活选中的 ${activatableItems.length} 个计划吗？`, '确认批量激活', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功激活 ${activatableItems.length} 个计划`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看计划详情: ${row.name}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑计划: ${row.name}`)
        },

        handleDelete(row) {
            this.$message.info(`删除计划: ${row.name}`)
        },

        handleStatusChange(row, newStatus) {
            this.$message.info(`计划状态已更改为: ${this.PLAN_STATUS_LABELS[newStatus]}`)
            this.loadData()
        }
    }
}
</script>
