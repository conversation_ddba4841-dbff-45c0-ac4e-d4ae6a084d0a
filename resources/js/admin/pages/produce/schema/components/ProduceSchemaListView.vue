<!--
/**
 * 生产方案列表视图组件
 *
 * 功能特性：
 * - 生产方案数据表格展示
 * - 方案管理和操作
 * - 状态管理和操作
 * - 响应式设计
 *
 * 版本：v1.0.0
 * 作者：Admin Team
 */
-->

<template>
    <div class="produce-schema-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"/>
            <el-table-column label="ID" prop="id" sortable width="80"/>

            <el-table-column
                label="方案名称"
                min-width="200"
                prop="name"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="schema-name">
                        <i class="fas fa-project-diagram schema-icon"></i>
                        <span class="name-text">{{ row.name }}</span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="方案类型"
                prop="schema_type"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag :type="getSchemaTypeColor(row.schema_type)" size="small">
                        {{ getSchemaTypeName(row.schema_type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="描述"
                min-width="200"
                prop="description"
                show-overflow-tooltip
            />

            <el-table-column
                align="center"
                label="步骤数"
                prop="steps_count"
                width="100"
            >
                <template #default="{ row }">
					<span class="steps-count">
						<i class="fas fa-list-ol"></i>
						{{ row.steps_count }}
					</span>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="预计时间"
                prop="estimated_time"
                width="120"
            />

            <el-table-column
                label="创建人"
                prop="created_by"
                width="120"
            />

            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="180"
            />

            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="320"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleViewDetail(row)"
                    >
                        <i class="fas fa-eye"></i>
                        查看
                    </el-button>
                    <el-button
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        <i class="fas fa-edit"></i>
                        编辑
                    </el-button>
                    <el-button
                        size="small"
                        type="warning"
                        @click="handleCopySchema(row)"
                    >
                        <i class="fas fa-copy"></i>
                        复制
                    </el-button>
                    <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="toggle-status">
                                    <i class="fas fa-toggle-on"></i>
                                    {{ row.status === 'active' ? '禁用' : '启用' }}
                                </el-dropdown-item>
                                <el-dropdown-item command="export">
                                    <i class="fas fa-download"></i>
                                    导出方案
                                </el-dropdown-item>
                                <el-dropdown-item command="view-history">
                                    <i class="fas fa-history"></i>
                                    查看历史
                                </el-dropdown-item>
                                <el-dropdown-item command="delete" divided>
                                    <i class="fas fa-trash"></i>
                                    删除
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'ProduceSchemaListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'delete',
        'status-change',
        'copy-schema'
    ],
    data() {
        return {
            // 状态映射
            statusMap: {
                'active': '启用中',
                'draft': '草稿',
                'archived': '已归档',
                'disabled': '已禁用'
            },

            // 方案类型映射
            schemaTypeMap: {
                'standard': '标准方案',
                'custom': '定制方案',
                'quick': '快速方案',
                'advanced': '高级方案'
            }
        }
    },
    methods: {
        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'active': 'success',
                'draft': 'warning',
                'archived': 'info',
                'disabled': 'danger'
            }
            return typeMap[status] || 'info'
        },

        // 获取方案类型名称
        getSchemaTypeName(type) {
            return this.schemaTypeMap[type] || type
        },

        // 获取方案类型颜色
        getSchemaTypeColor(type) {
            const colorMap = {
                'standard': 'primary',
                'custom': 'success',
                'quick': 'warning',
                'advanced': 'danger'
            }
            return colorMap[type] || 'info'
        },

        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态变更
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 复制方案
        handleCopySchema(row) {
            this.$emit('copy-schema', row)
        },

        // 下拉菜单命令处理
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'toggle-status':
                    const newStatus = row.status === 'active' ? 'disabled' : 'active'
                    this.handleStatusChange(row, newStatus)
                    break
                case 'export':
                    this.$message.info(`导出生产方案 "${row.name}"`)
                    break
                case 'view-history':
                    this.$message.info(`查看方案 "${row.name}" 的历史记录`)
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.produce-schema-list-view {
    .admin-table {
        .schema-name {
            display: flex;
            align-items: center;
            gap: 8px;

            .schema-icon {
                color: #409eff;
                font-size: 16px;
            }

            .name-text {
                font-weight: 500;
            }
        }

        .steps-count {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #606266;

            .fas {
                font-size: 12px;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .produce-schema-list-view {
        :deep(.el-table) {
            .el-table__body-wrapper {
                overflow-x: auto;
            }
        }
    }
}
</style>
