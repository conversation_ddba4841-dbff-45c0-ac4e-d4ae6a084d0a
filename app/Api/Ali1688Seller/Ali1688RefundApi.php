<?php

namespace App\Api\Ali1688Seller;

class Ali1688R<PERSON>und<PERSON>pi extends Ali1688BaseApi
{
    /**
     * 查询退款单列表(卖家视角)
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.4b6655edhsXjEG&ns=com.alibaba.trade&n=alibaba.trade.refund.queryOrderRefundList&v=1
     * @param        $Ali1688AccessToken
     * @param string $order_id
     * @return
     * @API  com.alibaba.trade / alibaba.trade.refund.queryOrderRefundList - 1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.queryOrderRefundList/4451567
     */
    public static function GetAli1688RefundQueryOrderRefundList($Ali1688AccessToken, $order_id, $page = 1, $page_size = 20)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.trade/alibaba.trade.refund.queryOrderRefundList/';
        //
        $body_params = [
            'orderId' => $order_id ?? null,//订单Id
            'applyStartTime' => $applyStartTime ?? null,//退款申请时间（起始）
            'applyEndTime' => $applyEndTime ?? null,//退款申请时间(截止)
            'refundStatusSet' => $refundStatusSet ?? null,//退款状态列表 等待卖家同意 waitselleragree;退款成功 refundsuccess;退款关闭 refundclose;待买家修改 waitbuyermodify;等待买家退货 waitbuyersend;等待卖家确认收货 waitsellerreceive
            'buyerMemberId' => $buyerMemberId ?? null,//买家memberId或者buyerOpenUid（买家加密ID）
            'buyerLoginId' => $buyerLoginId ?? null,//买家loginId或者buyerOpenUid（买家加密ID）
            'currentPageNum' => $page ?? null,//查询页码，起始页码为0
            'pageSize' => $page_size ?? null,//页大小
            'logisticsNo' => $logisticsNo ?? null,//退货运单号
            'modifyStartTime' => $modifyStartTime ?? null,//退款修改时间(起始)
            'modifyEndTime' => $modifyEndTime ?? null,//退款修改时间(截止)
            'dipsuteType' => $dipsuteType ?? null,//1:售中退款，2:售后退款；0:所有退款单
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

    /**
     * 延迟订单确认时间(卖家操作)
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.4b6655edhsXjEG&ns=com.alibaba.trade&n=alibaba.trade.order.PostponeConfirmTime&v=1
     * @param        $Ali1688AccessToken
     * @param string $order_id
     * @return
     * @API  com.alibaba.trade:alibaba.trade.order.PostponeConfirmTime-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/4451567
     */
    public static function GetAli1688RefundPostPoneConfirmTime($Ali1688AccessToken, string $order_id)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/';
        //
        $body_params = [
            'orderId' => $order_id ?? null,//订单Id
            'subPayOrderId' => $subPayOrderId ?? null,//子支付单号，如果传递，该值必须是真实的subPayOrderId
            'delayedDays' => $delayedDays ?? null,//延迟时间（天）
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }


    /**
     * 查询退款单详情-根据订单ID（卖家视角）
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView-1&aopApiCategory=trade_new
     * @param        $Ali1688AccessToken
     * @param string $order_id
     * @return
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/4451567
     */
    public static function GetAli1688RefundOpQueryBatchRefundByOrderIdAndStatusSellerView($Ali1688AccessToken, string $order_id)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/';
        //
        $body_params = [
            'orderId' => $order_id ?? null,//订单Id
            'queryType' => $queryType ?? null,//1：活动；3:退款成功（只支持退款中和退款成功）
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }


    /**
     * 查询退款单详情-根据退款单ID（卖家视角）
     * https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.4b6655edhsXjEG&ns=com.alibaba.trade&n=alibaba.trade.refund.OpQueryOrderRefund.sellerView&v=1
     * @param        $Ali1688AccessToken
     * @param string $order_id
     * @return
     * @API com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefund.sellerView-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/4451567
     */
    public static function GetAli1688RefundOpQueryOrderRefundSellerView($Ali1688AccessToken, string $refundId)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/';
        //
        $body_params = [
            'refundId' => $refundId ?? null,//退款单业务主键 TQ+ID
            'needTimeOutInfo' => $needTimeOutInfo ?? null,//需要退款单的超时信息
            'needOrderRefundOperation' => $needOrderRefundOperation ?? null,//需要退款单伴随的所有退款操作信息
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }


    /**
     * 查询退款单详情-根据订单ID（买家视角）
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.4b6655edhsXjEG&ns=com.alibaba.trade&n=alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus&v=1
     * @param        $Ali1688AccessToken
     * @param string $order_id
     * @return
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/4451567
     */
    public static function GetAli1688RefundOpQueryBatchRefundByOrderIdAndStatus($Ali1688AccessToken, string $order_id)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/';
        //
        $body_params = [
            'orderId' => $order_id ?? null,//订单Id
            'queryType' => $queryType ?? null,//1：活动；3:退款成功（只支持退款中和退款成功）
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }


    /**
     * 查询退款单详情-根据退款单ID（买家视角）
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.4b6655edhsXjEG&ns=com.alibaba.trade&n=alibaba.trade.refund.OpQueryOrderRefund&v=1
     * @param        $Ali1688AccessToken
     * @param string $order_id
     * @return
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefund-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/4451567
     */
    public static function GetAli1688RefundOpQueryOrderRefund($Ali1688AccessToken, string $refundId)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/';
        //
        $body_params = [
            'refundId' => $refundId ?? null,//退款单业务主键 TQ+ID
            'needTimeOutInfo' => $needTimeOutInfo ?? null,//需要退款单的超时信息
            'needOrderRefundOperation' => $needOrderRefundOperation ?? null,//需要退款单伴随的所有退款操作信息
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

    /**
     * 退款单操作-卖家同意退货
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.4b6655edhsXjEG&ns=com.alibaba.trade&n=alibaba.trade.refund.OpAgreeReturnGoods&v=1
     * @param        $Ali1688AccessToken
     * @param string $order_id
     * @return
     * @API  com.alibaba.trade:alibaba.trade.refund.OpAgreeReturnGoods-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/4451567
     */
    public static function PushAli1688RefundOpAgreeReturnGoods($Ali1688AccessToken, string $refundId)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.trade/alibaba.trade.order.PostponeConfirmTime/';
        //
        $body_params = [
            'refundId' => $refundId ?? null,//退款单Id
            'address' => $address ?? null,//卖家收货地址
            'post' => $post ?? null,//邮编
            'phone' => $phone ?? null,//电话
            'fullName' => $fullName ?? null,//全名
            'mobilePhone' => $mobilePhone ?? null,//手机
            'discription' => $discription ?? null,//说明
            'disputeType' => $disputeType ?? null,//1表示售中，2表示售后
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }
}
