<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;


/**
 * Class OrderStateEnum
 * @package App\Enums\State\Order
 * @method static self default() 默认状态
 * @method static self OrderCreated() 订单已创建
 * @method static self UserAuthentication() 用户身份验证
 * @method static self UserPermissionValidation() 用户权限验证
 * @method static self MerchantPermissionValidation() 商家权限验证
 * @method static self UserAntiFraudCheck() 用户反欺诈检查
 * @method static self AddressFormatCheck() 地址格式检查
 * @method static self OrderAddressCorrection() 订单地址纠错
 * @method static self SenderReceiverAddressMatching() 发收地址匹配
 * @method static self AddressReachabilityCheck() 地址可达性检查
 * @method static self DeliveryPathCalculation() 配送路径计算
 * @method static self SizeWeightCalculation() 尺寸重量计算
 * @method static self LogisticsCostEstimation() 物流费用估算
 * @method static self DeliveryTimeEstimation() 配送时效估算
 * @method static self ProductStatusCheck() 产品状态检查
 * @method static self SkuSalesCheck() sku销售检查
 * @method static self PlatformPolicyCheck() 平台政策检查
 * @method static self SalesRuleCheck() 销售规则检查
 * @method static self ProductInventoryCheck() 产品库存检查
 * @method static self ProductDiscountCheck() 产品优惠检查
 * @method static self ReturnPolicyValidation() 退货政策验证
 * @method static self ProductPriceCheck() 产品价格检查
 * @method static self PromotionActivityCheck() 促销活动检查
 * @method static self PromotionRuleCheck() 促销规则检查
 * @method static self PromotionTimeCheck() 促销时效检查
 * @method static self PromotionApplicabilityValidation() 促销适用验证
 * @method static self PromotionRuleCalculation() 促销规则计算
 * @method static self PromotionActualPaymentCalculation() 促销实付计算
 * @method static self PromotionInfoAllocation() 促销信息均摊
 * @method static self DiscountInfoVerification() 优惠信息核销
 * @method static self OrderIntegrityCheck() 订单完整性检查
 * @method static self PreventDuplicateSubmission() 防止重复提交
 * @method static self CrossSellValidation() 交叉销售验证
 * @method static self OrderDiscountValidation() 订单优惠验证
 * @method static self OrderPriceAudit() 订单价格审核
 * @method static self OrderInventoryLock() 订单库存锁定
 * @method static self OrderAmountTotal() 订单金额合计
 * @method static self OrderActualPaymentAllocation() 订单实付均摊
 * @method static self PaymentMethodSwitch() 支付方式切换
 * @method static self PaymentPriceCalculation() 支付价格计算
 * @method static self PaymentInfoValidation() 支付信息验证
 * @method static self PaymentRiskAssessment() 支付风险评估
 * @method static self OptionalPaymentMethods() 可选支付方式
 * @method static self PaymentOrderGeneration() 支付订单生成
 * @method static self PaymentTimeoutReminder() 支付超时提醒
 * @method static self PaymentAmountVerification() 支付金额核实
 * @method static self PaymentOrderReview() 支付单审核
 * @method static self PaymentOrderCompletion() 支付单完成
 * @method static self PaymentStatusChange() 支付状态变更
 * @method static self PaymentTimeoutClosure() 支付超时关闭
 * @method static self OrderPaymentFailure() 订单支付失败
 * @method static self UserRiskControl() 用户风险管控
 * @method static self MerchantRiskControl() 商家风险管控
 * @method static self PlatformRiskControl() 平台风险管控
 * @method static self PaymentRiskControl() 支付风险管控
 * @method static self PaymentSucceed() 支付成功
 * @method static self OrderCreationFailure() 订单创建失败
 * @method static self UserOrderCancellation() 用户取消订单
 * @method static self OrderProcessing() 订单处理中
 * @method static self OrderWaitingPush() 订单等待推送
 * @method static self OrderPushing() 订单推送中
 * @method static self OrderPushSucceed() 订单推送成功
 * @method static self OrderPushFailure() 订单推送失败
 * @method static self OrderWaitingAssign() 订单等待派单
 * @method static self OrderAssigning() 派单中
 * @method static self OrderAssignSucceed() 派单成功
 * @method static self OrderAssignFailure() 派单失败
 * @method static self OrderWaitingAccept() 订单等待接单
 * @method static self OrderAccepting() 接单中
 * @method static self OrderAcceptSucceed() 接单成功
 * @method static self OrderAcceptFailure() 接单失败
 * @method static self OrderWaitingReturn() 等待退单
 * @method static self OrderReturning() 退单中
 * @method static self OrderReturnSucceed() 退单成功
 * @method static self OrderReturnFailure() 退单失败
 * @method static self OrderWaitingUnpack() 等待拆单
 * @method static self OrderUnpacking() 拆单中
 * @method static self OrderUnpackSucceed() 拆单成功
 * @method static self OrderUnpackFailure() 拆单失败
 * @method static self OrderWaitingSplit() 等待分单
 * @method static self OrderSplitting() 分单中
 * @method static self OrderSplitSucceed() 分单成功
 * @method static self OrderSplitFailure() 分单失败
 * @method static self OrderWaitingVerify() 等待验单
 * @method static self OrderVerifying() 验单中
 * @method static self OrderVerifySucceed() 验单成功
 * @method static self OrderVerifyFailure() 验单失败
 * @method static self OrderWaitingDelay() 等待延单
 * @method static self OrderDelaying() 延单中
 * @method static self OrderDelaySucceed() 延单成功
 * @method static self OrderDelayFailure() 延单失败
 * @method static self OrderProcessingException() 订单处理异常
 * @method static self OrderInsufficientStock() 商家库存不足
 * @method static self OrderLogisticsException() 商家物流异常
 * @method static self OrderMerchantRefused() 商家拒绝订单
 * @method static self OrderRiskReassessment() 订单风险再评估
 * @method static self OrderWaitingIssue() 等待处理问题
 * @method static self OrderIssueProcessing() 处理问题中
 * @method static self OrderIssueResolved() 问题解决
 * @method static self OrderIssueUnresolved() 问题未解决
 * @method static self OrderPicking() 配货中
 * @method static self OrderPickingMergeCalculation() 订单合并计算
 * @method static self OrderPickingWarehouseAllocation() 分配仓库
 * @method static self OrderPickingPickerAllocation() 分配拣货员
 * @method static self OrderPickingPickerReceiving() 拣货员收单
 * @method static self OrderPickingBatchGeneration() 生成拣货批次
 * @method static self OrderPickingInventoryLock() 库存锁定
 * @method static self OrderPickingInProgress() 拣货中
 * @method static self OrderPickingBatchReview() 拣货批次复核
 * @method static self OrderPickingMerchantCompletion() 商家拣货完成
 * @method static self OrderPickingServiceProviderCompletion() 服务商拣货完成
 * @method static self OrderPickingHandover() 拣货交接
 * @method static self OrderPickingCompletion() 拣货完成
 * @method static self OrderPickingInsufficientStock() 商品库存不足
 * @method static self OrderPickingQuantityException() 拣货数量异常
 * @method static self OrderPickingTimeOut() 拣货时间超时
 * @method static self OrderPickingIssueHandling() 问题订单处理
 * @method static self OrderPickingFailure() 订单拣货失败
 * @method static * //
 * @method static self OrderShipping() 出货中
 * @method static self OrderShippingAllocation() 出货待分配
 * @method static self OrderShippingAppointment() 出货预约
 * @method static self OrderShippingSorting() 出货待分拣
 * @method static self OrderShippingChecking() 出货待盘点
 * @method static self OrderShippingHandover() 出货待交接
 * @method static self OrderShippingReview() 出货待复核
 * @method static self OrderShippingSortingCompletion() 出货分拣完成
 * @method static self OrderShippingCompletion() 出货完成
 * @method static self OrderShippingTimeOut() 出货时间超时
 * @method static self OrderShippingFailure() 订单出货失败
 * @method static self OrderShipment() 发货中
 * @method static self OrderShipmentAllocation() 发货待分配
 * @method static self OrderShipmentGetExpressNo() 待获取单号
 * @method static self OrderShipmentGetExpressNoSucceed() 单号获取成功
 * @method static self OrderShipmentLabelPrinting() 面单打印中
 * @method static self OrderShipmentLabelPrintingSucceed() 面单打印成功
 * @method static self OrderShipmentWaitingPack() 待打包
 * @method static self OrderShipmentWaitingWeigh() 待称重
 * @method static self OrderShipmentPacking() 打包完成
 * @method static self OrderShipmentTransmit() 发货上报通知
 * @method static self OrderShipmentStockOut() 发货出库完成
 * @method static self OrderShipmentTimeOut() 发货时间超时
 * @method static self OrderShipmentFailure() 订单发货失败
 * @method static self OrderTransport() 运输中
 * @method static self OrderTransportWaitingPickup() 待揽收
 * @method static self OrderTransportPickupTaskAllocation() 揽收任务分配
 * @method static self OrderTransportPickupCompletion() 揽收完成
 * @method static self OrderTransportDriverReceiving() 司机收箱
 * @method static self OrderTransportLeaveOriginDepot() 离开起运地网点
 * @method static self OrderTransportLeaveOriginSortingCenter() 离开起运地分拣中心
 * @method static self OrderTransportInTransit() 运输中
 * @method static self OrderTransportArriveDestinationSortingCenter() 到达目的地分拣中心
 * @method static self OrderTransportArriveDestinationDepot() 到达目的地网点
 * @method static self OrderTransportTrunkCompletion() 干线运输完成
 * @method static self OrderTransportTrunkTimeOut() 干线运输超时
 * @method static self OrderTransportTrunkFailure() 干线运输失败
 * @method static *          //
 * @method static self DeliveryArriveAtDispatchPoint() 到达派件点
 * @method static self DeliveryWaitingArrangement() 等待派送安排
 * @method static self DeliveryPendingConfirmation() 派件待确认
 * @method static self DeliveryPendingSorting() 派件待分拣
 * @method static self DeliveryPendingNotification() 派件待通知
 * @method static self DeliveryInProgress() 派送中
 * @method static self DeliveryExceptionDelay() 派送异常，派送时间延期
 * @method static self DeliveryExceptionIncompleteAddress() 派送异常，地址不详
 * @method static self DeliveryExceptionUnreachableAddress() 派送异常，地址无法到达
 * @method static self DeliveryExceptionUnableToContactRecipient() 派送异常，无法联系收件人
 * @method static self DeliveryRedelivery() 再投递
 * @method static self DeliveryRefuseToSign() 派件拒签
 * @method static self DeliveryCompleted() 派件完成
 * @method static self SelfPickupPending() 待自提
 * @method static self SelfPickupPendingNotification() 派件待通知
 * @method static self SelfPickupInProgress() 自提中
 * @method static self SelfPickupTimeAdjustmentInProgress() 自提时间调整中
 * @method static self SelfPickupTimeout() 自提超时
 * @method static self SelfPickupPointFull() 自提点已满
 * @method static self SelfPickupPointClosedInAdvance() 自提点提前关闭
 * @method static self SelfPickupServiceAdjustmentInProgress() 自提点服务调整中
 * @method static self SelfPickupAddressChangeInProgress() 自提地址更改中
 * @method static self SelfPickupPointFullCapacity() 自提点已满额
 * @method static self SelfPickupPointServiceSuspended() 自提点暂停服务
 * @method static self OrderSigned() 已签收
 * @method static self DeliveryTimeout() 包裹超时
 * @method static self DeliveryRefusedToSign() 包裹拒签
 * @method static self DeliveryDamaged() 包裹损坏
 * @method static self DeliveryLost() 包裹丢失
 * @method static self DeliveryFailure() 包裹失败
 * @method static *          //
 * @method static self ConfirmationPendingReceipt() 待确认收货
 * @method static self BillingGenerationInProgress() 账单生成中
 * @method static self BillingGenerationSucceed() 账单生成成功
 * @method static self BillingGenerationFailure() 账单生成失败
 * @method static self SettlementPending() 待结算
 * @method static self SettlementInProgress() 结算中
 * @method static self Overdue() 逾期
 * @method static self PartialSettlement() 部分结算
 * @method static self CancelSettlement() 取消结算
 * @method static self Settled() 已结算
 * @method static self RefundInProgress() 退款中
 * @method static self RefuseSettlement() 拒绝结算
 * @method static self ReconciliationInProgress() 对账中
 * @method static self ReconciliationSucceed() 对账成功
 * @method static self ReconciliationFailure() 对账失败
 * @method static self CheckoutSucceed() 结账成功
 * @method static self CheckoutFailure() 结账失败
 * @method static self SplitSucceed() 分账成功
 * @method static self SplitFailure() 分账失败
 * @method static self PaymentDelay() 延期付款
 * @method static self AccountInProgress() 挂账中
 * @method static self AccountSucceed() 挂账成功
 * @method static self AccountFailure() 挂账失败
 * @method static self UnlockingInProgress() 解锁中
 * @method static self UnlockingSucceed() 解锁成功
 * @method static self DiscountInProgress() 贴现中
 * @method static self DiscountSucceed() 贴现成功
 * @method static self DiscountFailure() 贴现失败
 * @method static * //
 * @method static self WithdrawalInProgress() 提现中
 * @method static self WithdrawalSucceed() 提现成功
 * @method static self WithdrawalFailure() 提现失败
 * @method static self ReversalInProgress() 冲正中
 * @method static self ReversalSucceed() 冲正成功
 * @method static self ReversalFailure() 冲正失败
 * @method static self RestitutionInProgress() 返还中
 * @method static self RestitutionSucceed() 返还成功
 * @method static self RestitutionFailure() 返还失败
 * @method static self OrderCompleted() 订单完成
 * @method static self WaitingForReview() 等待复核
 * @method static self ReviewPassed() 复核通过
 * @method static self ReviewRejected() 复核拒绝
 * @method static self AccountFrozen() 账户冻结
 * @method static self AccountExpired() 账户过期
 * @method static self AccountFailed() 账户失败
 * @method static self ReconciliationFailed() 账单比对失败
 * @method static self CheckoutFailed() 结现失败
 * @method static self SplitFailed() 账单分账失败
 * @method static self WithdrawalFailed() 转账失败
 * @method static self InsufficientBalance() 余额不足
 * @method static self AuditTimeout() 审核超时
 * @method static self SettlementFailed() 结算失败
 * @method static self ConfirmationFailed() 确认失败
 * @method static self OrderLocked() 订单锁定
 * @method static self PaymentTimeout() 付款超时
 * @method static self PaymentInterception() 支付拦截
 * @method static self UserCanceled() 用户主动取消
 * @method static self UnsupportedShipping() 不支持发货
 * @method static self LogisticsControlled() 物流管控中
 * @method static self LogisticsCanceled() 物流商取消
 * @method static self RefundApplication() 申请退款
 * @method static self ReturnedOrderCanceled() 退件订单取消
 * @method static self ExceptionalSignature() 签收异常
 * @method static self ReturnRefund() 退货退款
 * @method static self OrderClosed() 订单关闭
 * @method static self OrderFinish() 订单完结
 * @method static self SystemMaintenance() 系统维护中
 * @method static self SystemFailure() 系统故障
 * @method static self OrderArchived() 订单归档
 */
final class OrderStateEnum extends BaseEnum
{


    /**
     * 状态值定义
     * @return array
     */
    protected static function values(): array
    {
        return [
            'default' => 0,//默认状态
            //订单创建与支付
            'OrderCreated' => 1000,//订单已创建
            //信息（info Check）
            'UserAuthentication' => 1111,//用户身份验证
            'UserPermissionValidation' => 1131,//用户权限验证
            'MerchantPermissionValidation' => 1141,//商家权限验证
            'UserAntiFraudCheck' => 1151,//用户反欺诈检查
            //物流（Shipment Check）
            'AddressFormatCheck' => 1211,//地址格式检查
            'OrderAddressCorrection' => 1221,//订单地址纠错
            'SenderReceiverAddressMatching' => 1231,//发收地址匹配
            'AddressReachabilityCheck' => 1241,//地址可达性检查
            'DeliveryPathCalculation' => 1251,//配送路径计算
            'SizeWeightCalculation' => 1271,//尺寸重量计算
            'LogisticsCostEstimation' => 1281,//物流费用估算
            'DeliveryTimeEstimation' => 1291,//配送时效估算
            //产品（Product Check）
            'ProductStatusCheck' => 1311,//产品状态检查
            'SkuSalesCheck' => 1321,//sku销售检查
            'PlatformPolicyCheck' => 1331,//平台政策检查
            'SalesRuleCheck' => 1341,//销售规则检查
            'ProductInventoryCheck' => 1351,//产品库存检查
            'ProductDiscountCheck' => 1361,//产品优惠检查
            'ReturnPolicyValidation' => 1371,//退货政策验证
            'ProductPriceCheck' => 1391,//产品价格检查
            //促销（Promotion Check）
            'PromotionActivityCheck' => 1411,//促销活动检查
            'PromotionRuleCheck' => 1421,//促销规则检查
            'PromotionTimeCheck' => 1431,//促销时效检查
            'PromotionApplicabilityValidation' => 1441,//促销适用验证
            'PromotionRuleCalculation' => 1451,//促销规则计算
            'PromotionActualPaymentCalculation' => 1461,//促销实付计算
            'PromotionInfoAllocation' => 1471,//促销信息均摊
            'DiscountInfoVerification' => 1481,//优惠信息核销
            //订单（Order Check）
            'OrderIntegrityCheck' => 1511,//订单完整性检查
            'PreventDuplicateSubmission' => 1521,//防止重复提交
            'CrossSellValidation' => 1531,//交叉销售验证
            'OrderDiscountValidation' => 1541,//订单优惠验证
            'OrderPriceAudit' => 1551,//订单价格审核
            'OrderInventoryLock' => 1561,//订单库存锁定
            'OrderAmountTotal' => 1571,//订单金额合计
            'OrderActualPaymentAllocation' => 1581,//订单实付均摊
            //支付（Payment Check）
            'PaymentMethodSwitch' => 1591,//支付方式切换
            'PaymentPriceCalculation' => 1611,//支付价格计算
            'PaymentInfoValidation' => 1621,//支付信息验证
            'PaymentRiskAssessment' => 1631,//支付风险评估
            'OptionalPaymentMethods' => 1641,//可选支付方式
            'PaymentOrderGeneration' => 1651,//支付订单生成
            'PaymentTimeoutReminder' => 1661,//支付超时提醒
            'PaymentAmountVerification' => 1671,//支付金额核实
            'PaymentOrderReview' => 1675,//支付单审核
            'PaymentOrderCompletion' => 1680,//支付单完成
            'PaymentStatusChange' => 1681,//支付状态变更
            'PaymentTimeoutClosure' => 1691,//支付超时关闭
            'OrderPaymentFailure' => 1695,//订单支付失败
            //风控（Risk Check）
            'UserRiskControl' => 1711,//用户风险管控
            'MerchantRiskControl' => 1731,//商家风险管控
            'PlatformRiskControl' => 1751,//平台风险管控
            'PaymentRiskControl' => 1771,//支付风险管控
            //支付成功（Payment Succeed）
            'PaymentSucceed' => 1800,//支付成功
            //订单（Order Fail）
            'OrderCreationFailure' => 1911,//订单创建失败
            'UserOrderCancellation' => 1921,//用户取消订单
            //订单处理与分配
            //推单（Push Order）:
            'OrderProcessing' => 2000,//订单处理中
            'OrderWaitingPush' => 2100,//订单等待推送
            'OrderPushing' => 2111,//订单推送中
            'OrderPushSucceed' => 2181,//订单推送成功
            'OrderPushFailure' => 2191,//订单推送失败
            'OrderWaitingAssign' => 2200,//订单等待派单
            //派单（Assign Order）:
            'OrderAssigning' => 2211,//派单中
            'OrderAssignSucceed' => 2281,//派单成功
            'OrderAssignFailure' => 2291,//派单失败
            //接单（Accept Order）:
            'OrderWaitingAccept' => 2300,//订单等待接单
            'OrderAccepting' => 2311,//接单中
            'OrderAcceptSucceed' => 2381,//接单成功
            'OrderAcceptFailure' => 2391,//接单失败
            'OrderWaitingReturn' => 2400,//等待退单
            //退单（Return Order）:
            'OrderReturning' => 2411,//退单中
            'OrderReturnSucceed' => 2481,//退单成功
            'OrderReturnFailure' => 2491,//退单失败
            //拆单（Unpack Order）:
            'OrderWaitingUnpack' => 2500,//等待拆单
            'OrderUnpacking' => 2511,//拆单中
            'OrderUnpackSucceed' => 2581,//拆单成功
            'OrderUnpackFailure' => 2591,//拆单失败
            //分单（Split Order）:
            'OrderWaitingSplit' => 2600,//等待分单
            'OrderSplitting' => 2611,//分单中
            'OrderSplitSucceed' => 2681,//分单成功
            'OrderSplitFailure' => 2691,//分单失败
            //验单（Verify Order）:
            'OrderWaitingVerify' => 2700,//等待验单
            'OrderVerifying' => 2711,//验单中
            'OrderVerifySucceed' => 2781,//验单成功
            'OrderVerifyFailure' => 2791,//验单失败
            //延单（Delay Order）:
            'OrderWaitingDelay' => 2800,//等待延单
            'OrderDelaying' => 2811,//延单中
            'OrderDelaySucceed' => 2881,//延单成功
            'OrderDelayFailure' => 2891,//延单失败
            //问题单（Issue Order）:
            'OrderProcessingException' => 2900,//订单处理异常
            'OrderInsufficientStock' => 2911,//商家库存不足
            'OrderLogisticsException' => 2921,//商家物流异常
            'OrderMerchantRefused' => 2931,//商家拒绝订单
            'OrderRiskReassessment' => 2951,//订单风险再评估
            'OrderWaitingIssue' => 2961,//等待处理问题
            'OrderIssueProcessing' => 2971,//处理问题中
            'OrderIssueResolved' => 2981,//问题解决
            'OrderIssueUnresolved' => 2991,//问题未解决
            //配货中
            'OrderPicking' => 3000,//配货中
            'OrderPickingMergeCalculation' => 3101,//订单合并计算
            'OrderPickingWarehouseAllocation' => 3111,//分配仓库
            'OrderPickingPickerAllocation' => 3211,//分配拣货员
            'OrderPickingPickerReceiving' => 3221,//拣货员收单
            'OrderPickingBatchGeneration' => 3311,//生成拣货批次
            'OrderPickingInventoryLock' => 3321,//库存锁定
            'OrderPickingInProgress' => 3411,//拣货中
            'OrderPickingBatchReview' => 3511,//拣货批次复核
            'OrderPickingMerchantCompletion' => 3611,//商家拣货完成
            'OrderPickingServiceProviderCompletion' => 3651,//服务商拣货完成
            'OrderPickingHandover' => 3711,//拣货交接
            'OrderPickingCompletion' => 3800,//拣货完成
            'OrderPickingInsufficientStock' => 3911,//商品库存不足
            'OrderPickingQuantityException' => 3921,//拣货数量异常
            'OrderPickingTimeOut' => 3931,//拣货时间超时
            'OrderPickingIssueHandling' => 3961,//问题订单处理
            'OrderPickingFailure' => 3991,//订单拣货失败
            //出货中
            'OrderShipping' => 4000,//出货中
            'OrderShippingAllocation' => 4111,//出货待分配
            'OrderShippingAppointment' => 4211,//出货预约
            'OrderShippingSorting' => 4311,//出货待分拣
            'OrderShippingChecking' => 4411,//出货待盘点
            'OrderShippingHandover' => 4511,//出货待交接
            'OrderShippingReview' => 4611,//出货待复核
            'OrderShippingSortingCompletion' => 4711,//出货分拣完成
            'OrderShippingCompletion' => 4800,//出货完成
            'OrderShippingTimeOut' => 4911,//出货时间超时
            'OrderShippingFailure' => 4999,//订单出货失败
            //发货中
            'OrderShipment' => 5000,//发货中
            'OrderShipmentAllocation' => 5111,//发货待分配
            'OrderShipmentGetExpressNo' => 5211,//待获取单号
            'OrderShipmentGetExpressNoSucceed' => 5251,//单号获取成功
            'OrderShipmentLabelPrinting' => 5311,//面单打印中
            'OrderShipmentLabelPrintingSucceed' => 5321,//面单打印成功
            'OrderShipmentWaitingPack' => 5411,//待打包
            'OrderShipmentWaitingWeigh' => 5451,//待称重
            'OrderShipmentPacking' => 5511,//打包完成
            'OrderShipmentTransmit' => 5611,//发货上报通知
            'OrderShipmentStockOut' => 5800,//发货出库完成
            'OrderShipmentTimeOut' => 5911,//发货时间超时
            'OrderShipmentFailure' => 5999,//订单发货失败
            //运输中
            'OrderTransport' => 6000,//运输中
            'OrderTransportWaitingPickup' => 6111,//待揽收
            'OrderTransportPickupTaskAllocation' => 6211,//揽收任务分配
            'OrderTransportPickupCompletion' => 6311,//揽收完成
            'OrderTransportDriverReceiving' => 6321,//司机收箱
            'OrderTransportLeaveOriginDepot' => 6411,//离开起运地网点
            'OrderTransportLeaveOriginSortingCenter' => 6421,//离开起运地分拣中心
            'OrderTransportInTransit' => 6511,//运输中
            'OrderTransportArriveDestinationSortingCenter' => 6611,//到达目的地分拣中心
            'OrderTransportArriveDestinationDepot' => 6711,//到达目的地网点
            'OrderTransportTrunkCompletion' => 6800,//干线运输完成
            'OrderTransportTrunkTimeOut' => 6911,//干线运输超时
            'OrderTransportTrunkFailure' => 6999,//干线运输失败
            //派送中
            'OrderDistributionArriveAtDispatchPoint' => 7000,//到达派件点
            'OrderDistributionWaitingArrangement' => 7111,//等待派送安排
            'OrderDistributionPendingConfirmation' => 7121,//派件待确认
            'OrderDistributionPendingSorting' => 7211,//派件待分拣
            'OrderDistributionPendingNotification' => 7251,//派件待通知
            'OrderDistributionInProgress' => 7311,//派送中
            'OrderDistributionExceptionDelay' => 7411,//派送异常，派送时间延期
            'OrderDistributionExceptionIncompleteAddress' => 7421,//派送异常，地址不详
            'OrderDistributionExceptionUnreachableAddress' => 7425,//派送异常，地址无法到达
            'OrderDistributionExceptionUnableToContactRecipient' => 7431,//派送异常，无法联系收件人
            'OrderDistributionRedelivery' => 7441,//再投递
            'OrderDistributionRefuseToSign' => 7461,//派件拒签
            'OrderDistributionCompleted' => 7500,//派件完成
            //自提
            'OrderSelfPickupPending' => 7611,//待自提
            'OrderSelfPickupPendingNotification' => 7621,//派件待通知
            'OrderSelfPickupInProgress' => 7631,//自提中
            'OrderSelfPickupTimeAdjustmentInProgress' => 7711,//自提时间调整中
            'OrderSelfPickupTimeout' => 7721,//自提超时
            'OrderSelfPickupPointFull' => 7731,//自提点已满
            'OrderSelfPickupPointClosedInAdvance' => 7741,//自提点提前关闭
            'OrderSelfPickupServiceAdjustmentInProgress' => 7751,//自提点服务调整中
            'OrderSelfPickupAddressChangeInProgress' => 7761,//自提地址更改中
            'OrderSelfPickupPointFullCapacity' => 7771,//自提点已满额
            'OrderSelfPickupPointServiceSuspended' => 7791,//自提点暂停服务
            //
            'OrderSigned' => 7800,//已签收
            //
            'OrderDistributionTimeout' => 7911,//包裹超时
            'OrderDistributionRefusedToSign' => 7921,//包裹拒签
            'OrderDistributionDamaged' => 7931,//包裹损坏
            'OrderDistributionLost' => 7941,//包裹丢失
            'OrderDistributionFailure' => 7999,//包裹失败
            //结算与账单
            'ConfirmationPendingReceipt' => 8000,//待确认收货
            //账单生成阶段：
            'BillingGenerationInProgress' => 8010,//账单生成中
            'BillingGenerationSucceed' => 8080,//账单生成成功
            'BillingGenerationFailure' => 8091,//账单生成失败
            //结算阶段：
            'SettlementPending' => 8100,//待结算
            'SettlementInProgress' => 8111,//结算中
            'Overdue' => 8121,//逾期
            'PartialSettlement' => 8131,//部分结算
            'CancelSettlement' => 8141,//取消结算
            'Settled' => 8150,//已结算
            'RefundInProgress' => 8161,//退款中
            'RefuseSettlement' => 8171,//拒绝结算
            //对账阶段：
            'ReconciliationInProgress' => 8211,//对账中
            'ReconciliationSucceed' => 8221,//对账成功
            'ReconciliationFailure' => 8241,//对账失败
            //结账阶段：
            'CheckoutSucceed' => 8250,//结账成功
            'CheckoutFailure' => 8261,//结账失败
            'SplitSucceed' => 8280,//分账成功
            'SplitFailure' => 8291,//分账失败
            //挂账阶段：
            'PaymentDelay' => 8311,//延期付款
            'AccountInProgress' => 8321,//挂账中
            'AccountSucceed' => 8330,//挂账成功
            'AccountFailure' => 8341,//挂账失败
            'UnlockingInProgress' => 8351,//解锁中
            'UnlockingSucceed' => 8380,//解锁成功
            //贴现阶段：
            'DiscountInProgress' => 8411,//贴现中
            'DiscountSucceed' => 8430,//贴现成功
            'DiscountFailure' => 8441,//贴现失败
            //提现阶段：
            'WithdrawalInProgress' => 8511,//提现中
            'WithdrawalSucceed' => 8530,//提现成功
            'WithdrawalFailure' => 8541,//提现失败
            //冲正阶段：
            'ReversalInProgress' => 8600,//冲正中
            'ReversalSucceed' => 8630,//冲正成功
            'ReversalFailure' => 8641,//冲正失败
            //返还阶段：
            'RestitutionInProgress' => 8700,//返还中
            'RestitutionSucceed' => 8711,//返还成功
            'RestitutionFailure' => 8741,//返还失败
            //复核阶段：
            'OrderCompleted' => 8800,//订单完成
            'WaitingForReview' => 8811,//等待复核
            'ReviewPassed' => 8821,//复核通过
            'ReviewRejected' => 8841,//复核拒绝
            //异常阶段：
            'AccountFrozen' => 8911,//账户冻结
            'AccountExpired' => 8915,//账户过期
            'AccountFailed' => 8917,//账户失败
            'ReconciliationFailed' => 8921,//账单比对失败
            'CheckoutFailed' => 8929,//结现失败
            'SplitFailed' => 8931,//账单分账失败
            'WithdrawalFailed' => 8941,//转账失败
            'InsufficientBalance' => 8951,//余额不足
            'AuditTimeout' => 8981,//审核超时
            'SettlementFailed' => 8991,//结算失败
            'ConfirmationFailed' => 8999,//确认失败
            //订单异常
            'OrderLocked' => 9000,//订单锁定
            'PaymentTimeout' => 9111,//付款超时
            'PaymentInterception' => 9121,//支付拦截
            'UserCanceled' => 9131,//用户主动取消
            'UnsupportedShipping' => 9211,//不支持发货
            'LogisticsControlled' => 9311,//物流管控中
            'LogisticsCanceled' => 9411,//物流商取消
            'RefundApplication' => 9511,//申请退款
            'ReturnedOrderCanceled' => 9611,//退件订单取消
            'ExceptionalSignature' => 9621,//签收异常
            'ReturnRefund' => 9711,//退货退款
            'OrderClosed' => 9751,//订单关闭
            'OrderFinish' => 9811,//订单完结
            'SystemMaintenance' => 9911,//系统维护中
            'SystemFailure' => 9921,//系统故障
            'OrderArchived' => 9999,//订单归档
        ];
    }

    /**
     * 状态标签定义
     * @return array
     */
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
            // 订单创建与支付
            'OrderCreated' => '订单已创建',
            // 信息（信息检查）
            'UserAuthentication' => '用户身份验证',
            'UserPermissionValidation' => '用户权限验证',
            'MerchantPermissionValidation' => '商家权限验证',
            'UserAntiFraudCheck' => '用户反欺诈检查',
            // 物流（物流检查）
            'AddressFormatCheck' => '地址格式检查',
            'OrderAddressCorrection' => '订单地址纠错',
            'SenderReceiverAddressMatching' => '发收地址匹配',
            'AddressReachabilityCheck' => '地址可达性检查',
            'DeliveryPathCalculation' => '配送路径计算',
            'SizeWeightCalculation' => '尺寸重量计算',
            'LogisticsCostEstimation' => '物流费用估算',
            'DeliveryTimeEstimation' => '配送时效估算',
            // 产品（产品检查）
            'ProductStatusCheck' => '产品状态检查',
            'SkuSalesCheck' => 'sku销售检查',
            'PlatformPolicyCheck' => '平台政策检查',
            'SalesRuleCheck' => '销售规则检查',
            'ProductInventoryCheck' => '产品库存检查',
            'ProductDiscountCheck' => '产品优惠检查',
            'ReturnPolicyValidation' => '退货政策验证',
            'ProductPriceCheck' => '产品价格检查',
            // 促销（促销检查）
            'PromotionActivityCheck' => '促销活动检查',
            'PromotionRuleCheck' => '促销规则检查',
            'PromotionTimeCheck' => '促销时效检查',
            'PromotionApplicabilityValidation' => '促销适用验证',
            'PromotionRuleCalculation' => '促销规则计算',
            'PromotionActualPaymentCalculation' => '促销实付计算',
            'PromotionInfoAllocation' => '促销信息均摊',
            'DiscountInfoVerification' => '优惠信息核销',
            // 订单（订单检查）
            'OrderIntegrityCheck' => '订单完整性检查',
            'PreventDuplicateSubmission' => '防止重复提交',
            'CrossSellValidation' => '交叉销售验证',
            'OrderDiscountValidation' => '订单优惠验证',
            'OrderPriceAudit' => '订单价格审核',
            'OrderInventoryLock' => '订单库存锁定',
            'OrderAmountTotal' => '订单金额合计',
            'OrderActualPaymentAllocation' => '订单实付均摊',
            // 支付（支付检查）
            'PaymentMethodSwitch' => '支付方式切换',
            'PaymentPriceCalculation' => '支付价格计算',
            'PaymentInfoValidation' => '支付信息验证',
            'PaymentRiskAssessment' => '支付风险评估',
            'OptionalPaymentMethods' => '可选支付方式',
            'PaymentOrderGeneration' => '支付订单生成',
            'PaymentTimeoutReminder' => '支付超时提醒',
            'PaymentAmountVerification' => '支付金额核实',
            'PaymentOrderReview' => '支付单审核',
            'PaymentOrderCompletion' => '支付单完成',
            'PaymentStatusChange' => '支付状态变更',
            'PaymentTimeoutClosure' => '支付超时关闭',
            'OrderPaymentFailure' => '订单支付失败',
            // 风控（风险检查）
            'UserRiskControl' => '用户风险管控',
            'MerchantRiskControl' => '商家风险管控',
            'PlatformRiskControl' => '平台风险管控',
            'PaymentRiskControl' => '支付风险管控',
            // 支付成功（支付成功）
            'PaymentSucceed' => '支付成功',
            // 订单失败（订单失败）
            'OrderCreationFailure' => '订单创建失败',
            'UserOrderCancellation' => '用户取消订单',
            // 订单处理与分配
            // 推单（推单）:
            'OrderProcessing' => '订单处理中',
            'OrderWaitingPush' => '订单等待推送',
            'OrderPushing' => '订单推送中',
            'OrderPushSucceed' => '订单推送成功',
            'OrderPushFailure' => '订单推送失败',
            'OrderWaitingAssign' => '订单等待派单',
            // 派单（派单）:
            'OrderAssigning' => '派单中',
            'OrderAssignSucceed' => '派单成功',
            'OrderAssignFailure' => '派单失败',
            // 接单（接单）:
            'OrderWaitingAccept' => '订单等待接单',
            'OrderAccepting' => '接单中',
            'OrderAcceptSucceed' => '接单成功',
            'OrderAcceptFailure' => '接单失败',
            'OrderWaitingReturn' => '等待退单',
            // 退单（退单）:
            'OrderReturning' => '退单中',
            'OrderReturnSucceed' => '退单成功',
            'OrderReturnFailure' => '退单失败',
            // 拆单（拆单）:
            'OrderWaitingUnpack' => '等待拆单',
            'OrderUnpacking' => '拆单中',
            'OrderUnpackSucceed' => '拆单成功',
            'OrderUnpackFailure' => '拆单失败',
            // 分单（分单）:
            'OrderWaitingSplit' => '等待分单',
            'OrderSplitting' => '分单中',
            'OrderSplitSucceed' => '分单成功',
            'OrderSplitFailure' => '分单失败',
            // 验单（验单）:
            'OrderWaitingVerify' => '等待验单',
            'OrderVerifying' => '验单中',
            'OrderVerifySucceed' => '验单成功',
            'OrderVerifyFailure' => '验单失败',
            // 延单（延单）:
            'OrderWaitingDelay' => '等待延单',
            'OrderDelaying' => '延单中',
            'OrderDelaySucceed' => '延单成功',
            'OrderDelayFailure' => '延单失败',
            // 问题单（问题单）:
            'OrderProcessingException' => '订单处理异常',
            'OrderInsufficientStock' => '商家库存不足',
            'OrderLogisticsException' => '商家物流异常',
            'OrderMerchantRefused' => '商家拒绝订单',
            'OrderRiskReassessment' => '订单风险再评估',
            'OrderWaitingIssue' => '等待处理问题',
            'OrderIssueProcessing' => '处理问题中',
            'OrderIssueResolved' => '问题解决',
            'OrderIssueUnresolved' => '问题未解决',
            // 配货中
            'OrderPicking' => '配货中',
            'OrderPickingMergeCalculation' => '订单合并计算',
            'OrderPickingWarehouseAllocation' => '分配仓库',
            'OrderPickingPickerAllocation' => '分配拣货员',
            'OrderPickingPickerReceiving' => '拣货员收单',
            'OrderPickingBatchGeneration' => '生成拣货批次',
            'OrderPickingInventoryLock' => '库存锁定',
            'OrderPickingInProgress' => '拣货中',
            'OrderPickingBatchReview' => '拣货批次复核',
            'OrderPickingMerchantCompletion' => '商家拣货完成',
            'OrderPickingServiceProviderCompletion' => '服务商拣货完成',
            'OrderPickingHandover' => '拣货交接',
            'OrderPickingCompletion' => '拣货完成',
            'OrderPickingInsufficientStock' => '商品库存不足',
            'OrderPickingQuantityException' => '拣货数量异常',
            'OrderPickingTimeOut' => '拣货时间超时',
            'OrderPickingIssueHandling' => '问题订单处理',
            'OrderPickingFailure' => '订单拣货失败',
            // 出货中
            'OrderShipping' => '出货中',
            'OrderShippingAllocation' => '出货待分配',
            'OrderShippingAppointment' => '出货预约',
            'OrderShippingSorting' => '出货待分拣',
            'OrderShippingChecking' => '出货待盘点',
            'OrderShippingHandover' => '出货待交接',
            'OrderShippingReview' => '出货待复核',
            'OrderShippingSortingCompletion' => '出货分拣完成',
            'OrderShippingCompletion' => '出货完成',
            'OrderShippingTimeOut' => '出货时间超时',
            'OrderShippingFailure' => '订单出货失败',
            //发货中
            'OrderShipment' => '发货中',
            'OrderShipmentAllocation' => '发货待分配',
            'OrderShipmentGetExpressNo' => '待获取单号',
            'OrderShipmentGetExpressNoSucceed' => '单号获取成功',
            'OrderShipmentLabelPrinting' => '面单打印中',
            'OrderShipmentLabelPrintingSucceed' => '面单打印成功',
            'OrderShipmentWaitingPack' => '待打包',
            'OrderShipmentWaitingWeigh' => '待称重',
            'OrderShipmentPacking' => '打包完成',
            'OrderShipmentTransmit' => '发货上报通知',
            'OrderShipmentStockOut' => '发货出库完成',
            'OrderShipmentTimeOut' => '发货时间超时',
            'OrderShipmentFailure' => '订单发货失败',
            // 运输中
            'OrderTransport' => '运输中',
            'OrderTransportWaitingPickup' => '待揽收',
            'OrderTransportPickupTaskAllocation' => '揽收任务分配',
            'OrderTransportPickupCompletion' => '揽收完成',
            'OrderTransportDriverReceiving' => '司机收箱',
            'OrderTransportLeaveOriginDepot' => '离开起运地网点',
            'OrderTransportLeaveOriginSortingCenter' => '离开起运地分拣中心',
            'OrderTransportInTransit' => '干线运输中',
            'OrderTransportArriveDestinationSortingCenter' => '到达目的地分拣中心',
            'OrderTransportArriveDestinationDepot' => '到达目的地网点',
            'OrderTransportTrunkCompletion' => '干线运输完成',
            'OrderTransportTrunkTimeOut' => '干线运输超时',
            'OrderTransportTrunkFailure' => '干线运输失败',
            // 派送中
            'OrderDistributionArriveAtDispatchPoint' => '到达派件点',
            'OrderDistributionWaitingArrangement' => '等待派送安排',
            'OrderDistributionPendingConfirmation' => '派件待确认',
            'OrderDistributionPendingSorting' => '派件待分拣',
            'OrderDistributionPendingNotification' => '派件待通知',
            'OrderDistributionInProgress' => '派送中',
            'OrderDistributionExceptionDelay' => '派送异常，派送时间延期',
            'OrderDistributionExceptionIncompleteAddress' => '派送异常，地址不详',
            'OrderDistributionExceptionUnreachableAddress' => '派送异常，地址无法到达',
            'OrderDistributionExceptionUnableToContactRecipient' => '派送异常，无法联系收件人',
            'OrderDistributionRedelivery' => '再投递',
            'OrderDistributionRefuseToSign' => '派件拒签',
            'OrderDistributionCompleted' => '派件完成',
            // 自提
            'OrderSelfPickupPending' => '待自提',
            'OrderSelfPickupPendingNotification' => '自提待通知',
            'OrderSelfPickupInProgress' => '自提中',
            'OrderSelfPickupTimeAdjustmentInProgress' => '自提时间调整中',
            'OrderSelfPickupTimeout' => '自提超时',
            'OrderSelfPickupPointFull' => '自提点已满',
            // 自提点
            'OrderSelfPickupPointClosedInAdvance' => '自提点提前关闭',
            'OrderSelfPickupServiceAdjustmentInProgress' => '自提点服务调整中',
            'OrderSelfPickupAddressChangeInProgress' => '自提地址更改中',
            'OrderSelfPickupPointFullCapacity' => '自提点已满额',
            'OrderSelfPickupPointServiceSuspended' => '自提点暂停服务',
            // 派件
            'OrderSigned' => '已签收',
            'OrderDistributionTimeout' => '包裹超时',
            'OrderDistributionRefusedToSign' => '包裹拒签',
            'OrderDistributionDamaged' => '包裹损坏',
            'OrderDistributionLost' => '包裹丢失',
            'OrderDistributionFailure' => '包裹失败',
            // 结算与账单
            'ConfirmationPendingReceipt' => '待确认收货',
            // 账单生成阶段
            'BillingGenerationInProgress' => '账单生成中',
            'BillingGenerationSucceed' => '账单生成成功',
            'BillingGenerationFailure' => '账单生成失败',
            // 结算阶段
            'SettlementPending' => '待结算',
            'SettlementInProgress' => '结算中',
            'Overdue' => '逾期',
            'PartialSettlement' => '部分结算',
            'CancelSettlement' => '取消结算',
            'Settled' => '已结算',
            'RefundInProgress' => '退款中',
            'RefuseSettlement' => '拒绝结算',
            // 对账阶段
            'ReconciliationInProgress' => '对账中',
            'ReconciliationSucceed' => '对账成功',
            'ReconciliationFailure' => '对账失败',
            // 结账阶段
            'CheckoutSucceed' => '结账成功',
            'CheckoutFailure' => '结账失败',
            'SplitSucceed' => '分账成功',
            'SplitFailure' => '分账失败',
            // 挂账阶段
            'PaymentDelay' => '延期付款',
            'AccountInProgress' => '挂账中',
            'AccountSucceed' => '挂账成功',
            'AccountFailure' => '挂账失败',
            'UnlockingInProgress' => '解锁中',
            'UnlockingSucceed' => '解锁成功',
            // 贴现阶段
            'DiscountInProgress' => '贴现中',
            'DiscountSucceed' => '贴现成功',
            'DiscountFailure' => '贴现失败',
            // 提现阶段
            'WithdrawalInProgress' => '提现中',
            'WithdrawalSucceed' => '提现成功',
            'WithdrawalFailure' => '提现失败',
            // 冲正阶段
            'ReversalInProgress' => '冲正中',
            'ReversalSucceed' => '冲正成功',
            'ReversalFailure' => '冲正失败',
            // 返还阶段
            'RestitutionInProgress' => '返还中',
            'RestitutionSucceed' => '返还成功',
            'RestitutionFailure' => '返还失败',
            // 复核阶段
            'OrderCompleted' => '订单完成',
            'WaitingForReview' => '等待复核',
            'ReviewPassed' => '复核通过',
            'ReviewRejected' => '复核拒绝',
            // 异常阶段
            'AccountFrozen' => '账户冻结',
            'AccountExpired' => '账户过期',
            'AccountFailed' => '账户失败',
            'ReconciliationFailed' => '账单比对失败',
            'CheckoutFailed' => '结现失败',
            'SplitFailed' => '账单分账失败',
            'WithdrawalFailed' => '转账失败',
            'InsufficientBalance' => '余额不足',
            'AuditTimeout' => '审核超时',
            'SettlementFailed' => '结算失败',
            'ConfirmationFailed' => '确认失败',
            // 订单异常
            'OrderLocked' => '订单锁定',
            'PaymentTimeout' => '付款超时',
            'PaymentInterception' => '支付拦截',
            'UserCanceled' => '用户主动取消',
            'UnsupportedShipping' => '不支持发货',
            'LogisticsControlled' => '物流管控中',
            'LogisticsCanceled' => '物流商取消',
            'RefundApplication' => '申请退款',
            'ReturnedOrderCanceled' => '退件订单取消',
            'ExceptionalSignature' => '签收异常',
            'ReturnRefund' => '退货退款',
            'OrderClosed' => '订单关闭',
            'OrderFinish' => '订单完结',
            'SystemMaintenance' => '系统维护中',
            'SystemFailure' => '系统故障',
            'OrderArchived' => '订单归档',
        ];
    }

    /**
     * 状态颜色定义
     * @return array
     */
    protected static function colors(): array
    {
        return [
            0 => 'red',
            1000 => 'green',
            // 根据需要为其他状态添加颜色
        ];
    }
}
