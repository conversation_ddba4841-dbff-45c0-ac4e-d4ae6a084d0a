<?php

namespace App\Observers\Store;

use App\Models\StoreCenter\StoreClass;

class StoreClassObserver
{
    /**
     * 处理 StoreClass "created" 事件.
     */
    public function created(StoreClass $storeClass): void
    {
        //
    }

    /**
     * 处理 StoreClass "updated" 事件.
     */
    public function updated(StoreClass $storeClass): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($storeClass->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($storeClass->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * 处理 StoreClass "deleted" 事件.
     */
    public function deleted(StoreClass $storeClass): void
    {
        //
    }

    /**
     * 处理 StoreClass "restored" 事件.
     */
    public function restored(StoreClass $storeClass): void
    {
        //
    }

    /**
     * 处理 StoreClass "force deleted" 事件.
     */
    public function forceDeleted(StoreClass $storeClass): void
    {
        //
    }
}
