<!--
/**
 * 用途管理页面
 *
 * 功能特性：
 * - 管理产品用途和应用场景
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * - 用途分类管理
 *
 * 路由路径：/admin/produce/purpose
 * 页面标题：用途管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="purpose-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索用途名称、关键词或应用场景'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="用途类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="日常使用" value="daily"></el-option>
                                <el-option label="商业用途" value="commercial"></el-option>
                                <el-option label="工业用途" value="industrial"></el-option>
                                <el-option label="特殊用途" value="special"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="应用领域">
                            <el-select v-model="filters.field" clearable placeholder="选择领域">
                                <el-option label="全部领域" value=""></el-option>
                                <el-option label="服装" value="clothing"></el-option>
                                <el-option label="家居" value="home"></el-option>
                                <el-option label="装饰" value="decoration"></el-option>
                                <el-option label="其他" value="other"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 用途列表 -->
            <template #default>
                <PurposeListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总用途: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import PurposeListView from './components/PurposeListView.vue'

export default {
    name: 'AdminProducePurposeIndexPage',
    components: {
        BackendPageListLayout,
        PurposeListView
    },
    data() {
        return {
            // 加载状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                type: '',
                field: '',
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部用途', value: 'all'},
                {label: '日常使用', value: 'daily'},
                {label: '商业用途', value: 'commercial'},
                {label: '工业用途', value: 'industrial'},
                {label: '特殊用途', value: 'special'}
            ],

            // 数据列表
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            this.loading = true
            this.v_loading = true
            try {
                // 模拟API调用
                await this.simulateApiCall()
                this.generateMockData()
            } catch (error) {
                console.error('加载数据失败:', error)
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // 模拟API调用
        simulateApiCall() {
            return new Promise(resolve => {
                setTimeout(resolve, 800)
            })
        },

        // 生成模拟数据
        generateMockData() {
            const types = ['daily', 'commercial', 'industrial', 'special']
            const fields = ['clothing', 'home', 'decoration', 'other']
            const typeLabels = {
                daily: '日常使用',
                commercial: '商业用途',
                industrial: '工业用途',
                special: '特殊用途'
            }
            const fieldLabels = {
                clothing: '服装',
                home: '家居',
                decoration: '装饰',
                other: '其他'
            }

            const purposes = [
                '休闲穿着', '正式场合', '运动健身', '户外活动', '工作服装',
                '家居装饰', '窗帘布艺', '床上用品', '沙发套件', '地毯材料',
                '工业防护', '特殊环境', '医疗用品', '汽车内饰', '包装材料',
                '艺术创作', '手工制作', '展示用品', '礼品包装', '节庆装饰'
            ]

            this.dataList = Array.from({length: 50}, (_, index) => {
                const type = types[index % types.length]
                const field = fields[index % fields.length]
                const purpose = purposes[index % purposes.length]

                return {
                    id: index + 1,
                    name: purpose,
                    code: `PURPOSE${String(index + 1).padStart(3, '0')}`,
                    type: type,
                    type_label: typeLabels[type],
                    field: field,
                    field_label: fieldLabels[field],
                    description: `适用于${fieldLabels[field]}领域的${purpose}场景`,
                    keywords: this.generateKeywords(purpose, fieldLabels[field]),
                    applications: this.generateApplications(purpose),
                    status: Math.random() > 0.2 ? 'active' : 'inactive',
                    sort_order: index + 1,
                    usage_count: Math.floor(Math.random() * 1000),
                    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    updated_at: new Date().toISOString().split('T')[0]
                }
            })
            this.total = this.dataList.length
        },

        // 生成关键词
        generateKeywords(purpose, field) {
            const keywords = [purpose, field, '高品质', '耐用', '舒适']
            return keywords.slice(0, 3).join(', ')
        },

        // 生成应用场景
        generateApplications(purpose) {
            const applications = [
                `${purpose}的主要应用`,
                `适合${purpose}的场景`,
                `${purpose}相关产品`
            ]
            return applications.slice(0, 2).join('; ')
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                type: '',
                field: '',
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.currentPage = 1
            this.loadData()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的用途吗？', '批量删除', {
                type: 'warning'
            }).then(() => {
                this.selectedRows = []
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑用途: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除用途 "${row.name}" 吗？`, '删除确认', {
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看用途详情: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`${row.name} 状态已更新`)
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.purpose-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        align-items: center;
        gap: 24px;
        color: #606266;
        font-size: 14px;

        .stat-item {
            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
