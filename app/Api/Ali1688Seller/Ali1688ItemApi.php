<?php

namespace App\Api\Ali1688Seller;

use Exception;

class Ali1688I<PERSON><PERSON>pi extends Ali1688BaseApi
{
    /**
     * 从Ali1688获取并更新商品列表
     * @param array $Ali1688AccessToken
     * @param int $page 当前页码，默认为1
     * @param int $page_size 每页显示数量，默认为3
     * @return null
     * @throws Exception
     */
    public static function GetAli1688ProductList($Ali1688AccessToken, int $page = 1, int $page_size = 20)
    {
        //
        set_time_limit(0); // 设置执行时间不受限制
        //
        $apiInfo = 'param2/1/com.alibaba.product/alibaba.product.list.get/'; // API接口信息
        // 构建请求参数
        $body_params = [
            'pageNo' => $page,//分页参数，起始为1
            'pageSize' => $page_size,//分页参数，每页大小，目前最大值为20，超过20时以20为准,'expired','new','modified','TBD','deleted'
            'statusList' => "['published']",//商品状态，published:上架状态，expired：过期(包含手动过期与自动过期)，TBD：to be delete，deleted：删除，new：新发，modified：修改，member expired：会员撤销
            //                'categoryId'           => '1031728',//类目ID
            //                'startModifyTime'      => '20180717174002000+0800',//产品修改开始时间
            //                'endModifyTime'        => '20180717174002000+0800',//产品修改结束时间
            //                'subjectKey'           => '北极',//关键词
            //                'cargoNumber'          => 'LYQ-8816',//货号
            //                'productIds'           => '[12323213,12312323]',//产品ID列表
            //                'beginStar'            => '0',//星级范围起始值0-5，不能高于endStar
            //                'endStar'              => '5',//星级范围起始值0-5，不能低于beginStar
            //                'groupIds'             => '100416219',//查询自定义分组ID，目前只支持查询一个
            //                'startPublishTime'     => '20180717174002000+0800',//发布时间起始值
            //                'endPublishTime'       => '20180717174002000+0800',//发布时间结束值
            //                'startExpiredTime'     => '20180717174002000+0800',//过期时间起始值
            //                'endExpiredTime'       => '20180717174002000+0800',//过期时间结束值
            //                'priceStart'           => '1.0',//价格范围起始值
            //                'priceEnd'             => '1000.0',//价格范围结束值
            //                'orderByCondition'     => 'PRICE',//排序条件：CREATE_DATE,POST_DATE,MODIFY_DATE,APPROVED_DATE,EXPIRE_DATE,STATUS,ID,GROUP_ID,PRICE,SALE_QUANTITY
            //                'orderByType'          => 'DESC',//升序 ASC 还是降序 DESC
            //                'supportOnlineTrade'   => true,//支持在线交易
            //                'privateOffer'         => false,//私密商品
            //                'needDetail'           => true,//是否需要详情
            //                'needFreight'          => true,//是否需要运费
            //                'needUserCategoryInfo' => true,//是否需要自定义类目
        ];
        //
        try {
            // 调用实际的 API 请求方法，传递凭证和参数
            return Ali1688BaseApi::callApi(
                $Ali1688AccessToken['client_id'],
                $Ali1688AccessToken['client_secret'],
                $Ali1688AccessToken['access_token'],
                $apiInfo,
                $body_params,
                'GET'
            );
        } catch (Exception $e) {
            // 捕获异常并输出错误信息
            throw new Exception('API请求失败：' . $e->getMessage());
        }
    }

    /**
     * 获取指定商品的详细信息
     * @param  $appid
     * * @param  $secret
     * * @param  $access_token
     * * @param int $product_id 商品ID
     * * @param array $retail_al
     * @API  com.alibaba.product/alibaba.product.get-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.get/7587553
     * @return mixed 返回商品详细信息
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.get-1&aopApiCategory=product_new
     */
    public static function GetAli1688Product($Ali1688AccessToken, int $product_id)
    {
        //
        set_time_limit(0);
        //
        $apiInfo = 'param2/1/com.alibaba.product/alibaba.product.get/';
        //
        $body_params = [
            'productID' => $product_id,//商品ID
            //                'webSite'   => "",//站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688）
            //                'scene'     => "",//业务场景 零售通(lst) 1688市场(1688)
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

    /**
     * 推送商品信息至Ali1688
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.add-1&aopApiCategory=product_new
     * @param     $appid
     *                        * @param  $secret
     *                        * @param  $access_token
     * @param int $product_id 商品ID
     * @return mixed
     * @API  com.alibaba.product / alibaba.product.add - 1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.add/7587553
     */
    public static function PushAli1688Product($Ali1688AccessToken, int $product_id)
    {
        //
        set_time_limit(0);
        //
        $apiInfo = 'param2/1/com.alibaba.product/alibaba.product.add/';
        //
        $body_params = [
            'productID' => $product_id,//商品ID
            //                'webSite'   => "",//站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688）
            //                'scene'     => "",//业务场景 零售通(lst) 1688市场(1688)
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

    /**
     * 根据关键字搜索类目
     * @param array $Ali1688AccessToken
     * @param string $keyword
     * @return mixed
     */
    public static function searchCategoryByKeyword($Ali1688AccessToken, $keyword)
    {
        //
        $apiPath = 'param2/1/com.alibaba.category/alibaba.category.searchByKeyword/';
        $params = [
            'keyword' => $keyword,
        ];
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }

    /**
     * 根据类目Id查询类目
     * @param array $Ali1688AccessToken
     * @param int $categoryId
     * @return mixed
     */
    public static function getCategoryById($Ali1688AccessToken, $categoryId)
    {
        $apiPath = 'param2/1/com.alibaba.category/alibaba.category.get/';
        $params = [
            'categoryId' => $categoryId,
        ];
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }

    /**
     * 获取用户产品线信息
     * @param array $Ali1688AccessToken
     * @return mixed
     */
    public static function getProductLines($Ali1688AccessToken)
    {
        $apiPath = 'param2/1/com.alibaba.productLines/alibaba.productLines.get/';
        $params = [];
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }

    /**
     * 获取商品发布规则和详情
     * @param array $Ali1688AccessToken
     * @return mixed
     */
    public static function getProductPublishSchema($Ali1688AccessToken)
    {
        $apiPath = 'param2/1/com.alibaba.new.product/alibaba.new.product.getSchema/';
        $params = [];
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }

    /**
     * 获取商品发布级联子组件规则
     * @param array $Ali1688AccessToken
     * @return mixed
     */
    public static function getProductPublishSubSchema($Ali1688AccessToken)
    {
        $apiPath = 'param2/1/com.alibaba.new.product/alibaba.new.product.getSubSchema/';
        $params = [];
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }

    /**
     * 新版商品发布
     * @param array $Ali1688AccessToken
     * @param array $productData
     * @return mixed
     */
    public static function addNewProduct($Ali1688AccessToken, array $productData)
    {
        $apiPath = 'param2/1/com.alibaba.new.product/alibaba.new.product.add/';
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $productData,
            'POST'
        );
    }

    /**
     * 商品发布异步信息查询
     * @param array $Ali1688AccessToken
     * @param string $asyncInfoId
     * @return mixed
     */
    public static function asyncQueryInfo($Ali1688AccessToken, $asyncInfoId)
    {
        $apiPath = 'param2/1/com.alibaba.product/product.asyncQueryInfo.getInfo/';
        $params = [
            'asyncInfoId' => $asyncInfoId,
        ];
        return self::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiPath,
            $params,
            'GET'
        );
    }
}
