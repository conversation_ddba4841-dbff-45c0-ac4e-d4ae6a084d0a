// ========================================
// Workhub Core 企业级设计系统 - SCSS 变量
// 技术栈: Laravel Blade + Vue 3 + Element Plus + Tailwind CSS + Vite + SCSS
// 设计风格: Modern, Clean, Professional, Enterprise-grade
// 中文注释用于中国合规
// ========================================

@use 'sass:color';
@use 'sass:math';

// ========================================
// 1. 基础色彩系统 (Base Color Palette)
// ========================================

// 主色调 (Primary Colors) - #5247ef
$primary-color: #5247ef;
$primary-light: #7b6ff3;
$primary-lighter: #a396f7;
$primary-lightest: #e8e6fd;
$primary-dark: #453de8;
$primary-darker: #3831d4;

// 次要色调 (Secondary Colors) - #1664FF
$secondary-color: #1664FF;
$secondary-light: #4d84ff;
$secondary-lighter: #80a4ff;
$secondary-lightest: #e6f0ff;
$secondary-dark: #0052e6;
$secondary-darker: #0041cc;

// 状态颜色 (Status Colors)
$success-color: #04c717;
$success-light: #48da89;
$success-lighter: #71e5a3;
$success-lightest: #e8f8f0;
$success-dark: #1f9d55;
$success-darker: #178344;

$warning-color: #FF5000;
$warning-light: #ffb976;
$warning-lighter: #ffd3a9;
$warning-lightest: #fff3e6;
$warning-dark: #e6832a;
$warning-darker: #cc6718;

$danger-color: #FF0036;
$danger-light: #ff6b6c;
$danger-lighter: #ff9899;
$danger-lightest: #ffe6e6;
$danger-dark: #ce3e3f;
$danger-darker: #b22a2b;

$info-color: #97a6ba;
$info-light: #b4c1d1;
$info-lighter: #d1dce8;
$info-lightest: #f4f6f8;
$info-dark: #7a8fa6;
$info-darker: #909399;

// 中性色彩 (Neutral Colors) - 基于 #97a6ba 灰色系统优化
$white: #ffffff;
$black: #000000;
$gray-50: #f8f9fb;
$gray-100: #f1f3f6;
$gray-200: #e8ecf0;
$gray-300: #d4dae2;
$gray-400: #b8c2ce;
$gray-500: #97a6ba; // 主灰色 - 与您的配色方案匹配
$gray-600: #7a8fa6;
$gray-700: #909399;
$gray-800: #40617e;
$gray-900: #909399;

// 明暗主题色 (Light/Dark Theme) - 基于优化的灰色系统
$light-color: #f8f9fb;
$light-dark: #f1f3f6;
$dark-color: #909399;
$dark-light: #40617e;
$dark-lighter: #909399;

// ========================================
// 2. 透明度色彩系统 (Alpha Colors) - 企业级增强版
// ========================================

// 主色调透明度系统 - 完整层次
$primary-alpha-03: rgba($primary-color, 0.03);
$primary-alpha-05: rgba($primary-color, 0.05);
$primary-alpha-08: rgba($primary-color, 0.08);
$primary-alpha-10: rgba($primary-color, 0.1);
$primary-alpha-12: rgba($primary-color, 0.12);
$primary-alpha-15: rgba($primary-color, 0.15);
$primary-alpha-18: rgba($primary-color, 0.18);
$primary-alpha-20: rgba($primary-color, 0.2);
$primary-alpha-25: rgba($primary-color, 0.25);
$primary-alpha-30: rgba($primary-color, 0.3);
$primary-alpha-40: rgba($primary-color, 0.4);
$primary-alpha-50: rgba($primary-color, 0.5);
$primary-alpha-60: rgba($primary-color, 0.6);
$primary-alpha-70: rgba($primary-color, 0.7);
$primary-alpha-80: rgba($primary-color, 0.8);
$primary-alpha-90: rgba($primary-color, 0.9);

// 次要色调透明度系统 - 完整层次
$secondary-alpha-03: rgba($secondary-color, 0.03);
$secondary-alpha-05: rgba($secondary-color, 0.05);
$secondary-alpha-08: rgba($secondary-color, 0.08);
$secondary-alpha-10: rgba($secondary-color, 0.1);
$secondary-alpha-12: rgba($secondary-color, 0.12);
$secondary-alpha-15: rgba($secondary-color, 0.15);
$secondary-alpha-18: rgba($secondary-color, 0.18);
$secondary-alpha-20: rgba($secondary-color, 0.2);
$secondary-alpha-25: rgba($secondary-color, 0.25);
$secondary-alpha-30: rgba($secondary-color, 0.3);
$secondary-alpha-40: rgba($secondary-color, 0.4);
$secondary-alpha-50: rgba($secondary-color, 0.5);
$secondary-alpha-60: rgba($secondary-color, 0.6);
$secondary-alpha-70: rgba($secondary-color, 0.7);
$secondary-alpha-80: rgba($secondary-color, 0.8);
$secondary-alpha-90: rgba($secondary-color, 0.9);

// 状态颜色透明度系统 - 企业级完整层次
$success-alpha-03: rgba($success-color, 0.03);
$success-alpha-05: rgba($success-color, 0.05);
$success-alpha-08: rgba($success-color, 0.08);
$success-alpha-10: rgba($success-color, 0.1);
$success-alpha-12: rgba($success-color, 0.12);
$success-alpha-15: rgba($success-color, 0.15);
$success-alpha-18: rgba($success-color, 0.18);
$success-alpha-20: rgba($success-color, 0.2);
$success-alpha-25: rgba($success-color, 0.25);
$success-alpha-30: rgba($success-color, 0.3);

$warning-alpha-03: rgba($warning-color, 0.03);
$warning-alpha-05: rgba($warning-color, 0.05);
$warning-alpha-08: rgba($warning-color, 0.08);
$warning-alpha-10: rgba($warning-color, 0.1);
$warning-alpha-12: rgba($warning-color, 0.12);
$warning-alpha-15: rgba($warning-color, 0.15);
$warning-alpha-18: rgba($warning-color, 0.18);
$warning-alpha-20: rgba($warning-color, 0.2);
$warning-alpha-25: rgba($warning-color, 0.25);
$warning-alpha-30: rgba($warning-color, 0.3);

$danger-alpha-03: rgba($danger-color, 0.03);
$danger-alpha-05: rgba($danger-color, 0.05);
$danger-alpha-08: rgba($danger-color, 0.08);
$danger-alpha-10: rgba($danger-color, 0.1);
$danger-alpha-12: rgba($danger-color, 0.12);
$danger-alpha-15: rgba($danger-color, 0.15);
$danger-alpha-18: rgba($danger-color, 0.18);
$danger-alpha-20: rgba($danger-color, 0.2);
$danger-alpha-25: rgba($danger-color, 0.25);
$danger-alpha-30: rgba($danger-color, 0.3);

$info-alpha-03: rgba($info-color, 0.03);
$info-alpha-05: rgba($info-color, 0.05);
$info-alpha-08: rgba($info-color, 0.08);
$info-alpha-10: rgba($info-color, 0.1);
$info-alpha-12: rgba($info-color, 0.12);
$info-alpha-15: rgba($info-color, 0.15);
$info-alpha-18: rgba($info-color, 0.18);
$info-alpha-20: rgba($info-color, 0.2);
$info-alpha-25: rgba($info-color, 0.25);
$info-alpha-30: rgba($info-color, 0.3);

// 黑白透明度系统 - 企业级完整层次
$white-alpha-03: rgba($white, 0.03);
$white-alpha-05: rgba($white, 0.05);
$white-alpha-08: rgba($white, 0.08);
$white-alpha-10: rgba($white, 0.1);
$white-alpha-12: rgba($white, 0.12);
$white-alpha-15: rgba($white, 0.15);
$white-alpha-18: rgba($white, 0.18);
$white-alpha-20: rgba($white, 0.2);
$white-alpha-25: rgba($white, 0.25);
$white-alpha-30: rgba($white, 0.3);
$white-alpha-40: rgba($white, 0.4);
$white-alpha-50: rgba($white, 0.5);
$white-alpha-60: rgba($white, 0.6);
$white-alpha-70: rgba($white, 0.7);
$white-alpha-80: rgba($white, 0.8);
$white-alpha-85: rgba($white, 0.85);
$white-alpha-90: rgba($white, 0.9);
$white-alpha-95: rgba($white, 0.95);
$white-alpha-98: rgba($white, 0.98);

$black-alpha-03: rgba($black, 0.03);
$black-alpha-05: rgba($black, 0.05);
$black-alpha-08: rgba($black, 0.08);
$black-alpha-10: rgba($black, 0.1);
$black-alpha-12: rgba($black, 0.12);
$black-alpha-15: rgba($black, 0.15);
$black-alpha-18: rgba($black, 0.18);
$black-alpha-20: rgba($black, 0.2);
$black-alpha-25: rgba($black, 0.25);
$black-alpha-30: rgba($black, 0.3);
$black-alpha-40: rgba($black, 0.4);
$black-alpha-50: rgba($black, 0.5);
$black-alpha-60: rgba($black, 0.6);
$black-alpha-70: rgba($black, 0.7);
$black-alpha-80: rgba($black, 0.8);
$black-alpha-90: rgba($black, 0.9);

// ========================================
// 2.5. 企业级灰色透明度系统 (Gray Alpha Colors) - 新增
// ========================================

// 基于主灰色 #97a6ba 的透明度系统
$gray-alpha-03: rgba($gray-500, 0.03);
$gray-alpha-05: rgba($gray-500, 0.05);
$gray-alpha-08: rgba($gray-500, 0.08);
$gray-alpha-10: rgba($gray-500, 0.1);
$gray-alpha-12: rgba($gray-500, 0.12);
$gray-alpha-15: rgba($gray-500, 0.15);
$gray-alpha-18: rgba($gray-500, 0.18);
$gray-alpha-20: rgba($gray-500, 0.2);
$gray-alpha-25: rgba($gray-500, 0.25);
$gray-alpha-30: rgba($gray-500, 0.3);
$gray-alpha-40: rgba($gray-500, 0.4);
$gray-alpha-50: rgba($gray-500, 0.5);
$gray-alpha-60: rgba($gray-500, 0.6);
$gray-alpha-70: rgba($gray-500, 0.7);
$gray-alpha-80: rgba($gray-500, 0.8);
$gray-alpha-90: rgba($gray-500, 0.9);

// 不同灰色层级的透明度系统
$gray-100-alpha-50: rgba($gray-100, 0.5);
$gray-200-alpha-50: rgba($gray-200, 0.5);
$gray-300-alpha-30: rgba($gray-300, 0.3);
$gray-300-alpha-50: rgba($gray-300, 0.5);
$gray-400-alpha-30: rgba($gray-400, 0.3);
$gray-400-alpha-50: rgba($gray-400, 0.5);
$gray-600-alpha-30: rgba($gray-600, 0.3);
$gray-600-alpha-50: rgba($gray-600, 0.5);
$gray-700-alpha-30: rgba($gray-700, 0.3);
$gray-700-alpha-50: rgba($gray-700, 0.5);
$gray-800-alpha-30: rgba($gray-800, 0.3);
$gray-800-alpha-50: rgba($gray-800, 0.5);
$gray-900-alpha-30: rgba($gray-900, 0.3);
$gray-900-alpha-50: rgba($gray-900, 0.5);

// ========================================
// 3. 语义化颜色 (Semantic Colors) - 企业级增强
// ========================================

// 文字颜色 (Text Colors) - 企业级增强系统
$text-primary: #909399;
$text-secondary: #909399;
$text-tertiary: #7a8fa6; // 三级文字颜色
$text-light: #97a6ba;
$text-lighter: #b8c2ce;
$text-lightest: #d4dae2;
$text-dark: #40617e;
$text-darker: #909399;
$text-muted: #97a6ba; // 与主灰色匹配，减少眼疲劳
$text-muted-light: #b8c2ce;
$text-muted-lighter: #d4dae2;

// 特殊文字颜色 - 企业级场景
$text-inverse: $white; // 反色文字
$text-disabled: #d4dae2; // 禁用状态文字
$text-placeholder-light: #b8c2ce; // 浅色占位符
$text-placeholder-dark: #7a8fa6; // 深色占位符
$text-link: $primary-color; // 链接文字
$text-link-hover: $primary-dark; // 链接悬停
$text-link-visited: $primary-darker; // 已访问链接
$text-error: $danger-color; // 错误文字
$text-success: $success-color; // 成功文字
$text-warning: $warning-color; // 警告文字
$text-info: $info-color; // 信息文字

// 标题颜色 (Heading Colors) - 企业级增强
$heading-primary: #909399;
$heading-secondary: #40617e;
$heading-light: #909399;
$heading-lighter: #7a8fa6;
$heading-muted: #97a6ba;
$heading-inverse: $white; // 反色标题

// 背景颜色 (Background Colors) - 企业级增强系统
$body-bg: #f8f9fb;
$body-bg-dark: #909399;
$body-bg-darker: #1a3a52; // 更深的暗色背景
$body-bg-light: #ffffff;
$body-bg-lighter: #fafbfc; // 更浅的背景

$card-bg: #ffffff;
$card-bg-secondary: #f8f9fb;
$card-bg-tertiary: #f1f3f6;
$card-bg-hover: #f8f9fb; // 卡片悬停背景
$card-bg-active: #f1f3f6; // 卡片激活背景
$card-bg-disabled: #e8ecf0; // 卡片禁用背景

// 特殊背景颜色
$bg-overlay: $black-alpha-50; // 遮罩背景
$bg-overlay-light: $black-alpha-30; // 浅色遮罩
$bg-overlay-dark: $black-alpha-70; // 深色遮罩
$bg-glass: $white-alpha-80; // 毛玻璃效果
$bg-glass-dark: $black-alpha-20; // 深色毛玻璃
$bg-selected: $primary-alpha-08; // 选中状态背景
$bg-selected-hover: $primary-alpha-12; // 选中悬停背景

// 边框颜色 (Border Colors) - 企业级增强系统
$border-color: #e8ecf0;
$border-color-light: #f1f3f6;
$border-color-lighter: #f8f9fb;
$border-color-lightest: #fafbfc;
$border-color-extra-light: #f8f9fb;
$border-color-dark: #d4dae2;
$border-color-darker: #b8c2ce;
$border-color-darkest: #97a6ba;

// 特殊边框颜色
$border-color-focus: $primary-color; // 焦点边框
$border-color-focus-light: $primary-light; // 浅色焦点边框
$border-color-error: $danger-color; // 错误边框
$border-color-success: $success-color; // 成功边框
$border-color-warning: $warning-color; // 警告边框
$border-color-info: $info-color; // 信息边框
$border-color-disabled: #d4dae2; // 禁用边框
$border-color-hover: #b8c2ce; // 悬停边框
$border-color-active: #97a6ba; // 激活边框

// 分隔符颜色
$divider-color: #e8ecf0;
$divider-color-light: #f1f3f6;
$divider-color-dark: #d4dae2;
$divider-color-vertical: #e8ecf0; // 垂直分隔符

// Element Plus 兼容边框颜色 - 基于优化的灰色系统
$border-light: rgba($gray-300, 0.3);
$text-regular: #909399;
$text-placeholder: #97a6ba;
$bg-white: #ffffff;
$bg-light: #f8f9fb;
$bg-lighter: #f1f3f6;
$bg-hover: #e8ecf0;
$header-bg: $body-bg;

// ========================================
// 3.5. 企业级功能颜色系统 (Functional Colors) - 新增
// ========================================

// 状态指示颜色
$status-online: #10b981; // 在线状态
$status-offline: #6b7280; // 离线状态
$status-away: #f59e0b; // 离开状态
$status-busy: #ef4444; // 忙碌状态
$status-idle: #8b5cf6; // 空闲状态

// 优先级颜色
$priority-critical: #dc2626; // 紧急
$priority-high: #ea580c; // 高优先级
$priority-medium: #d97706; // 中优先级
$priority-low: #65a30d; // 低优先级
$priority-none: #6b7280; // 无优先级

// 进度颜色
$progress-complete: $success-color; // 完成
$progress-in-progress: $primary-color; // 进行中
$progress-pending: $warning-color; // 待处理
$progress-blocked: $danger-color; // 阻塞
$progress-cancelled: $gray-500; // 已取消

// 标签颜色系统 - 企业级多彩标签
$tag-blue: #3b82f6;
$tag-blue-light: #dbeafe;
$tag-green: #10b981;
$tag-green-light: #d1fae5;
$tag-yellow: #f59e0b;
$tag-yellow-light: #fef3c7;
$tag-red: #ef4444;
$tag-red-light: #fee2e2;
$tag-purple: #8b5cf6;
$tag-purple-light: #ede9fe;
$tag-pink: #ec4899;
$tag-pink-light: #fce7f3;
$tag-indigo: #6366f1;
$tag-indigo-light: #e0e7ff;
$tag-teal: #14b8a6;
$tag-teal-light: #ccfbf1;
$tag-orange: #f97316;
$tag-orange-light: #fed7aa;
$tag-cyan: #06b6d4;
$tag-cyan-light: #cffafe;

// 图表颜色系统 - 数据可视化
$chart-primary: $primary-color;
$chart-secondary: $secondary-color;
$chart-accent-1: #8b5cf6; // 紫色
$chart-accent-2: #10b981; // 绿色
$chart-accent-3: #f59e0b; // 黄色
$chart-accent-4: #ef4444; // 红色
$chart-accent-5: #06b6d4; // 青色
$chart-accent-6: #ec4899; // 粉色
$chart-accent-7: #84cc16; // 青绿色
$chart-accent-8: #f97316; // 橙色

// 渐变颜色系统
$gradient-primary: linear-gradient(135deg, #{$primary-color} 0%, #{$primary-light} 100%);
$gradient-secondary: linear-gradient(135deg, #{$secondary-color} 0%, #{$secondary-light} 100%);
$gradient-success: linear-gradient(135deg, #{$success-color} 0%, #{$success-light} 100%);
$gradient-warning: linear-gradient(135deg, #{$warning-color} 0%, #{$warning-light} 100%);
$gradient-danger: linear-gradient(135deg, #{$danger-color} 0%, #{$danger-light} 100%);
$gradient-info: linear-gradient(135deg, #{$info-color} 0%, #{$info-light} 100%);
$gradient-gray: linear-gradient(135deg, #{$gray-400} 0%, #{$gray-500} 100%);

// 特殊效果颜色
$glow-primary: 0 0 20px #{$primary-alpha-30}; // 主色发光
$glow-success: 0 0 20px #{$success-alpha-30}; // 成功发光
$glow-warning: 0 0 20px #{$warning-alpha-30}; // 警告发光
$glow-danger: 0 0 20px #{$danger-alpha-30}; // 危险发光

// ========================================
// 4. 阴影系统 (Shadow System) - 企业级增强
// ========================================

// 基础阴影系统
$box-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$box-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$box-shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$box-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
$box-shadow-3xl: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
$box-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
$box-shadow-inner-lg: inset 0 4px 8px 0 rgba(0, 0, 0, 0.1);
$box-shadow-none: none;

// 企业级特殊阴影
$box-shadow-card: 0 2px 8px 0 rgba(0, 0, 0, 0.08); // 卡片阴影
$box-shadow-card-hover: 0 4px 16px 0 rgba(0, 0, 0, 0.12); // 卡片悬停阴影
$box-shadow-modal: 0 20px 40px 0 rgba(0, 0, 0, 0.15); // 模态框阴影
$box-shadow-dropdown: 0 8px 24px 0 rgba(0, 0, 0, 0.12); // 下拉菜单阴影
$box-shadow-tooltip: 0 4px 12px 0 rgba(0, 0, 0, 0.1); // 工具提示阴影
$box-shadow-floating: 0 12px 32px 0 rgba(0, 0, 0, 0.1); // 浮动元素阴影
$box-shadow-sticky: 0 2px 12px 0 rgba(0, 0, 0, 0.08); // 粘性元素阴影

// 彩色阴影系统 - 企业级增强
$box-shadow-primary: 0 4px 15px rgba($primary-color, 0.25);
$box-shadow-primary-light: 0 2px 8px rgba($primary-color, 0.15);
$box-shadow-primary-strong: 0 8px 25px rgba($primary-color, 0.35);

$box-shadow-secondary: 0 4px 15px rgba($secondary-color, 0.25);
$box-shadow-secondary-light: 0 2px 8px rgba($secondary-color, 0.15);
$box-shadow-secondary-strong: 0 8px 25px rgba($secondary-color, 0.35);

$box-shadow-success: 0 4px 15px rgba($success-color, 0.25);
$box-shadow-success-light: 0 2px 8px rgba($success-color, 0.15);
$box-shadow-success-strong: 0 8px 25px rgba($success-color, 0.35);

$box-shadow-warning: 0 4px 15px rgba($warning-color, 0.25);
$box-shadow-warning-light: 0 2px 8px rgba($warning-color, 0.15);
$box-shadow-warning-strong: 0 8px 25px rgba($warning-color, 0.35);

$box-shadow-danger: 0 4px 15px rgba($danger-color, 0.25);
$box-shadow-danger-light: 0 2px 8px rgba($danger-color, 0.15);
$box-shadow-danger-strong: 0 8px 25px rgba($danger-color, 0.35);

$box-shadow-info: 0 4px 15px rgba($info-color, 0.25);
$box-shadow-info-light: 0 2px 8px rgba($info-color, 0.15);
$box-shadow-info-strong: 0 8px 25px rgba($info-color, 0.35);

// 特殊阴影效果 - 企业级场景
$box-shadow-focus: 0 0 0 3px rgba($primary-color, 0.1); // 焦点阴影
$box-shadow-focus-error: 0 0 0 3px rgba($danger-color, 0.1); // 错误焦点阴影
$box-shadow-focus-success: 0 0 0 3px rgba($success-color, 0.1); // 成功焦点阴影
$box-shadow-pressed: inset 0 2px 4px rgba(0, 0, 0, 0.1); // 按压阴影
$box-shadow-elevated: 0 16px 32px rgba(0, 0, 0, 0.08); // 提升阴影
$box-shadow-depth-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24); // 深度1
$box-shadow-depth-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23); // 深度2
$box-shadow-depth-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23); // 深度3
$box-shadow-depth-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22); // 深度4
$box-shadow-depth-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22); // 深度5

// ========================================
// 5. 圆角系统 (Border Radius) - 基于HTML根元素12px计算
// ========================================

$border-radius-none: 0;
$border-radius-xs: 0.167rem; // 2px (2÷12=0.167)
$border-radius-sm: 0.333rem; // 4px (4÷12=0.333)
$border-radius-base: 0.5rem; // 6px (6÷12=0.5)
$border-radius-md: 0.667rem; // 8px (8÷12=0.667)
$border-radius-lg: 1rem; // 12px (12÷12=1) - 基础圆角
$border-radius-xl: 1.333rem; // 16px (16÷12=1.333)
$border-radius-2xl: 2rem; // 24px (24÷12=2)
$border-radius-3xl: 2.667rem; // 32px (32÷12=2.667)
$border-radius-full: 9999px;

// 扩展圆角 - 企业级特殊用途
$border-radius-xxs: 0.083rem; // 1px (1÷12=0.083) - 超小圆角
$border-radius-4xl: 3.333rem; // 40px (40÷12=3.333) - 超大圆角

// ========================================
// 6. 间距系统 - 企业级大气布局 (基于HTML根元素12px计算)
// ========================================

$spacing-0: 0; // 0px
$spacing-xs: 0.333rem; // 4px (4÷12=0.333)
$spacing-sm: 0.667rem; // 8px (8÷12=0.667)
$spacing-base: 1rem; // 12px (12÷12=1) - 基础间距
$spacing-md: 1.333rem; // 16px (16÷12=1.333)
$spacing-lg: 1.667rem; // 20px (20÷12=1.667)
$spacing-xl: 2rem; // 24px (24÷12=2)
$spacing-2xl: 2.667rem; // 32px (32÷12=2.667)
$spacing-3xl: 4rem; // 48px (48÷12=4)
$spacing-4xl: 5.333rem; // 64px (64÷12=5.333)
$spacing-6xl: 8rem; // 96px (96÷12=8)
$spacing-8xl: 10.667rem; // 128px (128÷12=10.667)
$spacing-12xl: 16rem; // 192px (192÷12=16)

// 扩展间距 - 企业级特殊用途
$spacing-xxs: 0.167rem; // 2px (2÷12=0.167) - 超小间距
$spacing-5xl: 6.667rem; // 80px (80÷12=6.667) - 大间距
$spacing-10xl: 13.333rem; // 160px (160÷12=13.333) - 超大间距

// ========================================
// 7. 字体系统 - 企业级设计
// ========================================

// 字体族 (Font Families)
$font-family-primary: 'Inter', 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-secondary: Georgia, Cambria, 'Times New Roman', Times, serif;
$font-family-monospace: 'JetBrains Mono', Menlo, Monaco, 'Cascadia Code', 'Segoe UI Mono', 'Roboto Mono', 'Oxygen Mono', 'Ubuntu Monospace', 'Source Code Pro', 'Fira Mono', 'Droid Sans Mono', 'Courier New', monospace;

// 字号 - 12px 基础大气版本 (基于HTML根元素12px计算)
$font-size-xxs: 0.667rem; // 8px (8÷12=0.667)
$font-size-xs: 1rem; // 12px - 基础字体大小 (12÷12=1)
$font-size-sm: 1.167rem; // 14px (14÷12=1.167)
$font-size-base: 1.333rem; // 16px (16÷12=1.333)
$font-size-md: 1.5rem; // 18px (18÷12=1.5)
$font-size-lg: 1.667rem; // 20px (20÷12=1.667)
$font-size-xl: 2rem; // 24px (24÷12=2)
$font-size-2xl: 2.5rem; // 30px (30÷12=2.5)
$font-size-3xl: 3rem; // 36px (36÷12=3)
$font-size-4xl: 3.667rem; // 44px (44÷12=3.667)
$font-size-5xl: 4.667rem; // 56px (56÷12=4.667)
$font-size-6xl: 5.667rem; // 68px (68÷12=5.667)
$font-size-7xl: 6.667rem; // 80px (80÷12=6.667)

// 扩展字体大小
$font-size-xxl: 2.4rem; // 28.8px (28.8÷12=2.4)
$font-size-xxxl: 3.733rem; // 44.8px (44.8÷12=3.733)

// 字重 (Font Weight)
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;

// 行高 - 优化比例 (Line Height)
$line-height-tight: 1.2;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.7;
$line-height-loose: 1.9;

// ========================================
// 8. 布局系统 - 企业级设计
// ========================================

// 容器最大宽度 - 1760px 最大宽度，640px 最小宽度
$container-max-width: 1760px;
$container-min-width: 640px;
$desktop-min-width: 1200px;

// 响应式断点
$breakpoint-xs: 640px; // 大手机
$breakpoint-sm: 768px; // 平板竖屏
$breakpoint-md: 1024px; // 平板横屏
$breakpoint-lg: 1200px; // 小桌面
$breakpoint-xl: 1440px; // 标准桌面
$breakpoint-2xl: 1760px; // 企业级大桌面
$breakpoint-3xl: 1920px; // 超大桌面

// 导航栏高度 - 基于12px倍数优化
$nav-height-top: 30px; // 顶部导航栏高度 (12px × 3)
$nav-height-main: 60px; // 主导航栏高度 (12px × 5)
$nav-height-bottom: 30px; // 底部版权栏高度 (12px × 3)

// 扩展导航栏高度 - 响应式支持
$nav-height-mobile: 48px; // 移动端导航栏高度 (12px × 4)
$nav-height-compact: 30px; // 紧凑型导航栏高度 (12px × 2)

// ========================================
// 9. Z-index 层级系统
// ========================================

$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-toast: 1080;

// ========================================
// 10. Element Plus 组件尺寸变量 - 必须在 CSS 自定义属性之前定义
// ========================================

// Element Plus 组件尺寸 (基于12px根元素)
$--component-size-large: 2.5rem; // 30px (2.5rem) - 基于12px
$--component-size: 2rem; // 24px (2rem) - 基于12px
$--component-size-small: 1.5rem; // 18px (1.5rem) - 基于12px


// ========================================
// 12. Element Plus 企业级主题变量覆盖 - 完美配合
// ========================================

// Element Plus 主色调覆盖
$--color-primary: $primary-color; // #5247ef
$--color-success: $success-color; // #04c717
$--color-warning: $warning-color; // #FF5000
$--color-danger: $danger-color; // #FF0036
$--color-error: $danger-color; // 错误色同危险色
$--color-info: $info-color; // #97a6ba

// Element Plus 字体系统覆盖 - 12px 基础 (基于HTML根元素12px计算)
$--font-size-extra-small: $font-size-xs; // 12px (1rem)
$--font-size-small: $font-size-sm; // 14px (1.167rem)
$--font-size-base: $font-size-sm; // 14px 作为Element Plus基础大小
$--font-size-medium: $font-size-base; // 16px (1.333rem)
$--font-size-large: $font-size-md; // 18px (1.5rem)
$--font-size-extra-large: $font-size-lg; // 20px (1.667rem)

// Element Plus 字体家族
$--font-family: $font-family-primary;

// Element Plus 边框圆角系统 (基于12px根元素)
$--border-radius-base: $border-radius-base; // 6px (0.5rem)
$--border-radius-small: $border-radius-sm; // 4px (0.333rem)
$--border-radius-round: $border-radius-xl; // 16px (1.333rem)
$--border-radius-circle: 50%;

// Element Plus 间距系统 (基于12px根元素)
$--spacing-base: $spacing-md; // 16px (1.333rem)
$--spacing-small: $spacing-base; // 12px (1rem)
$--spacing-large: $spacing-xl; // 24px (2rem)


// Element Plus 边框颜色
$--border-color-base: $border-color;
$--border-color-light: $border-color-light;
$--border-color-lighter: $border-color-lighter;
$--border-color-extra-light: $border-color-extra-light;
$--border-color-dark: $border-color-dark;
$--border-color-darker: $border-color-darker;

// Element Plus 文字颜色
$--color-text-primary: $text-primary;
$--color-text-regular: $text-regular;
$--color-text-secondary: $text-secondary;
$--color-text-placeholder: $text-placeholder;

// Element Plus 背景颜色
$--color-white: $white;
$--color-black: $black;
$--background-color-base: $bg-white;
$--background-color-page: $body-bg;

// Element Plus 填充颜色
$--fill-base: $gray-100;
$--fill-light: $gray-50;
$--fill-lighter: $gray-50;
$--fill-extra-light: $bg-lighter;
$--fill-blank: $white;
$--fill-dark: $gray-200;
$--fill-darker: $gray-300;

// Element Plus 企业级无动画设计
$--transition-duration: 0ms; // 无动画
$--transition-duration-fast: 0ms;
$--transition-function-ease-in-out-bezier: linear;
$--transition-function-fast-bezier: linear;

// Element Plus 阴影系统
$--box-shadow-base: $box-shadow-base;
$--box-shadow-light: $box-shadow-sm;
$--box-shadow-lighter: $box-shadow-xs;
$--box-shadow-dark: $box-shadow-lg;

// ========================================
// 13. 布局专用 SCSS 变量 - 企业级增强
// ========================================

// 顶部导航栏变量系统 - 企业级增强
$top-nav-bg: $black;
$top-nav-bg-transparent: $black-alpha-90;
$top-nav-text: $gray-300;
$top-nav-text-active: $white;
$top-nav-text-muted: $gray-400;
$top-nav-hover-bg: $white-alpha-10;
$top-nav-hover-text: $white;
$top-nav-border: $gray-800;
$top-nav-shadow: $box-shadow-sm;

// 顶部导航栏状态变量 - 新增
$top-nav-active-bg: $white-alpha-15;
$top-nav-focus-bg: $white-alpha-08;
$top-nav-disabled-text: $gray-600;
$top-nav-separator: $gray-700;

// 主导航栏变量系统 - 企业级增强
$main-nav-bg: $white;
$main-nav-bg-transparent: $white-alpha-95;
$main-nav-border: $border-color;
$main-nav-text: $text-muted; // 更灰的文字颜色，减少眼疲劳
$main-nav-text-active: $text-primary;
$main-nav-text-hover: $text-secondary;
$main-nav-hover-bg: $bg-hover;
$main-nav-active-bg: $bg-selected;
$main-nav-shadow: $box-shadow-card;
$main-nav-sticky-shadow: $box-shadow-sticky;

// 主导航栏状态变量 - 新增
$main-nav-focus-bg: $primary-alpha-05;
$main-nav-disabled-text: $text-disabled;
$main-nav-separator: $border-color-light;
$main-nav-dropdown-bg: $white;
$main-nav-dropdown-shadow: $box-shadow-dropdown;

// 侧边栏变量系统 - 新增
$sidebar-bg: $white;
$sidebar-bg-dark: $gray-900;
$sidebar-border: $border-color;
$sidebar-text: $text-secondary;
$sidebar-text-active: $text-primary;
$sidebar-hover-bg: $bg-hover;
$sidebar-active-bg: $bg-selected;
$sidebar-shadow: $box-shadow-lg;
$sidebar-width: 280px;
$sidebar-width-collapsed: 64px;

// 内容区域变量 - 新增
$content-bg: $body-bg;
$content-padding: $spacing-2xl;
$content-padding-mobile: $spacing-lg;
$content-max-width: $container-max-width;
$content-border-radius: $border-radius-lg;

// 徽章变量系统 - 企业级增强
$badge-bg: $danger-color;
$badge-text: $white;
$badge-border-radius: $border-radius-full;
$badge-font-size: $font-size-xxs;
$badge-padding: $spacing-xs $spacing-sm;
$badge-shadow: $box-shadow-sm;

// 页脚变量系统 - 企业级增强
$footer-bg: $bg-light;
$footer-bg-dark: $gray-900;
$footer-text: $text-secondary;
$footer-text-muted: $text-muted;
$footer-text-link: $text-link;
$footer-border: $border-color;
$footer-shadow: $box-shadow-sm;
$footer-padding: $spacing-2xl;

// 面包屑导航变量 - 新增
$breadcrumb-bg: transparent;
$breadcrumb-text: $text-muted;
$breadcrumb-text-active: $text-primary;
$breadcrumb-separator: $text-lighter;
$breadcrumb-hover-text: $text-secondary;

// 标签页变量 - 新增
$tab-bg: transparent;
$tab-bg-active: $white;
$tab-text: $text-muted;
$tab-text-active: $text-primary;
$tab-text-hover: $text-secondary;
$tab-border: $border-color;
$tab-border-active: $primary-color;
$tab-shadow-active: $box-shadow-sm;

// 表格变量系统 - 新增
$table-bg: $white;
$table-bg-striped: $gray-50;
$table-bg-hover: $bg-hover;
$table-bg-selected: $bg-selected;
$table-border: $border-color;
$table-header-bg: $bg-light;
$table-header-text: $text-secondary;
$table-cell-padding: $spacing-sm $spacing-base;

// 模态框变量系统 - 新增
$modal-bg: $white;
$modal-overlay-bg: $bg-overlay;
$modal-border: $border-color;
$modal-shadow: $box-shadow-modal;
$modal-header-bg: $bg-light;
$modal-header-border: $border-color;
$modal-footer-bg: $bg-light;
$modal-footer-border: $border-color;

// ========================================
// 常用 mixins 定义 - 供 Vue 文件使用
// ========================================

// 企业级无动画 mixin - 符合用户偏好
@mixin no-animation {
    transition: none !important;
    animation: none !important;
    transform: none !important;
}

// 企业级焦点移除 mixin - 符合用户偏好
@mixin no-focus-outline {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

// 企业级 flex 布局 mixin
@mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

@mixin flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@mixin flex-column {
    display: flex;
    flex-direction: column;
}

// 企业级链接基础样式 mixin
@mixin enterprise-link {
    text-decoration: none;
    cursor: pointer;
    color: $text-muted; // 更灰的文字颜色，减少眼疲劳
    @include no-animation;
    @include no-focus-outline;

    &:hover {
        color: $text-primary;
        // 无悬停效果，符合用户偏好
    }
}

// 企业级容器 mixin
@mixin enterprise-container($max-width: $container-max-width) {
    max-width: $max-width;
    min-width: $container-min-width;
    margin: 0 auto;
    padding: 0 $spacing-2xl;
}

// 企业级容器响应式 mixin
@mixin enterprise-container-responsive {
    @media (max-width: $breakpoint-sm) {
        padding: 0 $spacing-md;
    }
}

// 企业级图标样式 mixin
@mixin enterprise-icon($size: 0.85em, $margin: $spacing-xs) {
    font-size: $size;
    margin-right: $margin;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1em;
    vertical-align: middle;
    line-height: 1;
    flex-shrink: 0;
    @include no-animation;
}

// 响应式设计 mixin
@mixin responsive($breakpoint) {
    @if $breakpoint == xs {
        @media (max-width: #{$breakpoint-xs - 1px}) {
            @content;
        }
    }
    @if $breakpoint == sm {
        @media (min-width: $breakpoint-sm) {
            @content;
        }
    }
    @if $breakpoint == md {
        @media (min-width: $breakpoint-md) {
            @content;
        }
    }
    @if $breakpoint == lg {
        @media (min-width: $breakpoint-lg) {
            @content;
        }
    }
    @if $breakpoint == xl {
        @media (min-width: $breakpoint-xl) {
            @content;
        }
    }
    @if $breakpoint == 2xl {
        @media (min-width: $breakpoint-2xl) {
            @content;
        }
    }
}

// 文字截断 mixin
@mixin text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@mixin text-truncate-lines($lines: 2) {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
}

// 企业级表单元素 mixin
@mixin enterprise-form-input {
    width: 100%;
    padding: $spacing-sm $spacing-base;
    border: 1px solid $border-color;
    border-radius: $border-radius-md;
    font-size: $font-size-xs; // 12px 基础
    font-family: $font-family-primary;
    background-color: $white;
    color: $text-primary;
    @include no-animation;

    &:focus {
        border-color: $primary-color;
        @include no-focus-outline;
    }

    &::placeholder {
        color: $text-placeholder;
    }
}

// 企业级按钮基础样式 mixin
@mixin enterprise-button {
    @include flex-center;
    @include enterprise-link;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-md;
    font-weight: $font-weight-medium;
    font-size: $font-size-xs; // 12px 基础
    background-color: transparent;
    border: 1px solid transparent;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

// 企业级卡片样式 mixin
@mixin enterprise-card {
    background-color: $card-bg;
    border: 1px solid $border-color;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-sm;
    @include no-animation;
}

// 企业级文字样式 mixin
@mixin enterprise-text($size: 'base', $weight: 'normal', $color: $text-primary) {
    @if $size == 'xs' {
        font-size: $font-size-xs;
    } @else if $size == 'sm' {
        font-size: $font-size-sm;
    } @else if $size == 'base' {
        font-size: $font-size-base;
    } @else if $size == 'lg' {
        font-size: $font-size-lg;
    } @else if $size == 'xl' {
        font-size: $font-size-xl;
    }

    @if $weight == 'light' {
        font-weight: $font-weight-light;
    } @else if $weight == 'normal' {
        font-weight: $font-weight-normal;
    } @else if $weight == 'medium' {
        font-weight: $font-weight-medium;
    } @else if $weight == 'semibold' {
        font-weight: $font-weight-semibold;
    } @else if $weight == 'bold' {
        font-weight: $font-weight-bold;
    }

    color: $color;
    font-family: $font-family-primary;
}

// 企业级进度条 mixin
@mixin enterprise-progress($height: 8px, $color: $primary-color) {
    width: 100%;
    height: $height;
    background-color: $border-color;
    border-radius: $border-radius-full;
    overflow: hidden;

    .progress-bar {
        height: 100%;
        background-color: $color;
        border-radius: $border-radius-full;
        @include no-animation;
    }
}
