<?php

namespace App\Services\Process\Rice;

use App\Models\PegCenter\Peg;
use App\Models\RiceCenter\Rice;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class RiceMatchKeywordService extends BaseService
{
    /**
     * 处理单条 Rice 记录
     * @param Rice $rice
     */
    public static function processRiceMatchPeg(Peg $pegs, Rice $rice): void
    {
        try {
            // 1️⃣ 获取目标Peg集合
            //            $pegs = Peg::where('platform_id', '=', config('app.platform_id'))->get();
            if ($pegs && count($pegs) > 0) {
                //
                // 2️⃣ 遍历每个Peg，查找是否需要关联
                foreach ($pegs as $peg) {
                    // 判断rice_title或pin_subject是否包含peg_name
                    if ((isset($rice->rice_title) && Str::contains($rice->rice_title, $peg->peg_name)) || (isset($rice->pin_subject) && Str::contains($rice->pin_subject, $peg->peg_name))) {
                        // 3️⃣ 同步Rice与Peg的关系
                        $rice->pegs()->syncWithoutDetaching($peg->id);
                    }
                }
            }

        } catch (Exception $e) {
            // 捕获顶层异常，记录错误日志
            Log::error('RiceProcessPegService 标记失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
