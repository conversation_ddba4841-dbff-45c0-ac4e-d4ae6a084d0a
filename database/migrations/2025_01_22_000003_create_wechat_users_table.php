<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 微信用户信息表迁移
 * 
 * 创建微信用户信息表，用于存储微信用户的详细信息和操作记录
 * 支持公众号、小程序等多种微信应用场景
 * 
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
return new class extends Migration
{
    /**
     * 执行迁移
     * 
     * 创建微信用户信息表
     */
    public function up(): void
    {
        Schema::create('wechat_users', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            
            // 关联信息
            $table->unsignedBigInteger('user_id')->nullable()->comment('关联用户ID');
            
            // 微信基础信息
            $table->string('openid', 100)->comment('微信OpenID');
            $table->string('unionid', 100)->nullable()->comment('微信UnionID');
            $table->enum('platform', ['official_account', 'mini_program', 'open_platform', 'work'])->comment('微信平台类型');
            $table->string('app_id', 50)->comment('应用ID');
            
            // 用户信息
            $table->string('nickname', 100)->nullable()->comment('昵称');
            $table->string('head_img_url')->nullable()->comment('头像URL');
            $table->tinyInteger('sex')->nullable()->comment('性别：0-未知，1-男，2-女');
            $table->string('country', 50)->nullable()->comment('国家');
            $table->string('province', 50)->nullable()->comment('省份');
            $table->string('city', 50)->nullable()->comment('城市');
            $table->string('language', 20)->nullable()->comment('语言');
            
            // 关注信息（公众号）
            $table->boolean('is_subscribe')->default(false)->comment('是否关注公众号');
            $table->timestamp('subscribe_time')->nullable()->comment('关注时间');
            $table->timestamp('unsubscribe_time')->nullable()->comment('取消关注时间');
            $table->string('subscribe_scene', 50)->nullable()->comment('关注场景');
            $table->string('qr_scene', 100)->nullable()->comment('二维码场景值');
            $table->string('qr_scene_str', 100)->nullable()->comment('二维码场景描述');
            
            // 分组和标签
            $table->integer('group_id')->nullable()->comment('用户分组ID');
            $table->json('tag_id_list')->nullable()->comment('用户标签ID列表');
            $table->text('remark')->nullable()->comment('备注');
            
            // 权限信息
            $table->json('privilege')->nullable()->comment('用户特权信息');
            
            // 扩展信息
            $table->json('extra_data')->nullable()->comment('扩展数据');
            
            // 统计信息
            $table->integer('message_count')->default(0)->comment('消息数量');
            $table->timestamp('last_message_at')->nullable()->comment('最后消息时间');
            $table->timestamp('last_active_at')->nullable()->comment('最后活跃时间');
            
            // 状态信息
            $table->enum('status', ['active', 'blocked', 'deleted'])->default('active')->comment('状态');
            $table->text('status_reason')->nullable()->comment('状态变更原因');
            
            // 时间戳
            $table->timestamps();
            
            // 索引
            $table->unique(['openid', 'platform', 'app_id'], 'uk_wechat_users_openid_platform_app');
            $table->index(['user_id'], 'idx_wechat_users_user_id');
            $table->index(['unionid'], 'idx_wechat_users_unionid');
            $table->index(['platform'], 'idx_wechat_users_platform');
            $table->index(['app_id'], 'idx_wechat_users_app_id');
            $table->index(['is_subscribe'], 'idx_wechat_users_is_subscribe');
            $table->index(['subscribe_time'], 'idx_wechat_users_subscribe_time');
            $table->index(['last_active_at'], 'idx_wechat_users_last_active_at');
            $table->index(['status'], 'idx_wechat_users_status');
            $table->index(['created_at'], 'idx_wechat_users_created_at');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * 回滚迁移
     * 
     * 删除微信用户信息表
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_users');
    }
};
