<?php

namespace App\Enums\Vest;

use App\Enums\BaseEnum;

/**
 * Class OwnerVestEnum
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self user()
 * @method static self store()
 * @method static self provider()
 * @method static self supplier()
 * @method static self founder()
 * @method static self merchant()
 * @method static self sale()
 * @method static self transport()
 * @method static self soft()
 * @method static self platform()
 * @method static self site()
 * @method static self retail()
 * @method static self insurer()
 * @method static self designer()
 */
final class OwnerVestEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'user' => 1,
            'store' => 2,
            'provider' => 3,
            'supplier' => 4,
            'founder' => 5,
            'merchant' => 6,
            'sale' => 7,
            'transport' => 8,
            'soft' => 9,
            'platform' => 10,
            'site' => 11,
            'retail' => 12,
            'insurer' => 13,
            'designer' => 14,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'user' => '用户',
            'store' => '商家',
            'provider' => '服务商',
            'supplier' => '供应商',
            'founder' => '品牌商',
            'merchant' => '批发商',
            'sale' => '销售商',
            'transport' => '运输商',
            'soft' => '软件服务商',
            'platform' => '平台',
            'site' => '站点',
            'retail' => '渠道',
            'insurer' => '保险公司',
            'designer' => '设计师',
        ];
    }

    // Define color mapping

    public function color(): string
    {
        $value = $this->value;
        $colors = static::colors();
        return $colors[$value] ?? 'default_color';
    }

    protected static function colors(): array
    {
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
            6 => 'blue',
            7 => 'indigo',
            8 => 'cyan',
            9 => 'teal',
            10 => 'pink',
            11 => 'brown',
            12 => 'grey',
            13 => 'lime',
            14 => 'amber',
        ];
    }
}
