<?php

namespace App\Models\SupplierCenter;

use App\Models\CommonBrace\Region;
use App\Models\CommonBrace\Week;
use App\Models\IndustryBrace\Category;
use App\Models\SellerCenter\SellerInfo;
use App\Models\SiteBrace\Site;
use App\Models\SiteBrace\SiteMarket;
use App\Models\StoreCenter\Store;
use App\Models\UserCenter\User;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Image\Exceptions\InvalidManipulation;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Supplier extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'supplier_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'cat' => 'array',
        'copy_cat' => 'array',
        'photo_url' => 'array',
        'week' => 'array',
        'market' => 'array',
        'deleted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * @param Media|null $media
     * @return void
     * @throws InvalidManipulation
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')->width(300)->height(300);
    }

    /*
     *
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('big-files')->useDisk('ftp');
    }

    /**
     * @return HasOne
     */
    public function info()
    {
        //
        return $this->hasOne(SellerInfo::class, 'supplier_id', 'id');
    }

    /**
     * @return BelongsToMany
     */
    public function regions()
    {
        //
        return $this->belongsToMany(Region::class)->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function markets()
    {
        //
        return $this->belongsToMany(SiteMarket::class)->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function categories()
    {
        //
        return $this->belongsToMany(Category::class)->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function stores()
    {
        //
        return $this->belongsToMany(Store::class)->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function week()
    {
        return $this->belongsToMany(Week::class)->withTimestamps();
    }

    /**
     * 获取拥有此电话的用户
     */
    public function site()
    {
        //
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * 获取拥有此电话的用户
     */
    public function user()
    {
        //
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
