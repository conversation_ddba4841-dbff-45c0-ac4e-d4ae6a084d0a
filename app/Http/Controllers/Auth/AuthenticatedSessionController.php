<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

/**
 * 用户认证会话控制器
 *
 * 负责处理用户的登录、登出等认证相关操作
 * 包括会话管理、安全检查、日志记录等功能
 *
 * @package App\Http\Controllers\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class AuthenticatedSessionController extends Controller
{
    /**
     * 显示登录页面
     *
     * 渲染用户登录表单页面，如果用户已经登录则重定向到仪表板
     *
     * @return View|RedirectResponse 返回登录视图或重定向响应
     */
    public function create(): View|RedirectResponse
    {
        // 如果用户已经登录，直接重定向到仪表板
        if (Auth::check()) {
            //
            Log::info('已登录用户尝试访问登录页面', [
                'user_id' => Auth::id(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);

            return redirect()->intended(route('dashboard', absolute: false));
        }

        // 记录登录页面访问日志
        Log::info('用户访问登录页面', [
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'referer' => request()->header('referer')
        ]);

        return view('auth.login');
    }

    /**
     * 处理用户登录请求
     *
     * 验证用户凭据，创建认证会话，记录登录日志
     * 支持"记住我"功能和登录后重定向
     *
     * @param LoginRequest $request 登录请求对象，包含验证规则
     * @return RedirectResponse|JsonResponse 返回重定向响应或JSON响应
     */
    public function store(LoginRequest $request): RedirectResponse|JsonResponse
    {
        try {
            // 执行用户认证（包含频率限制检查）
            $request->authenticate();

            // 获取当前认证用户
            $user = Auth::user();

            // 重新生成会话ID以防止会话固定攻击
            $request->session()->regenerate();

            // 记录用户登录成功日志
            Log::info('用户登录成功', [
                'user_id' => $user->id,
                'email' => $user->email ?? $user->mobile ?? 'unknown',
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'remember' => $request->boolean('remember'),
                'login_time' => now()->toDateTimeString()
            ]);

            // 触发登录事件（用于其他监听器处理）
            event(new Login('web', $user, $request->boolean('remember')));

            // 设置登录成功的会话标识
            Session::put('login_success', true);
            Session::put('last_login_time', now());

            // 如果是AJAX请求，返回JSON响应
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '登录成功',
                    'redirect_url' => route('dashboard', absolute: false),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name ?? $user->user_name ?? '',
                        'email' => $user->email ?? '',
                    ]
                ]);
            }

            // 重定向到用户原本想访问的页面，或默认到仪表板
            return redirect()->intended(route('dashboard', absolute: false))->with('success', '欢迎回来！登录成功。');

        } catch (\Exception $e) {
            // 记录登录失败日志
            Log::warning('用户登录失败', [
                'email' => $request->input('email'),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'error' => $e->getMessage(),
                'attempt_time' => now()->toDateTimeString()
            ]);

            // 如果是AJAX请求，返回JSON错误响应
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '登录失败，请检查您的凭据',
                    'errors' => ['email' => ['登录凭据不正确']]
                ], 422);
            }

            // 重新抛出异常让Laravel处理
            throw $e;
        }
    }

    /**
     * 销毁用户认证会话（登出）
     *
     * 清除用户会话，使令牌失效，记录登出日志
     * 确保安全地清理所有认证相关数据
     *
     * @param Request $request HTTP请求对象
     * @return RedirectResponse|JsonResponse 返回重定向响应或JSON响应
     */
    public function destroy(Request $request): RedirectResponse|JsonResponse
    {
        try {
            // 获取当前用户信息（在登出前）
            $user = Auth::user();
            $userId = $user ? $user->id : null;
            $userEmail = $user ? ($user->email ?? $user->mobile ?? 'unknown') : 'unknown';

            // 触发登出事件
            if ($user) {
                event(new Logout('web', $user));
            }

            // 从指定的守卫中登出用户
            Auth::guard('web')->logout();

            // 使当前会话失效
            $request->session()->invalidate();

            // 重新生成CSRF令牌以防止CSRF攻击
            $request->session()->regenerateToken();

            // 清除所有会话数据
            Session::flush();

            // 记录用户登出日志
            Log::info('用户登出成功', [
                'user_id' => $userId,
                'email' => $userEmail,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'logout_time' => now()->toDateTimeString()
            ]);

            // 如果是AJAX请求，返回JSON响应
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '已安全登出',
                    'redirect_url' => '/'
                ]);
            }

            // 重定向到首页并显示成功消息
            return redirect('/')
                ->with('success', '您已安全登出，感谢使用！');

        } catch (\Exception $e) {
            // 记录登出失败日志
            Log::error('用户登出失败', [
                'user_id' => Auth::id(),
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
                'logout_attempt_time' => now()->toDateTimeString()
            ]);

            // 如果是AJAX请求，返回JSON错误响应
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '登出过程中发生错误，请重试',
                ], 500);
            }

            // 即使出错也要尝试清理会话
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect('/')->with('error', '登出过程中发生错误，但您已被安全登出。');
        }
    }
}
