<?php

namespace App\Api\Ali1688Cps;

class Ali1688CpsShopApi extends Ali1688CpsBaseApi
{
    /**
     * 获取联盟商家列表
     * 对应 API: com.alibaba.p4p:alibaba.cps.listShopPageQuery-1
     * 官方文档: https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.p4p:alibaba.cps.listShopPageQuery-1
     * @param array $Ali1688AccessToken 授权信息，需包含 ['client_id', 'client_secret', 'access_token']
     * @param array $params 参数列表 (categoryId 必填)
     *                                  [
     *                                  'sellerId'         => (int)   卖家ID(可选),
     *                                  'companyName'      => (string)公司名称(可选),
     *                                  'categoryId'       => (int)   类目ID(必填),
     *                                  'defineTags'       => (string)属性标签(可选)，如 slsj_flag=true,sdrd_flag=true,
     *                                  'filterRatioMin'   => (float) 佣金比例下限(可选),
     *                                  'filterRatioMax'   => (float) 佣金比例上限(可选),
     *                                  'sortField'        => (string)排序字段,示例 ratio^asc, productCnt^desc 等(可选),
     *                                  'pageNo'           => (int)   页码(必填), 默认1,
     *                                  'pageSize'         => (int)   分页大小(必填), 默认10,
     *                                  ]
     * @return mixed
     */
    public static function getAli1688CpsShopList($Ali1688AccessToken, array $params = [])
    {
        // 接口路径 (POST)
        // 注意：官方文档中，该接口请求方式为 POST
        // 最终请求地址形如:
        //   https://gw.open.1688.com/openapi/param2/1/com.alibaba.p4p/alibaba.cps.listShopPageQuery/${APPKEY}
        $apiInfo = 'param2/1/com.alibaba.p4p/alibaba.cps.listShopPageQuery/';

        // 必填校验: categoryId
        if (empty($params['categoryId'])) {
            //
            $params['categoryId'] = 0;
            //throw new InvalidArgumentException('categoryId 为必填参数');
        }

        // pageNo、pageSize 默认值处理
        if (!isset($params['pageNo'])) {
            $params['pageNo'] = 1;
        }
        if (!isset($params['pageSize'])) {
            $params['pageSize'] = 10;
        }

        // 准备传给 API 的 body 参数
        $body_params = [];

        // 可选或必选参数字段映射
        $fields = [
            'sellerId',
            'companyName',
            'categoryId',
            'defineTags',
            'filterRatioMin',
            'filterRatioMax',
            'sortField',
            'pageNo',
            'pageSize',
        ];

        foreach ($fields as $field) {
            if (isset($params[$field])) {
                $body_params[$field] = $params[$field];
            }
        }

        // 发起调用
        return Ali1688CpsBaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'POST'
        );
    }
}
