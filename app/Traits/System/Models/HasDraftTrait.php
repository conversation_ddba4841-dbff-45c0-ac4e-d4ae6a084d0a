<?php

namespace App\Traits\System\Models;

use Closure;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\{BelongsToMany, HasMany, HasOne, MorphTo, MorphToMany};
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Oddvalue\LaravelDrafts\Concerns\Publishes;
use Oddvalue\LaravelDrafts\Facades\LaravelDrafts;

/**
 * HasDraftTrait 提供草稿和版本控制功能
 */
trait HasDraftTrait
{
    //    use SoftDeletes;
    //    use HasEvents;
    //    use HasGlobalScopes;
    use Publishes;

    /**
     * 是否应创建修订版本
     * @var bool
     */
    protected bool $shouldCreateRevision = true;

    /**
     * 是否应保存为草稿
     * @var bool
     */
    protected bool $shouldSaveAsDraft = false;

    /*
    |--------------------------------------------------------------------------
    | 初始化方法
    |--------------------------------------------------------------------------
    */

    /**
     * 模型启动时的钩子
     */
    public static function bootHasDraftTrait(): void
    {
        // 移除全局作用域，避免影响查询
        // 如果需要，可以在需要的地方手动调用作用域
    }

    /**
     * 注册 savingAsDraft 事件
     */
    public static function savingAsDraft(string|Closure $callback): void
    {
        static::registerModelEvent('savingAsDraft', $callback);
    }

    /*
    |--------------------------------------------------------------------------
    | 主要功能方法
    |--------------------------------------------------------------------------
    */

    /**
     * 注册 savedAsDraft 事件
     */
    public static function savedAsDraft(string|Closure $callback): void
    {
        static::registerModelEvent('drafted', $callback);
    }

    /**
     * 创建草稿
     */
    public static function createDraft(...$attributes): self
    {
        return tap(static::make(...$attributes), function ($instance) {
            $instance->{$instance->getIsPublishedColumn()} = false;
            $instance->save();
        });
    }

    /**
     * 保存模型
     */
    public function save(array $options = []): bool
    {
        if ($this->exists && (data_get($options, 'draft') || $this->shouldDraft())) {
            return $this->saveAsDraft($options);
        }

        return parent::save($options);
    }

    /**
     * 检查是否应保存为草稿
     */
    public function shouldDraft(): bool
    {
        return $this->shouldSaveAsDraft;
    }

    /**
     * 以草稿形式保存
     */
    public function saveAsDraft(array $options = []): bool
    {
        if ($this->fireModelEvent('savingAsDraft') === false ||
            $this->fireModelEvent('saving') === false
        ) {
            return false;
        }

        $draft = $this->replicate();
        $draft->{$this->getPublishedAtColumn()} = null;
        $draft->{$this->getIsPublishedColumn()} = false;
        $draft->shouldSaveAsDraft = false;
        $draft->setCurrent();

        $saved = $draft->save($options);

        if ($saved) {
            $this->fireModelEvent('drafted');
            $this->pruneRevisions();
        }

        return $saved;
    }

    /**
     * 设置当前版本
     */
    public function setCurrent(): void
    {
        $this->{$this->getIsCurrentColumn()} = true;

        static::saved(function (Model $model) {
            if ($model->isNot($this)) {
                return;
            }

            $this->revisions()
                ->where($this->getIsCurrentColumn(), true)
                ->where($this->getKeyName(), '!=', $this->getKey())
                ->update([$this->getIsCurrentColumn() => false]);
        });
    }

    /**
     * 获取 is_current 字段名
     */
    public function getIsCurrentColumn(): string
    {
        return defined(static::class . '::IS_CURRENT')
            ? static::IS_CURRENT
            : config('drafts.column_names.is_current', 'is_current');
    }

    /**
     * 获取修订版本的关联关系
     */
    public function revisions(): HasMany
    {
        return $this->hasMany(
            static::class,
            $this->getUuidColumn(),
            $this->getUuidColumn()
        );
    }

    /**
     * 获取 uuid 字段名
     */
    public function getUuidColumn(): string
    {
        return defined(static::class . '::UUID')
            ? static::UUID
            : config('drafts.column_names.uuid', 'uuid');
    }

    /**
     * 清理旧的修订版本
     */
    public function pruneRevisions(): void
    {
        self::withoutEvents(function () {
            $revisionsToKeep = $this->revisions()
                ->orderByDesc($this->getUpdatedAtColumn())
                ->onlyDrafts()
                ->withoutCurrent()
                ->take(config('drafts.revisions.keep'))
                ->pluck($this->getKeyName())
                ->merge($this->revisions()->current()->pluck($this->getKeyName()))
                ->merge($this->revisions()->published()->pluck($this->getKeyName()));

            $this->revisions()
                ->whereNotIn($this->getKeyName(), $revisionsToKeep)
                ->delete();
        });
    }

    /**
     * 初始化模型时合并字段类型转换
     */
    public function initializeHasDraftTrait(): void
    {
        $this->mergeCasts([
            $this->getIsCurrentColumn() => 'boolean',
            $this->getIsPublishedColumn() => 'boolean',
            $this->getPublishedAtColumn() => 'datetime',
        ]);
    }

    /**
     * 设置不创建修订版本
     */
    public function withoutRevision(): static
    {
        $this->shouldCreateRevision = false;

        return $this;
    }

    /**
     * 生成 UUID
     */
    public function generateUuid(): void
    {
        if ($this->{$this->getUuidColumn()}) {
            return;
        }

        $this->{$this->getUuidColumn()} = Str::uuid();
    }

    /**
     * 设置为已发布状态
     */
    public function setLive(): void
    {
        $published = $this->revisions()->published()->first();

        if (!$published || $this->is($published)) {
            $this->{$this->getPublishedAtColumn()} ??= now();
            $this->{$this->getIsPublishedColumn()} = true;
            $this->setCurrent();

            return;
        }

        $oldAttributes = $published->getDraftableAttributes() ?? [];
        $newAttributes = $this->getDraftableAttributes();

        Arr::forget($oldAttributes, $this->getKeyName());
        Arr::forget($newAttributes, $this->getKeyName());

        $published->forceFill($newAttributes);
        $this->forceFill($oldAttributes);

        static::saved(function (Model $model) use ($published) {
            if ($model->isNot($this)) {
                return;
            }

            $published->{$this->getIsPublishedColumn()} = true;
            $published->{$this->getPublishedAtColumn()} ??= now();
            $published->setCurrent();
            $published->saveQuietly();

            $this->replicateAndAssociateDraftableRelations($published);
        });

        $this->{$this->getIsPublishedColumn()} = false;
        $this->{$this->getPublishedAtColumn()} = null;
        $this->{$this->getIsCurrentColumn()} = false;
        $this->timestamps = false;
        $this->shouldCreateRevision = false;
    }

    /*
    |--------------------------------------------------------------------------
    | 事件注册方法
    |--------------------------------------------------------------------------
    */

    /**
     * 获取可用于草稿的属性
     */
    public function getDraftableAttributes(): array
    {
        return $this->getAttributes();
    }

    /**
     * 复制并关联可草稿的关联关系
     */
    public function replicateAndAssociateDraftableRelations(Model $published): void
    {
        foreach ($this->getDraftableRelations() as $relationName) {
            $relation = $published->{$relationName}();

            switch (true) {
                case $relation instanceof HasOne:
                    if ($related = $this->{$relationName}) {
                        $replicated = $related->replicate();

                        $method = method_exists($replicated, 'getDraftableAttributes')
                            ? 'getDraftableAttributes'
                            : 'getAttributes';

                        $published->{$relationName}()->create($replicated->$method());
                    }
                    break;

                case $relation instanceof HasMany:
                    $this->{$relationName}()->get()->each(function ($model) use ($published, $relationName) {
                        $replicated = $model->replicate();

                        $method = method_exists($replicated, 'getDraftableAttributes')
                            ? 'getDraftableAttributes'
                            : 'getAttributes';

                        $published->{$relationName}()->create($replicated->$method());
                    });
                    break;

                case $relation instanceof MorphToMany:
                case $relation instanceof BelongsToMany:
                    $published->{$relationName}()->sync($this->{$relationName}()->pluck($this->{$relationName}()->getRelated()->getKeyName())->toArray());
                    break;
            }
        }
    }

    /**
     * 获取可草稿的关联关系
     */
    public function getDraftableRelations(): array
    {
        return property_exists($this, 'draftableRelations') ? $this->draftableRelations : [];
    }

    /**
     * 设置模型为草稿状态
     */
    public function asDraft(): static
    {
        $this->shouldSaveAsDraft = true;

        return $this;
    }

    /**
     * 设置已发布的属性（此处不做操作）
     */
    public function setPublishedAttributes(): void
    {
        // 留空，所有逻辑在 setLive 方法中处理
    }

    /**
     * 以草稿形式更新模型
     */
    public function updateAsDraft(array $attributes = [], array $options = []): bool
    {
        if (!$this->exists) {
            return false;
        }

        return $this->fill($attributes)->saveAsDraft($options);
    }

    /*
    |--------------------------------------------------------------------------
    | 辅助方法
    |--------------------------------------------------------------------------
    */

    /**
     * 获取完整的发布者关联字段名
     */
    public function getQualifiedPublisherColumns(): array
    {
        return array_map([$this, 'qualifyColumn'], $this->getPublisherColumns());
    }

    /**
     * 获取发布者关联字段名
     */
    public function getPublisherColumns(): array
    {
        return [
            'id' => defined(static::class . '::PUBLISHER_ID')
                ? static::PUBLISHER_ID
                : config('drafts.column_names.publisher_morph_name', 'publisher') . '_id',
            'type' => defined(static::class . '::PUBLISHER_TYPE')
                ? static::PUBLISHER_TYPE
                : config('drafts.column_names.publisher_morph_name', 'publisher') . '_type',
        ];
    }

    /**
     * 检查是否为当前版本
     */
    public function isCurrent(): bool
    {
        return $this->{$this->getIsCurrentColumn()} ?? false;
    }

    /**
     * 查询当前版本
     */
    public function scopeCurrent($query): void
    {
        $query->where($this->getIsCurrentColumn(), true);
    }

    /**
     * 查询非当前版本
     */
    public function scopeWithoutCurrent($query): void
    {
        $query->where($this->getIsCurrentColumn(), false);
    }

    /*
    |--------------------------------------------------------------------------
    | 关联关系
    |--------------------------------------------------------------------------
    */

    /**
     * 排除指定的修订版本
     */
    public function scopeExcludeRevision($query, int|Model $exclude): void
    {
        $id = $exclude instanceof Model ? $exclude->getKey() : $exclude;
        $query->where($this->getKeyName(), '!=', $id);
    }

    /**
     * 获取草稿属性
     */
    public function getDraftAttribute()
    {
        if ($this->relationLoaded('drafts')) {
            return $this->drafts->first();
        }

        if ($this->relationLoaded('revisions')) {
            return $this->revisions
                ->firstWhere($this->getIsCurrentColumn(), true);
        }

        return $this->drafts()->first();
    }

    /**
     * 获取草稿的关联关系
     */
    public function drafts(): HasMany
    {
        return $this->revisions()->current()->onlyDrafts();
    }

    /*
    |--------------------------------------------------------------------------
    | 查询作用域
    |--------------------------------------------------------------------------
    */

    /**
     * 创建新的修订版本
     */
    protected function newRevision(): void
    {
        if (
            config('drafts.revisions.keep') < 1 ||
            !$this->shouldCreateRevision() ||
            $this->isDirty($this->getDeletedAtColumn() ?? 'deleted_at') ||
            $this->fireModelEvent('creatingRevision') === false
        ) {
            return;
        }

        $revision = $this->fresh()->replicate();

        static::saved(function (Model $model) use ($revision) {
            if ($model->isNot($this)) {
                return;
            }

            $revision->{$this->getCreatedAtColumn()} = $this->{$this->getCreatedAtColumn()};
            $revision->{$this->getUpdatedAtColumn()} = $this->{$this->getUpdatedAtColumn()};
            $revision->{$this->getIsCurrentColumn()} = false;
            $revision->{$this->getIsPublishedColumn()} = false;

            $revision->saveQuietly(['timestamps' => false]);

            $this->setPublisher();
            $this->pruneRevisions();

            $this->fireModelEvent('createdRevision');
        });
    }

    /**
     * 检查是否应创建修订版本
     */
    public function shouldCreateRevision(): bool
    {
        return $this->shouldCreateRevision;
    }

    /**
     * 设置发布者
     */
    public function setPublisher(): static
    {
        if (is_null($this->{$this->getPublisherColumns()['id']}) &&
            LaravelDrafts::getCurrentUser()
        ) {
            $this->publisher()->associate(LaravelDrafts::getCurrentUser());
        }

        return $this;
    }

    /*
    |--------------------------------------------------------------------------
    | 访问器
    |--------------------------------------------------------------------------
    */

    /**
     * 获取发布者的关联关系
     */
    public function publisher(): MorphTo
    {
        return $this->morphTo(config('drafts.column_names.publisher_morph_name'));
    }

    /*
    |--------------------------------------------------------------------------
    | 修改器
    |--------------------------------------------------------------------------
    */

    // 此处可根据需要添加修改器
}
