<!--
/**
 * 用途列表视图组件
 *
 * 功能特性：
 * - 用途数据表格展示
 * - 用途分类和应用场景展示
 * - 行选择和批量操作
 * - 状态切换
 * - 操作按钮组
 *
 * 版本：v1.0.0
 * 作者：Admin Team
 */
-->

<template>
    <div class="purpose-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            row-key="id"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 用途名称 -->
            <el-table-column label="用途名称" min-width="180">
                <template #default="{ row }">
                    <div class="purpose-name">
                        <span class="name">{{ row.name }}</span>
                        <span class="code">{{ row.code }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 类型分类 -->
            <el-table-column align="center" label="类型" width="120">
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        {{ row.type_label }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 应用领域 -->
            <el-table-column align="center" label="应用领域" width="120">
                <template #default="{ row }">
                    <el-tag
                        :type="getFieldTagType(row.field)"
                        effect="plain"
                        size="small"
                    >
                        {{ row.field_label }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 关键词 -->
            <el-table-column label="关键词" min-width="150" show-overflow-tooltip>
                <template #default="{ row }">
                    <div class="keywords">
						<span
                            v-for="keyword in row.keywords.split(', ')"
                            :key="keyword"
                            class="keyword-tag"
                        >
							{{ keyword }}
						</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 描述 -->
            <el-table-column label="描述" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.description }}
                </template>
            </el-table-column>

            <!-- 应用场景 -->
            <el-table-column label="应用场景" min-width="180" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.applications }}
                </template>
            </el-table-column>

            <!-- 使用次数 -->
            <el-table-column align="center" label="使用次数" width="100">
                <template #default="{ row }">
                    <span class="usage-count">{{ row.usage_count }}</span>
                </template>
            </el-table-column>

            <!-- 排序 -->
            <el-table-column align="center" label="排序" width="80">
                <template #default="{ row }">
                    {{ row.sort_order }}
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column align="center" label="状态" width="100">
                <template #default="{ row }">
                    <el-switch
                        :model-value="row.status === 'active'"
                        @change="handleStatusChange(row)"
                    />
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column align="center" label="创建时间" width="120">
                <template #default="{ row }">
                    {{ row.created_at }}
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column align="center" fixed="right" label="操作" width="180">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            link
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="warning"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="danger"
                            @click="handleDelete(row)"
                        >
                            <i class="fas fa-trash"></i>
                            删除
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'PurposeListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'status-change',
        'view-detail',
        'edit',
        'delete'
    ],
    methods: {
        // 获取类型标签类型
        getTypeTagType(type) {
            const typeMap = {
                daily: 'success',
                commercial: 'primary',
                industrial: 'warning',
                special: 'danger'
            }
            return typeMap[type] || 'info'
        },

        // 获取领域标签类型
        getFieldTagType(field) {
            const fieldMap = {
                clothing: 'primary',
                home: 'success',
                decoration: 'warning',
                other: 'info'
            }
            return fieldMap[field] || 'info'
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 处理状态变化
        handleStatusChange(row) {
            row.status = row.status === 'active' ? 'inactive' : 'active'
            this.$emit('status-change', row)
        },

        // 处理查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 处理编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 处理删除
        handleDelete(row) {
            this.$emit('delete', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.purpose-list-view {
    .purpose-name {
        .name {
            display: block;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
        }

        .code {
            display: block;
            font-size: 12px;
            color: #909399;
        }
    }

    .keywords {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .keyword-tag {
            display: inline-block;
            padding: 2px 6px;
            background-color: #f0f2f5;
            color: #606266;
            font-size: 12px;
            border-radius: 3px;
            border: 1px solid #e4e7ed;
        }
    }

    .usage-count {
        font-weight: 600;
        color: #409eff;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;

        .el-button {
            padding: 4px 8px;

            i {
                margin-right: 4px;
            }
        }
    }

    :deep(.el-table) {
        .el-table__row {
            &:hover {
                background-color: #f5f7fa;
            }
        }

        .el-table__cell {
            padding: 12px 0;
        }
    }
}
</style>
